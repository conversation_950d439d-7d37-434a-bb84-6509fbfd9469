using Discord;
using Discord.WebSocket;
using BotChan.Services;
using System.Text.RegularExpressions;

// This file contains the implementation of the Echo command, which allows users to:
// 1. Echo a message back to the current channel
// 2. Echo a message to a specific channel
//
// The command supports two syntaxes:
// - %echo [message] - Echoes the message in the current channel
// - %echo #channel [message] - Echoes the message in the specified channel

namespace BotChan.Commands
{
    /// <summary>
    /// EchoCommand - A command that echoes back your message, optionally to a specific channel
    /// Usage:
    /// - %echo [message] - Echoes the message in the current channel
    /// - %echo #channel [message] - Echoes the message in the specified channel
    /// Examples:
    /// - %echo Hello, world!
    /// - %echo #general Good morning everyone!
    /// </summary>
    public class EchoCommand : Command
    {
        // Override the default category
        public override string Category => "Utility";

        // Override the default usage
        public override string Usage => "%echo [message]\n%echo #channel [message]";

        // Override the default examples
        public override string Examples => "%echo Hello, world!\n%echo I'm a bot!\n%echo #general Good morning everyone!";

        // <PERSON><PERSON><PERSON>ctor calls the base Command constructor with:
        // - First parameter: command name (used to invoke the command)
        // - Second parameter: command description (shown in help)
        public EchoCommand() : base("echo", "Echoes back your message, optionally to a specific channel")
        {
            // Any initialization code would go here
            // Most simple commands don't need additional initialization
        }

        // This method is called when a user invokes the command
        // Parameters:
        // - message: The original Discord message that triggered the command
        // - args: Array of command arguments (args[0] is the command name)
        // - client: Reference to the bot client for accessing bot-wide functionality
        /// <summary>
        /// Executes the echo command logic.
        /// </summary>
        /// <param name="message">The message that triggered the command</param>
        /// <param name="args">Command arguments (args[0] is the command name)</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // STEP 1: Validate basic command usage
            // Check if there are any arguments after the command
            if (args.Length <= 1)
            {
                // If no arguments, send a usage example
                await message.Channel.SendMessageAsync("Please provide a message to echo. Examples:\n%echo Hello!\n%echo #channel Hello!");
                return;
            }

            // STEP 2: Validate message context
            // Cast to SocketUserMessage to access more properties
            if (message is not SocketUserMessage userMessage)
            {
                return; // Not a user message, can't process
            }

            // Get the guild (server) from the message
            var guild = (userMessage.Channel as SocketGuildChannel)?.Guild;
            if (guild == null)
            {
                // Command was used in a DM or group chat
                await message.Channel.SendMessageAsync("This command can only be used in a server.");
                return;
            }

            // STEP 3: Check for channel mention and determine target channel
            // By default, we'll echo to the current channel
            var channelMention = args[1];
            bool hasChannelMention = false;
            ISocketMessageChannel targetChannel = message.Channel;

            // Channel mentions in Discord are in the format <#channelId>
            // For example: <#123456789012345678>
            var channelMentionRegex = new Regex(@"<#(\d+)>");
            var match = channelMentionRegex.Match(channelMention);

            if (match.Success)
            {
                // We found a channel mention, extract the channel ID
                if (ulong.TryParse(match.Groups[1].Value, out ulong channelId))
                {
                    // Try to get the channel from the guild
                    var mentionedChannel = guild.GetTextChannel(channelId);
                    if (mentionedChannel != null)
                    {
                        // Channel found, set it as the target
                        targetChannel = mentionedChannel;
                        hasChannelMention = true;

                        // STEP 4: Check permissions
                        // Make sure the bot has permission to send messages in the target channel
                        var botUser = guild.GetUser(client._client.CurrentUser.Id);
                        var permissions = botUser.GetPermissions(mentionedChannel);

                        if (!permissions.SendMessages)
                        {
                            await message.Channel.SendMessageAsync($"I don't have permission to send messages in {mentionedChannel.Mention}.");
                            return;
                        }
                    }
                    else
                    {
                        // Channel not found or not a text channel
                        await message.Channel.SendMessageAsync($"I couldn't find the channel {channelMention} or it's not a text channel.");
                        return;
                    }
                }
            }

            // STEP 5: Prepare the message to echo
            string textToEcho;
            if (hasChannelMention)
            {
                // If we're using a channel mention, make sure there's a message after it
                if (args.Length <= 2)
                {
                    await message.Channel.SendMessageAsync("Please provide a message to echo after the channel mention.");
                    return;
                }
                // Skip the command name and channel mention, join the rest as the message
                textToEcho = string.Join(" ", args.Skip(2));
            }
            else
            {
                // No channel mention, so just skip the command name
                textToEcho = string.Join(" ", args.Skip(1));
            }

            // STEP 6: Send the message
            // Send the echoed message to the target channel
            await targetChannel.SendMessageAsync(textToEcho);

            // STEP 7: Provide feedback
            // If the target channel is different from the source channel, send a confirmation
            // This helps the user know their message was sent successfully
            if (hasChannelMention && targetChannel.Id != message.Channel.Id)
            {

            }
        }
    }
}
