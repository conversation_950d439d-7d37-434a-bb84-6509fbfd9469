using Discord.WebSocket;
using BotChan.Services;

namespace BotChan.Commands
{
    /// <summary>
    /// Base class for commands that support aliases
    /// </summary>
    public abstract class AliasCommand : Command
    {
        /// <summary>
        /// List of aliases for this command
        /// </summary>
        public string[] Aliases { get; }

        /// <summary>
        /// Creates a new command with the specified name, description, and aliases
        /// </summary>
        /// <param name="name">The primary name of the command</param>
        /// <param name="description">A short description of what the command does</param>
        /// <param name="aliases">Additional names that can be used to invoke this command</param>
        protected AliasCommand(string name, string description, params string[] aliases) : base(name, description)
        {
            Aliases = aliases;
        }
    }
}
