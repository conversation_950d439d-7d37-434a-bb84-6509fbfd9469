using Discord;
using Discord.WebSocket;
using BotChan.Services;

namespace BotChan.Commands
{
    /// <summary>
    /// PingCommand - A simple command that responds with "pong!"
    /// Usage: %ping
    /// Example: %ping
    /// </summary>
    public class PingCommand : Command
    {
        // Override the default category
        public override string Category => "Utility";

        // Override the default usage
        public override string Usage => "%ping";

        // Override the default examples
        public override string Examples => "%ping";

        public PingCommand() : base("ping", "Responds with pong!")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            try
            {
                // Calculate latency
                var latency = client._client.Latency;

                // Determine latency status and color
                Color embedColor;
                string latencyStatus;
                string latencyEmoji;

                if (latency < 100)
                {
                    embedColor = Color.Green;
                    latencyStatus = "Excellent";
                    latencyEmoji = "🟢";
                }
                else if (latency < 200)
                {
                    embedColor = Color.Orange;
                    latencyStatus = "Good";
                    latencyEmoji = "🟡";
                }
                else
                {
                    embedColor = Color.Red;
                    latencyStatus = "Poor";
                    latencyEmoji = "🔴";
                }

                // Create the embed response (same as slash command)
                var embed = new EmbedBuilder()
                    .WithTitle("🏓 Pong!")
                    .WithDescription($"Bot is online and responding!")
                    .WithColor(embedColor)
                    .AddField("📡 Latency", $"{latencyEmoji} {latency}ms ({latencyStatus})", true)
                    .AddField("⏰ Response Time", $"< 1 second", true)
                    .AddField("🤖 Status", "✅ Operational", true)
                    .WithTimestamp(DateTimeOffset.Now)
                    .WithFooter("💬 Everyone can see this - Use /silent to make it private");

                await message.Channel.SendMessageAsync(embed: embed.Build());
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing ping command: {ex.Message}");
                await message.Channel.SendMessageAsync("❌ An error occurred while processing the ping command.");
            }
        }
    }
}
