using Discord;
using Discord.WebSocket;
using BotChan.Services;
using System.Reflection;
using System.Text;

// This file contains the implementation of the Help command, which provides information about
// all available commands in the bot. The help command supports two modes:
// 1. General help: Lists all commands organized by category
// 2. Command-specific help: Shows detailed information about a specific command
//
// The help command automatically discovers all commands in the bot by using reflection
// to find classes that inherit from the Command base class.

namespace BotChan.Commands
{
    /// <summary>
    /// HelpCommand - Displays information about available commands
    /// Usage:
    /// - %help - Shows a list of all commands organized by category
    /// - %help [command] - Shows detailed help for a specific command
    ///
    /// This command automatically discovers all commands in the bot and organizes them by category.
    /// It also provides detailed help for specific commands, including usage instructions and examples.
    /// </summary>
    public class HelpCommand : Command
    {
        // Maximum number of commands to show per page
        private const int CommandsPerPage = 10;

        public HelpCommand() : base("help", "Displays information about available commands")
        {
        }

        /// <summary>
        /// Executes the help command logic.
        /// </summary>
        /// <param name="message">The message that triggered the command</param>
        /// <param name="args">Command arguments (args[0] is the command name)</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // The help command has two modes:
            // 1. General help: %help
            // 2. Command-specific help: %help [command]

            // If a specific command is requested (args.Length > 1), show detailed help for that command
            // For example, if the user types "%help echo", args would be ["help", "echo"]
            if (args.Length > 1)
            {
                await ShowCommandHelp(message, args[1], client);
                return;
            }

            // Otherwise, show the general help menu that lists all commands
            await ShowCommandList(message, client);
        }

        /// <summary>
        /// Shows a list of all available commands organized by category.
        /// This method uses reflection to find all commands and groups them by their category.
        /// </summary>
        /// <param name="message">The message that triggered the command</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        private async Task ShowCommandList(SocketMessage message, DiscordBotClient client)
        {
            // STEP 1: Find all command classes using reflection
            // Get all types in the current assembly that:
            // 1. Are classes (not interfaces or other types)
            // 2. Are not abstract
            // 3. Inherit from the Command base class
            var commandTypes = Assembly.GetExecutingAssembly().GetTypes()
                .Where(t => t.IsClass && !t.IsAbstract && t.IsSubclassOf(typeof(Command)))
                .OrderBy(t => t.Name) // Sort alphabetically by name
                .ToList();

            // STEP 2: Extract command information
            // Create a list to store command information (name, description, category)
            var commands = new List<(string Name, string Description, string Category)>();

            // Create an instance of each command to access its properties
            foreach (var type in commandTypes)
            {
                if (Activator.CreateInstance(type) is Command command)
                {
                    // Get the category from the command's Category property
                    // This allows commands to be organized into logical groups
                    commands.Add((command.Name, command.Description, command.Category));
                }
            }

            // STEP 3: Group commands by category
            // This organizes commands into logical groups for better readability
            var commandsByCategory = commands
                .GroupBy(c => c.Category) // Group by the Category property
                .OrderBy(g => g.Key == "General" ? 0 : 1) // Put General category first
                .ThenBy(g => g.Key) // Then sort other categories alphabetically
                .ToList();

            // STEP 4: Create a rich embed message
            // Discord embeds provide a nicer formatting for complex messages
            var embed = new EmbedBuilder()
                .WithTitle("📚 Bot-Chan Command List") // Title with emoji
                .WithDescription($"Use `{client.Config.Prefix}help [command]` for detailed information about a specific command.")
                .WithColor(Color.Blue) // Blue color theme
                .WithFooter(footer => footer.Text = $"Requested by {message.Author.Username} | {commands.Count} commands available")
                .WithCurrentTimestamp(); // Add current time

            // STEP 5: Add each category as a field in the embed
            foreach (var category in commandsByCategory)
            {
                // Create a string builder for the commands in this category
                var commandList = new StringBuilder();

                // Add each command in the category to the list
                foreach (var (name, description, _) in category.OrderBy(c => c.Name)) // Sort commands alphabetically
                {
                    // Format: **%command** - Description
                    commandList.AppendLine($"**{client.Config.Prefix}{name}** - {description}");
                }

                // Add the category as a field with emoji and count
                embed.AddField($"📂 {category.Key} ({category.Count()})", commandList.ToString());
            }

            // STEP 6: Send the embed to the channel
            await message.Channel.SendMessageAsync(embed: embed.Build());
        }

        /// <summary>
        /// Shows detailed help for a specific command, including usage instructions and examples.
        /// </summary>
        /// <param name="message">The message that triggered the command</param>
        /// <param name="commandName">The name of the command to show help for</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        private async Task ShowCommandHelp(SocketMessage message, string commandName, DiscordBotClient client)
        {
            // STEP 1: Find the command by name using reflection
            // Look for a class that:
            // 1. Is a class (not interface or other type)
            // 2. Is not abstract
            // 3. Inherits from Command
            // 4. Has a Name property that matches the requested command name (case-insensitive)
            var commandType = Assembly.GetExecutingAssembly().GetTypes()
                .FirstOrDefault(t => t.IsClass && !t.IsAbstract && t.IsSubclassOf(typeof(Command)) &&
                                    ((Activator.CreateInstance(t) as Command)?.Name.Equals(commandName, StringComparison.OrdinalIgnoreCase) ?? false));

            // STEP 2: Handle command not found
            if (commandType == null)
            {
                await message.Channel.SendMessageAsync($"Command `{commandName}` not found. Use `{client.Config.Prefix}help` to see all available commands.");
                return;
            }

            // STEP 3: Create an instance of the command to access its properties
            if (Activator.CreateInstance(commandType) is Command command)
            {
                // STEP 4: Get command information
                // Get usage and examples from the command properties
                // Replace any default prefix with the configured prefix
                string usage = command.Usage.Replace("!", client.Config.Prefix);
                string examples = command.Examples.Replace("!", client.Config.Prefix);

                // STEP 5: Create a rich embed with command details
                var embed = new EmbedBuilder()
                    .WithTitle($"Command Help: {client.Config.Prefix}{command.Name}") // Title with command name
                    .WithDescription(command.Description) // Command description
                    .WithColor(Color.Green) // Green color theme for command help
                    .AddField("Category", command.Category) // Command category
                    .AddField("Usage", usage) // Usage instructions
                    .AddField("Examples", examples) // Usage examples
                    .WithFooter(footer => footer.Text = $"Requested by {message.Author.Username}") // Footer with requester name
                    .WithCurrentTimestamp() // Current timestamp
                    .Build();

                // STEP 6: Send the embed to the channel
                await message.Channel.SendMessageAsync(embed: embed);
            }
        }


    }
}
