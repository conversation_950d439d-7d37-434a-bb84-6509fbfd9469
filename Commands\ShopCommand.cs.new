using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;
using System.Text;

namespace BotChan.Commands
{
    /// <summary>
    /// ShopCommand - A command that shows the shop for purchasing consumables
    /// Usage: %shop
    /// Example: %shop
    /// </summary>
    public class ShopCommand : AliasCommand
    {
        // Number of items to display per page
        private const int ItemsPerPage = 7;
        
        // Pagination reaction emojis
        private readonly Emoji _leftArrow = new Emoji("⬅️");
        private readonly Emoji _rightArrow = new Emoji("➡️");
        
        // Dictionary to track active shop menus and their pages
        private static readonly Dictionary<ulong, (IUserMessage Message, int CurrentPage, DateTime Expiry)> _activeMenus = new Dictionary<ulong, (IUserMessage, int, DateTime)>();
        
        // Override the default category
        public override string Category => "Bots";

        // Override the default usage
        public override string Usage => "%shop\n%shop buy [id]\n%shop sell [id]";

        // Override the default examples
        public override string Examples => "%shop\n%shop buy 1\n%shop sell 2";

        public ShopCommand() : base("shop", "Browse and purchase consumables with your bots")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Get the user's data
            var userData = client.BotSystem.GetUserData(message.Author.Id);

            // Check if the user wants to buy something
            if (args.Length > 1 && args[1].ToLower() == "buy")
            {
                if (args.Length < 3)
                {
                    await message.Channel.SendMessageAsync("Please specify a consumable ID to buy. Example: `%shop buy 1`");
                    return;
                }

                // Try to parse the ID as an integer
                if (!int.TryParse(args[2], out int itemId))
                {
                    await message.Channel.SendMessageAsync("Invalid ID. Please provide a numeric ID. Example: `%shop buy 1`");
                    return;
                }

                await BuyConsumable(message, itemId, client);
                return;
            }
            
            // Check if the user wants to sell something
            if (args.Length > 1 && args[1].ToLower() == "sell")
            {
                if (args.Length < 3)
                {
                    await message.Channel.SendMessageAsync("Please specify an inventory ID to sell. Example: `%shop sell 1`");
                    return;
                }

                // Try to parse the ID as an integer
                if (!int.TryParse(args[2], out int inventoryId))
                {
                    await message.Channel.SendMessageAsync("Invalid ID. Please provide a numeric ID. Example: `%shop sell 1`");
                    return;
                }

                await SellConsumable(message, inventoryId, client);
                return;
            }

            // Otherwise, show the shop
            await ShowShop(message, 1, client);
        }

        private async Task ShowShop(SocketMessage message, int page, DiscordBotClient client)
        {
            // Get the user's data
            var userData = client.BotSystem.GetUserData(message.Author.Id);

            // Get all consumables
            var allConsumables = client.BotSystem.Config.AvailableConsumables;
            
            // Calculate total pages
            int totalPages = (int)Math.Ceiling(allConsumables.Count / (double)ItemsPerPage);
            if (totalPages == 0) totalPages = 1; // At least one page even if empty
            
            // Ensure page is within valid range
            page = Math.Max(1, Math.Min(page, totalPages));
            
            // Get consumables for the current page
            var pageConsumables = allConsumables
                .Skip((page - 1) * ItemsPerPage)
                .Take(ItemsPerPage)
                .ToList();
            
            // Build the shop message
            var sb = new StringBuilder();
            
            // Add the header with player profile picture
            var user = message.Author as SocketUser;
            string userAvatarUrl = user?.GetAvatarUrl() ?? user?.GetDefaultAvatarUrl();
            
            sb.AppendLine($"**Consumables**");
            sb.AppendLine("Purchase a consumable for more bots!");
            sb.AppendLine("The better the consumable the more bots you acquire!");
            sb.AppendLine("%shop buy {id} to buy an item");
            sb.AppendLine("%shop sell {id} to sell an item for 50% of its original price");
            sb.AppendLine("════════════════════════════════");
            
            // Add the consumables
            for (int i = 0; i < pageConsumables.Count; i++)
            {
                var consumable = pageConsumables[i];
                int displayId = (page - 1) * ItemsPerPage + i + 1; // Calculate the display ID
                string formattedPrice = FormatNumber(consumable.Cost);
                
                // Format: ID :emoji: name----------------------- price :BotDown:
                // Calculate dashes needed for alignment
                int dashCount = Math.Max(1, 40 - consumable.Name.Length - formattedPrice.Length);
                string dashes = new string('-', dashCount);
                
                sb.AppendLine($"{displayId} {consumable.Emoji} {consumable.Name}{dashes} {formattedPrice} <:BotDown:1363239519914164264>");
            }
            
            // Add the page indicator
            sb.AppendLine($"Page {page}/{totalPages}");
            
            // Send the message
            var shopMessage = await message.Channel.SendMessageAsync(sb.ToString());
            
            // Add reactions for pagination if there are multiple pages
            if (totalPages > 1)
            {
                await shopMessage.AddReactionAsync(_leftArrow);
                await shopMessage.AddReactionAsync(_rightArrow);
                
                // Store the menu in the active menus dictionary
                _activeMenus[message.Author.Id] = (shopMessage, page, DateTime.UtcNow.AddMinutes(5));
                
                // Set up the reaction handler
                client._client.ReactionAdded += async (cachedMessage, channel, reaction) =>
                {
                    // Check if this is a reaction to our shop message
                    if (reaction.MessageId != shopMessage.Id || reaction.UserId != message.Author.Id)
                        return;
                    
                    // Check if the menu is still active
                    if (!_activeMenus.TryGetValue(message.Author.Id, out var menuInfo) || 
                        menuInfo.Message.Id != shopMessage.Id || 
                        DateTime.UtcNow > menuInfo.Expiry)
                        return;
                    
                    // Get the current page
                    int currentPage = menuInfo.CurrentPage;
                    
                    // Handle the reaction
                    if (reaction.Emote.Name == _leftArrow.Name && currentPage > 1)
                    {
                        // Remove the user's reaction
                        await shopMessage.RemoveReactionAsync(_leftArrow, reaction.UserId);
                        
                        // Show the previous page
                        await ShowShop(message, currentPage - 1, client);
                        
                        // Delete the old message
                        await shopMessage.DeleteAsync();
                    }
                    else if (reaction.Emote.Name == _rightArrow.Name && currentPage < totalPages)
                    {
                        // Remove the user's reaction
                        await shopMessage.RemoveReactionAsync(_rightArrow, reaction.UserId);
                        
                        // Show the next page
                        await ShowShop(message, currentPage + 1, client);
                        
                        // Delete the old message
                        await shopMessage.DeleteAsync();
                    }
                };
            }
        }

        private async Task BuyConsumable(SocketMessage message, int displayId, DiscordBotClient client)
        {
            // Get all consumables
            var allConsumables = client.BotSystem.Config.AvailableConsumables;
            
            // Check if the ID is valid
            if (displayId <= 0 || displayId > allConsumables.Count)
            {
                await message.Channel.SendMessageAsync($"Error: Invalid consumable ID. Please use a number between 1 and {allConsumables.Count}.");
                return;
            }
            
            // Get the consumable by its display ID (1-based index)
            var consumable = allConsumables[displayId - 1];
            
            // Get the user's data
            var userData = client.BotSystem.GetUserData(message.Author.Id);

            // Check if the user has enough bots
            if (userData.BotCount < consumable.Cost)
            {
                await message.Channel.SendMessageAsync($"You don't have enough bots to purchase **{consumable.Name}**. You need **{consumable.Cost}** bots, but you only have **{userData.BotCount}**.");
                return;
            }

            // Purchase the consumable
            bool success = client.BotSystem.PurchaseConsumable(message.Author.Id, consumable.Id);

            if (success)
            {
                // Create a message for the response
                var sb = new StringBuilder();
                
                sb.AppendLine($"**Purchase Successful!**");
                sb.AppendLine($"You've purchased **{consumable.Name}** {consumable.Emoji} for **{consumable.Cost}** <:BotDown:1363239519914164264>!");
                sb.AppendLine();
                sb.AppendLine($"• This consumable gives you a **{consumable.Multiplier}x** multiplier for **{consumable.DefaultUses}** uses.");
                sb.AppendLine($"• Use it with the `%use [inventory_id]` command.");
                sb.AppendLine();
                sb.AppendLine($"**Remaining Bots:** {userData.BotCount}");
                
                // Send the response
                await message.Channel.SendMessageAsync(sb.ToString());
            }
            else
            {
                await message.Channel.SendMessageAsync("Error: Failed to purchase the consumable.");
            }
        }
        
        private async Task SellConsumable(SocketMessage message, int inventoryId, DiscordBotClient client)
        {
            // Get the user's data
            var userData = client.BotSystem.GetUserData(message.Author.Id);
            
            // Check if the user has the item
            var userConsumable = userData.GetConsumableByInventoryId(inventoryId);
            if (userConsumable == null)
            {
                await message.Channel.SendMessageAsync($"You don't have an item with ID {inventoryId} in your inventory. Use `%inventory` to see your items.");
                return;
            }
            
            // Get the consumable details
            var consumable = client.BotSystem.GetConsumable(userConsumable.ConsumableId);
            if (consumable == null)
            {
                await message.Channel.SendMessageAsync("Error: Consumable not found in the system.");
                return;
            }
            
            // Calculate the sell price (50% of original)
            int sellPrice = (int)(consumable.Cost * 0.5);
            
            // Remove the item from inventory
            userData.Inventory.Remove(userConsumable);
            
            // Add the bots to the user's account
            userData.BotCount += sellPrice;
            
            // Save the user data
            client.BotSystem.SaveUserData();
            
            // Send a confirmation message
            var sb = new StringBuilder();
            sb.AppendLine($"**Item Sold!**");
            sb.AppendLine($"You sold **{consumable.Name}** {consumable.Emoji} for **{sellPrice}** <:BotDown:1363239519914164264>.");
            sb.AppendLine();
            sb.AppendLine($"**New Bot Balance:** {userData.BotCount}");
            
            await message.Channel.SendMessageAsync(sb.ToString());
        }
        
        /// <summary>
        /// Formats a number with K, M, B suffixes for thousands, millions, billions
        /// </summary>
        private string FormatNumber(int number)
        {
            if (number >= 1_000_000_000)
                return $"{number / 1_000_000_000}B";
            if (number >= 1_000_000)
                return $"{number / 1_000_000}M";
            if (number >= 1_000)
                return $"{number / 1_000}K";
            return number.ToString();
        }
    }
}
