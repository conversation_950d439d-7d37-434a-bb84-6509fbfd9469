namespace BotChan.Models
{
    /// <summary>
    /// Configuration for the bot system.
    /// </summary>
    public class BotSystemConfig
    {
        /// <summary>
        /// The base number of bots awarded for using the recruit command.
        /// </summary>
        public int BaseRecruitReward { get; set; } = 1;

        /// <summary>
        /// The cooldown time for the recruit command in seconds.
        /// </summary>
        public int RecruitCooldown { get; set; } = 60;

        /// <summary>
        /// The list of available consumables.
        /// </summary>
        public List<Consumable> AvailableConsumables { get; set; } = new List<Consumable>();

        /// <summary>
        /// The list of available redemption codes.
        /// </summary>
        public List<RedemptionCode> RedemptionCodes { get; set; } = new List<RedemptionCode>();

        /// <summary>
        /// Whether the bot is currently in lockdown mode (emergency mode).
        /// When in lockdown, only staff members can use commands.
        /// </summary>
        public bool IsLockdownActive { get; set; } = false;

        /// <summary>
        /// The reason for the current lockdown, if any.
        /// </summary>
        public string LockdownReason { get; set; } = string.Empty;

        /// <summary>
        /// The user ID of the person who activated the lockdown.
        /// </summary>
        public ulong LockdownActivatedBy { get; set; } = 0;

        /// <summary>
        /// When the lockdown was activated.
        /// </summary>
        public DateTime LockdownActivatedAt { get; set; } = DateTime.MinValue;
    }
}
