using Discord;
using Discord.WebSocket;
using BotChan.Services;
using BotChan.Models;

namespace BotChan.Commands.SlashCommands
{
    /// <summary>
    /// BombSlashCommand - Slash command version of the bomb command
    /// Usage: /bomb target item_id
    /// Allows users to attack other players using bomb items
    /// </summary>
    public class BombSlashCommand
    {
        // Random number generator for bomb outcomes
        private static readonly Random _random = new Random();

        /// <summary>
        /// Executes the bomb slash command.
        /// </summary>
        /// <param name="command">The slash command that was executed</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task ExecuteAsync(SocketSlashCommand command, DiscordBotClient client)
        {
            try
            {
                // Check cooldown before allowing the command (same as %bomb)
                if (!client.Permissions.HasPermission(command.User.Id, "bomb", out string cooldownMessage))
                {
                    if (!string.IsNullOrEmpty(cooldownMessage))
                    {
                        await command.RespondAsync($"⏰ **Bomb command is on cooldown!** {cooldownMessage}", ephemeral: true);
                    }
                    else
                    {
                        await command.RespondAsync("❌ You don't have permission to use this command.", ephemeral: true);
                    }
                    return;
                }

                // Update last usage time for cooldown tracking
                client.Permissions.UpdateCommandUsage(command.User.Id, "bomb");

                // Get command options
                var targetOption = command.Data.Options.FirstOrDefault(x => x.Name == "target");
                var itemIdOption = command.Data.Options.FirstOrDefault(x => x.Name == "item_id");

                if (targetOption == null || itemIdOption == null)
                {
                    await command.RespondAsync($"**<:BotChanDespair:1410316642180796446> | {command.User.Username}**, please provide a target user ID and inventory item ID.", ephemeral: true);
                    return;
                }

                string target = targetOption.Value.ToString();
                string itemIdStr = itemIdOption.Value.ToString();

                // Get the user's data
                var userData = client.BotSystem.GetUserData(command.User.Id, command.User.Username);

                // Parse the inventory item ID
                if (!int.TryParse(itemIdStr, out int inventoryId))
                {
                    await command.RespondAsync($"**<:BotChanDespair:1410316642180796446> | {command.User.Username}**, please provide a valid inventory item ID number.", ephemeral: true);
                    return;
                }

                // Get the bomb item from user's inventory
                var bombItem = userData.GetConsumableByInventoryId(inventoryId);
                if (bombItem == null)
                {
                    await command.RespondAsync($"**<:BotChanDespair:1410316642180796446> | {command.User.Username}**, you don't have an item with inventory ID {inventoryId}. Use `/inventory` to see your items.", ephemeral: true);
                    return;
                }

                // Get the bomb consumable details
                var bombConsumable = client.BotSystem.GetConsumable(bombItem.ConsumableId);
                if (bombConsumable == null || bombConsumable.Category != 3)
                {
                    await command.RespondAsync($"**<:BotChanDespair:1410316642180796446> | {command.User.Username}**, the item with inventory ID {inventoryId} is not a bomb item. Only bomb items can be used with this command.", ephemeral: true);
                    return;
                }

                // Parse the target user ID
                if (!ulong.TryParse(target, out ulong targetUserId))
                {
                    await command.RespondAsync($"**<:BotChanDespair:1410316642180796446> | {command.User.Username}**, please provide a valid user ID.", ephemeral: true);
                    return;
                }

                // Check if this is a gift (negative BotReward) or an attack
                if (bombConsumable.BotReward < 0)
                {
                    // This is a gift - allow targeting yourself and don't require minimum bots
                    await ExecuteBombGift(command, client, userData, targetUserId, bombItem, bombConsumable);
                    return;
                }

                // Get the target user's data
                var targetUserData = client.BotSystem.GetUserData(targetUserId);

                // Check if bombing yourself or others
                bool isSelfBombing = targetUserId == command.User.Id;

                if (!isSelfBombing && targetUserData.BotCount < 100)
                {
                    await command.RespondAsync($"**<:BotChanDespair:1410316642180796446> | {command.User.Username}**, that user doesn't have enough bots to bomb (minimum 100 required).", ephemeral: true);
                    return;
                }

                // Execute the bomb attack (self-bombing has 100% success rate)
                await ExecuteBombAttack(command, client, userData, targetUserData, bombItem, bombConsumable, targetUserId, isSelfBombing);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing bomb slash command: {ex.Message}");
                
                if (!command.HasResponded)
                {
                    await command.RespondAsync("❌ An error occurred while processing the bomb command.", ephemeral: true);
                }
            }
        }

        /// <summary>
        /// Executes a bot gift using a negative BotReward bomb item
        /// </summary>
        private static async Task ExecuteBombGift(SocketSlashCommand command, DiscordBotClient client, UserData giverData, ulong targetUserId, UserConsumable giftItem, Consumable giftConsumable)
        {
            // Remove the gift item from giver's inventory (it's consumed)
            giverData.Inventory.Remove(giftItem);

            // Calculate the number of bots to give (negative BotReward becomes positive gift)
            long botsToGive = Math.Abs(giftConsumable.BotReward);

            // Gift bombs don't require additional bots from the giver - the bots are already "paid for"
            // when purchasing the gift bomb from the shop. Simply give the bots to the target.
            client.BotSystem.AddBots(targetUserId, botsToGive);

            // Get updated data
            giverData = client.BotSystem.GetUserData(command.User.Id);
            var targetData = client.BotSystem.GetUserData(targetUserId);

            // Get target user for display name
            var targetUser = client._client.GetUser(targetUserId);
            string targetName = targetUser?.Username ?? targetData.GetDisplayName();

            // Check if giving to self
            bool givingToSelf = targetUserId == command.User.Id;

            // Create gift embed
            var giftEmbed = new EmbedBuilder()
                .WithTitle($"🎁 Bot Gift Delivered!")
                .WithDescription(givingToSelf ?
                    $"**{command.User.Username}** gave themselves **{botsToGive:N0}** bots!" :
                    $"**{command.User.Username}** gave **{targetName}** **{botsToGive:N0}** bots!")
                .WithColor(Color.Green)
                .AddField("Gift Item Used", $"{giftConsumable.Name} {giftConsumable.Emoji}", true)
                .AddField("Bots Transferred", $"{botsToGive:N0} {Constants.Emojis.BotDown}", true)
                .AddField(givingToSelf ? "Your Bot Count" : $"{targetName}'s New Bot Count", $"{targetData.BotCount:N0} {Constants.Emojis.BotDown}", true)
                .WithThumbnailUrl("https://cdn.discordapp.com/attachments/1363245551184908468/1363320873498447923/Bot_Army.png")
                .WithTimestamp(DateTimeOffset.Now);

            await command.RespondAsync(embed: giftEmbed.Build());
        }

        /// <summary>
        /// Executes the bomb attack with success/failure mechanics
        /// </summary>
        private static async Task ExecuteBombAttack(SocketSlashCommand command, DiscordBotClient client, UserData attackerData, UserData targetData, UserConsumable bombItem, Consumable bombConsumable, ulong targetUserId, bool isSelfBombing = false)
        {
            // Remove the bomb item from attacker's inventory (it's consumed regardless of outcome)
            attackerData.Inventory.Remove(bombItem);

            // Determine if the bomb attack succeeds (self-bombing always succeeds)
            bool bombSucceeds;
            if (isSelfBombing)
            {
                bombSucceeds = true; // 100% success rate for self-bombing
            }
            else
            {
                int successRoll = _random.Next(1, 101); // 1-100
                bombSucceeds = successRoll <= bombConsumable.BombSuccessRate;
            }

            // Get target user for display name
            var targetUser = client._client.GetUser(targetUserId);
            string targetName = targetUser?.Username ?? targetData.GetDisplayName();

            if (bombSucceeds)
            {
                // Calculate damage to target using fixed win percentage
                long botsDestroyed = (long)Math.Ceiling(targetData.BotCount * (bombConsumable.BombWinPercentage / 100.0));
                botsDestroyed = Math.Max(1, Math.Min(botsDestroyed, targetData.BotCount));

                // Apply damage to target
                client.BotSystem.RemoveBots(targetUserId, botsDestroyed);

                // Get updated target data
                targetData = client.BotSystem.GetUserData(targetUserId);

                // Create success embed (exact same as %bomb)
                var successEmbed = new EmbedBuilder()
                    .WithTitle($"💥 Bomb Attack Successful!")
                    .WithDescription(isSelfBombing ?
                        $"**{command.User.Username}** bombed their own territory for bot storage!" :
                        $"**{command.User.Username}** successfully bombed **{targetName}**'s territory!")
                    .WithColor(Color.Orange)
                    .AddField("Bomb Used", $"{bombConsumable.Name} {bombConsumable.Emoji}", true)
                    .AddField("Success Rate", isSelfBombing ? "100% (Self-bombing)" : $"{bombConsumable.BombSuccessRate}%", true)
                    .AddField("Damage Dealt", $"{bombConsumable.BombWinPercentage}% ({botsDestroyed:N0} bots destroyed)", true)
                    .AddField("Target's Remaining Bots", $"{targetData.BotCount:N0} {Constants.Emojis.BotDown}", true)
                    .WithThumbnailUrl("https://cdn.discordapp.com/attachments/1363245551184908468/1363320873498447923/Bot_Army.png")
                    .WithTimestamp(DateTimeOffset.Now);

                await command.RespondAsync(embed: successEmbed.Build());
            }
            else
            {
                // Bomb failed - apply penalty to attacker using fixed lose percentage
                long penaltyBots = (long)Math.Ceiling(attackerData.BotCount * (bombConsumable.BombLosePercentage / 100.0));
                penaltyBots = Math.Max(1, Math.Min(penaltyBots, attackerData.BotCount));

                if (attackerData.BotCount >= penaltyBots)
                {
                    client.BotSystem.RemoveBots(command.User.Id, penaltyBots);
                }

                // Get updated attacker data
                attackerData = client.BotSystem.GetUserData(command.User.Id);

                // Create failure embed (exact same as %bomb)
                var failureEmbed = new EmbedBuilder()
                    .WithTitle($"💨 Bomb Attack Failed!")
                    .WithDescription($"**{command.User.Username}**'s bomb attack on **{targetName}** was unsuccessful!")
                    .WithColor(Color.Red)
                    .AddField("Bomb Used", $"{bombConsumable.Name} {bombConsumable.Emoji}", true)
                    .AddField("Success Rate", $"{bombConsumable.BombSuccessRate}%", true)
                    .AddField("Penalty Applied", $"{bombConsumable.BombLosePercentage}% ({penaltyBots:N0} bots lost)", true)
                    .AddField("Your Remaining Bots", $"{attackerData.BotCount:N0} {Constants.Emojis.BotDown}", true)
                    .WithThumbnailUrl("https://cdn.discordapp.com/attachments/1363245551184908468/1363320873498447923/Bot_Army.png")
                    .WithTimestamp(DateTimeOffset.Now);

                await command.RespondAsync(embed: failureEmbed.Build());
            }

            // Save all changes
            client.BotSystem.SaveUserData();
        }
    }
}
