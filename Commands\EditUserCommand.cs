using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;
using System.Text;

namespace BotChan.Commands
{
    /// <summary>
    /// EditUserCommand - Allows the Creator to edit user data (bots, items, etc.)
    /// Usage: %edituser
    /// Example: %edituser
    /// </summary>
    public class EditUserCommand : Command
    {
        // Override the default category
        public override string Category => "Admin";

        // Override the default usage
        public override string Usage => "%edituser";

        // Override the default examples
        public override string Examples => "%edituser";

        // Dictionary to track active edit menus
        private static readonly Dictionary<ulong, EditUserMenuState> _activeMenus = new();

        // Dictionary to track message handlers for each user
        private static readonly Dictionary<ulong, Func<SocketMessage, Task>> _messageHandlers = new();

        public EditUserCommand() : base("edituser", "Edit user data (Creator only)")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Only the creator can use this command
            bool isCreator = message.Author.Id == 513793435807907841;

            if (!isCreator)
            {
                await message.Channel.SendMessageAsync("You don't have permission to use this command.");
                return;
            }

            // If the user already has an active menu, close it first
            if (_activeMenus.ContainsKey(message.Author.Id))
            {
                await CloseMenu(message.Author.Id, client);
                await message.Channel.SendMessageAsync("Previous menu closed.");
            }

            // Create a new menu state for this user
            var menuState = new EditUserMenuState();
            _activeMenus[message.Author.Id] = menuState;

            // Show the main menu
            await ShowMainMenu(message, client);

            // Create a message handler for this menu
            Func<SocketMessage, Task> handler = async (msg) =>
            {
                // Only process messages from the original user
                if (msg.Author.Id != message.Author.Id || msg.Author.IsBot)
                    return;

                // Check if the user has an active menu
                if (!_activeMenus.TryGetValue(msg.Author.Id, out var state))
                    return;

                // Check for menu timeout (2 minutes)
                if (DateTime.UtcNow - state.LastActivity > TimeSpan.FromMinutes(2))
                {
                    await CloseMenu(msg.Author.Id, client);
                    await msg.Channel.SendMessageAsync("Menu closed due to inactivity.");
                    return;
                }

                // Process the menu input
                await ProcessMenuInput(msg, state, client);
            };

            // Store the handler so we can remove it later
            _messageHandlers[message.Author.Id] = handler;

            // Register the handler
            client._client.MessageReceived += handler;
        }

        /// <summary>
        /// Closes the menu for a user and cleans up resources.
        /// </summary>
        private async Task CloseMenu(ulong userId, DiscordBotClient client)
        {
            // Remove the menu state
            _activeMenus.Remove(userId);

            // Get and remove the message handler
            if (_messageHandlers.TryGetValue(userId, out var handler))
            {
                // Unregister the handler
                client._client.MessageReceived -= handler;
                _messageHandlers.Remove(userId);
            }
        }

        /// <summary>
        /// Shows the main menu for the edit user command.
        /// </summary>
        private async Task ShowMainMenu(SocketMessage message, DiscordBotClient client)
        {
            var embed = new EmbedBuilder()
                .WithTitle("Edit User Menu")
                .WithDescription("Select an option:")
                .AddField("1️⃣ Find User", "Search for a user to edit")
                .AddField("2️⃣ List Users", "List all users with bot data")
                .AddField("exit", "Close this menu")
                .WithColor(Color.Blue)
                .WithFooter("Type the number of your choice");

            await message.Channel.SendMessageAsync(embed: embed.Build());

            // Update last activity time
            if (_activeMenus.TryGetValue(message.Author.Id, out var state))
            {
                state.LastActivity = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Processes menu input based on the current menu state.
        /// </summary>
        private async Task ProcessMenuInput(SocketMessage message, EditUserMenuState state, DiscordBotClient client)
        {
            // Update last activity time
            state.LastActivity = DateTime.UtcNow;

            // Check for exit command
            if (message.Content.Equals("exit", StringComparison.OrdinalIgnoreCase))
            {
                await CloseMenu(message.Author.Id, client);
                await message.Channel.SendMessageAsync("User edit menu closed.");
                return;
            }

            // Check for back command
            if (message.Content.Equals("back", StringComparison.OrdinalIgnoreCase))
            {
                // Reset the state
                state.CurrentMenu = EditUserMenu.Main;
                state.SelectedUserId = 0;

                // Show the main menu
                await ShowMainMenu(message, client);
                return;
            }

            // Process input based on current menu
            switch (state.CurrentMenu)
            {
                case EditUserMenu.Main:
                    await ProcessMainMenuInput(message, state, client);
                    break;

                case EditUserMenu.FindUser:
                    await ProcessFindUserInput(message, state, client);
                    break;

                case EditUserMenu.ListUsers:
                    await ProcessListUsersInput(message, state, client);
                    break;

                case EditUserMenu.SelectFoundUser:
                    await ProcessSelectFoundUserInput(message, state, client);
                    break;

                case EditUserMenu.EditUser:
                    await ProcessEditUserInput(message, state, client);
                    break;

                case EditUserMenu.EditBots:
                    await ProcessEditBotsInput(message, state, client);
                    break;

                case EditUserMenu.EditItems:
                    await ProcessEditItemsInput(message, state, client);
                    break;
            }
        }

        /// <summary>
        /// Processes input for the main menu.
        /// </summary>
        private async Task ProcessMainMenuInput(SocketMessage message, EditUserMenuState state, DiscordBotClient client)
        {
            switch (message.Content)
            {
                case "1":
                    // Find user
                    state.CurrentMenu = EditUserMenu.FindUser;
                    await message.Channel.SendMessageAsync("Enter the username or ID of the user you want to find:");
                    break;

                case "2":
                    // List users
                    state.CurrentMenu = EditUserMenu.ListUsers;
                    state.CurrentPage = 1;
                    await ShowUserList(message, state, client);
                    break;

                default:
                    await message.Channel.SendMessageAsync("Invalid option. Please select a number from 1-2, or type 'exit' to close the menu.");
                    break;
            }
        }

        /// <summary>
        /// Processes input for selecting a user from the found users list.
        /// </summary>
        private async Task ProcessSelectFoundUserInput(SocketMessage message, EditUserMenuState state, DiscordBotClient client)
        {
            // Check if the input is a number
            if (int.TryParse(message.Content, out int selection))
            {
                // Check if the selection is valid
                if (selection >= 1)
                {
                    // Check if we're selecting from database users or Discord users
                    if (state.FoundUserIds.Count > 0 && selection <= state.FoundUserIds.Count)
                    {
                        // Select from database users
                        state.SelectedUserId = state.FoundUserIds[selection - 1];
                        state.CurrentMenu = EditUserMenu.EditUser;
                        await ShowUserEditMenu(message, state, client);
                        return;
                    }
                    else if (state.FoundUsers.Count > 0 && selection <= state.FoundUsers.Count)
                    {
                        // Select from Discord users
                        var selectedUser = state.FoundUsers[selection - 1];
                        state.SelectedUserId = selectedUser.Id;
                        state.CurrentMenu = EditUserMenu.EditUser;

                        // Update the username in the database
                        client.BotSystem.GetUserData(selectedUser.Id, selectedUser.Username);

                        await ShowUserEditMenu(message, state, client);
                        return;
                    }
                }

                await message.Channel.SendMessageAsync("Invalid selection. Please try again.");
            }
            else
            {
                await message.Channel.SendMessageAsync("Invalid input. Please enter a number or type 'back' to return to the main menu.");
            }
        }

        /// <summary>
        /// Processes input for the find user menu.
        /// </summary>
        private async Task ProcessFindUserInput(SocketMessage message, EditUserMenuState state, DiscordBotClient client)
        {
            // Try to parse as user ID
            if (ulong.TryParse(message.Content, out ulong userId))
            {
                // First check if the user exists in our database
                var allUsers = client.BotSystem.GetAllUserData();
                var userData = allUsers.FirstOrDefault(u => u.UserId == userId);

                if (userData != null)
                {
                    // User found in database
                    state.SelectedUserId = userId;
                    state.CurrentMenu = EditUserMenu.EditUser;

                    // Try to update username if user is in Discord
                    var discordUser = client._client.GetUser(userId);
                    if (discordUser != null && userData.Username == "Unknown User")
                    {
                        client.BotSystem.GetUserData(userId, discordUser.Username);
                    }

                    await ShowUserEditMenu(message, state, client);
                    return;
                }

                // If not in database, check if they're in Discord
                var user = client._client.GetUser(userId);
                if (user != null)
                {
                    // User found in Discord
                    state.SelectedUserId = userId;
                    state.CurrentMenu = EditUserMenu.EditUser;

                    // Create user data with their username
                    client.BotSystem.GetUserData(userId, user.Username);

                    await ShowUserEditMenu(message, state, client);
                    return;
                }

                // User not found anywhere
                await message.Channel.SendMessageAsync("No user found with that ID. Please try again or type 'back' to return to the main menu.");
                return;
            }

            // Try to find by username in database first
            var allUserData = client.BotSystem.GetAllUserData();
            var matchingUsers = allUserData
                .Where(u => u.Username.Contains(message.Content, StringComparison.OrdinalIgnoreCase))
                .ToList();

            if (matchingUsers.Count > 0)
            {
                if (matchingUsers.Count == 1)
                {
                    // Only one user found in database
                    state.SelectedUserId = matchingUsers[0].UserId;
                    state.CurrentMenu = EditUserMenu.EditUser;
                    await ShowUserEditMenu(message, state, client);
                    return;
                }

                // Multiple users found in database, show a list
                var dbUsersEmbed = new EmbedBuilder()
                    .WithTitle("Multiple Users Found")
                    .WithDescription("Please select a user by typing their number:")
                    .WithColor(Color.Blue);

                for (int i = 0; i < Math.Min(matchingUsers.Count, 10); i++)
                {
                    dbUsersEmbed.AddField($"{i + 1}. {matchingUsers[i].Username}", $"ID: {matchingUsers[i].UserId}");
                }

                dbUsersEmbed.AddField("back", "Return to the main menu");

                await message.Channel.SendMessageAsync(embed: dbUsersEmbed.Build());

                // Store the found users in a custom state
                state.FoundUserIds = matchingUsers.Take(10).Select(u => u.UserId).ToList();
                state.CurrentMenu = EditUserMenu.SelectFoundUser;
                return;
            }

            // If not found in database, try to find in Discord
            var guildUsers = client._client.Guilds
                .SelectMany(g => g.Users)
                .Where(u => u.Username.Contains(message.Content, StringComparison.OrdinalIgnoreCase))
                .Distinct()
                .ToList();

            // Convert to List<SocketUser>
            var users = guildUsers.Cast<SocketUser>().ToList();

            if (users.Count == 0)
            {
                await message.Channel.SendMessageAsync("No users found. Please try again or type 'back' to return to the main menu.");
                return;
            }

            if (users.Count == 1)
            {
                // Only one user found, select them
                state.SelectedUserId = users[0].Id;
                state.CurrentMenu = EditUserMenu.EditUser;

                // Update the username in the database
                client.BotSystem.GetUserData(users[0].Id, users[0].Username);

                await ShowUserEditMenu(message, state, client);
                return;
            }

            // Multiple users found, show a list
            var embed = new EmbedBuilder()
                .WithTitle("Multiple Users Found")
                .WithDescription("Please select a user by typing their number:")
                .WithColor(Color.Blue);

            for (int i = 0; i < Math.Min(users.Count, 10); i++)
            {
                embed.AddField($"{i + 1}. {users[i].Username}", $"ID: {users[i].Id}");
            }

            embed.AddField("back", "Return to the main menu");

            await message.Channel.SendMessageAsync(embed: embed.Build());

            // Store the found users in the state
            state.FoundUsers = users.Take(10).ToList();
            state.CurrentMenu = EditUserMenu.SelectFoundUser;
        }

        /// <summary>
        /// Processes input for the list users menu.
        /// </summary>
        private async Task ProcessListUsersInput(SocketMessage message, EditUserMenuState state, DiscordBotClient client)
        {
            // Check for page navigation
            if (message.Content.Equals("next", StringComparison.OrdinalIgnoreCase))
            {
                state.CurrentPage++;
                await ShowUserList(message, state, client);
                return;
            }

            if (message.Content.Equals("prev", StringComparison.OrdinalIgnoreCase))
            {
                if (state.CurrentPage > 1)
                {
                    state.CurrentPage--;
                    await ShowUserList(message, state, client);
                }
                else
                {
                    await message.Channel.SendMessageAsync("You are already on the first page.");
                }
                return;
            }

            // Check if the input is a number
            if (int.TryParse(message.Content, out int selection))
            {
                // Get all users with bot data
                var allUsers = client.BotSystem.GetAllUserData()
                    .OrderByDescending(u => u.BotCount)
                    .ToList();

                // Calculate the index in the full list
                int index = (state.CurrentPage - 1) * 10 + selection - 1;

                if (index >= 0 && index < allUsers.Count)
                {
                    // Select the user
                    state.SelectedUserId = allUsers[index].UserId;
                    state.CurrentMenu = EditUserMenu.EditUser;

                    // Try to update the username if the user is in the server
                    var user = client._client.GetUser(allUsers[index].UserId);
                    if (user != null && allUsers[index].Username == "Unknown User")
                    {
                        client.BotSystem.GetUserData(allUsers[index].UserId, user.Username);
                    }

                    await ShowUserEditMenu(message, state, client);
                }
                else
                {
                    await message.Channel.SendMessageAsync("Invalid selection. Please try again.");
                }
            }
            else
            {
                await message.Channel.SendMessageAsync("Invalid input. Please enter a number, 'next', 'prev', or 'back'.");
            }
        }

        /// <summary>
        /// Shows the user list for the specified page.
        /// </summary>
        private async Task ShowUserList(SocketMessage message, EditUserMenuState state, DiscordBotClient client)
        {
            // Get all users with bot data
            var allUsers = client.BotSystem.GetAllUserData()
                .OrderByDescending(u => u.BotCount)
                .ToList();

            // Calculate total pages
            int totalPages = (int)Math.Ceiling(allUsers.Count / 10.0);

            // Ensure page is within valid range
            state.CurrentPage = Math.Max(1, Math.Min(state.CurrentPage, totalPages));

            // Get users for the current page
            var pageUsers = allUsers
                .Skip((state.CurrentPage - 1) * 10)
                .Take(10)
                .ToList();

            var embed = new EmbedBuilder()
                .WithTitle("User List")
                .WithDescription("Select a user to edit by typing their number:")
                .WithColor(Color.Blue)
                .WithFooter($"Page {state.CurrentPage}/{totalPages} | Type 'next' or 'prev' to navigate pages | 'back' to return to main menu");

            for (int i = 0; i < pageUsers.Count; i++)
            {
                var userData = pageUsers[i];
                string username = userData.Username;

                // Try to update the username if the user is in the server
                var user = client._client.GetUser(userData.UserId);
                if (user != null && username == "Unknown User")
                {
                    username = user.Username;
                    client.BotSystem.GetUserData(userData.UserId, username);
                }

                embed.AddField($"{i + 1}. {username}", $"ID: {userData.UserId} | Bots: {userData.BotCount} {Constants.Emojis.BotDown} | Items: {userData.Inventory.Count}");
            }

            await message.Channel.SendMessageAsync(embed: embed.Build());
        }

        /// <summary>
        /// Shows the edit menu for the selected user.
        /// </summary>
        private async Task ShowUserEditMenu(SocketMessage message, EditUserMenuState state, DiscordBotClient client)
        {
            // Get the user data
            var userData = client.BotSystem.GetUserData(state.SelectedUserId);

            // Try to get the Discord user if available
            var user = client._client.GetUser(state.SelectedUserId);
            string username = userData.Username;
            string avatarUrl = null;

            // If we found the user in Discord, update their stored username and get their avatar
            if (user != null)
            {
                if (username == "Unknown User")
                {
                    username = user.Username;
                    client.BotSystem.GetUserData(state.SelectedUserId, username);
                }
                avatarUrl = user.GetAvatarUrl() ?? user.GetDefaultAvatarUrl();
            }

            var embed = new EmbedBuilder()
                .WithTitle($"Edit User: {username}")
                .WithDescription("Select an option:")
                .AddField("User Info", $"ID: {state.SelectedUserId}\nBots: {userData.BotCount} {Constants.Emojis.BotDown}\nItems: {userData.Inventory.Count}")
                .AddField("1️⃣ Edit Bots", "Add or remove bot currency")
                .AddField("2️⃣ Edit Items", "Add or remove items")
                .AddField("3️⃣ Reset User", "Reset all user data (WARNING: Cannot be undone)")
                .AddField("4️⃣ Clear ALL Data", "Completely remove user from system (WARNING: PERMANENT)")
                .AddField("back", "Return to the main menu")
                .AddField("exit", "Close this menu")
                .WithColor(Color.Orange);

            // Add thumbnail if available
            if (avatarUrl != null)
            {
                embed.WithThumbnailUrl(avatarUrl);
            }

            await message.Channel.SendMessageAsync(embed: embed.Build());
        }

        /// <summary>
        /// Processes input for the edit user menu.
        /// </summary>
        private async Task ProcessEditUserInput(SocketMessage message, EditUserMenuState state, DiscordBotClient client)
        {
            switch (message.Content)
            {
                case "1":
                    // Edit bots
                    state.CurrentMenu = EditUserMenu.EditBots;
                    await ShowEditBotsMenu(message, state, client);
                    break;

                case "2":
                    // Edit items
                    state.CurrentMenu = EditUserMenu.EditItems;
                    await ShowEditItemsMenu(message, state, client);
                    break;

                case "3":
                    // Reset user
                    await message.Channel.SendMessageAsync("Are you sure you want to reset this user? Type 'confirm' to proceed or anything else to cancel.");
                    state.ConfirmAction = "reset";
                    break;

                case "4":
                    // Clear ALL data - completely remove user from system
                    await message.Channel.SendMessageAsync("⚠️ **DANGER** ⚠️\nThis will **PERMANENTLY DELETE** this user from the system entirely!\nThey will be completely removed from leaderboards and all save data.\nType 'CONFIRM DELETE' to proceed or anything else to cancel.");
                    state.ConfirmAction = "delete";
                    break;

                case "confirm":
                    if (state.ConfirmAction == "reset")
                    {
                        // Reset the user
                        client.BotSystem.ResetUser(state.SelectedUserId);
                        await message.Channel.SendMessageAsync("User data has been reset.");

                        // Return to the edit user menu
                        state.ConfirmAction = null;
                        await ShowUserEditMenu(message, state, client);
                    }
                    break;

                case "CONFIRM DELETE":
                    if (state.ConfirmAction == "delete")
                    {
                        // Get username for confirmation message
                        var userData = client.BotSystem.GetUserData(state.SelectedUserId);
                        string username = userData.Username ?? "Unknown User";

                        // Completely remove the user from the system
                        int removedCount = client.BotSystem.RemoveUsers(new List<ulong> { state.SelectedUserId });

                        if (removedCount > 0)
                        {
                            // Force immediate save to update leaderboards
                            client.BotSystem.ForceSaveUserData();
                            await message.Channel.SendMessageAsync($"✅ User **{username}** (ID: {state.SelectedUserId}) has been **PERMANENTLY DELETED** from the system.\nThey have been completely removed from all leaderboards and save data.");
                        }
                        else
                        {
                            await message.Channel.SendMessageAsync("❌ Failed to delete user. They may not exist in the system.");
                        }

                        // Return to main menu since the user no longer exists
                        state.ConfirmAction = null;
                        state.SelectedUserId = 0;
                        state.CurrentMenu = EditUserMenu.Main;
                        await ShowMainMenu(message, client);
                    }
                    break;

                default:
                    if (state.ConfirmAction != null)
                    {
                        // Cancel the confirmation
                        await message.Channel.SendMessageAsync("Action cancelled.");
                        state.ConfirmAction = null;
                        await ShowUserEditMenu(message, state, client);
                    }
                    else
                    {
                        await message.Channel.SendMessageAsync("Invalid option. Please select a number from 1-4, or type 'back' to return to the main menu.");
                    }
                    break;
            }
        }

        /// <summary>
        /// Shows the edit bots menu for the selected user.
        /// </summary>
        private async Task ShowEditBotsMenu(SocketMessage message, EditUserMenuState state, DiscordBotClient client)
        {
            // Get the user data
            var userData = client.BotSystem.GetUserData(state.SelectedUserId);

            // Try to get the Discord user if available
            var user = client._client.GetUser(state.SelectedUserId);
            string username = userData.Username;
            string avatarUrl = null;

            // If we found the user in Discord, update their stored username and get their avatar
            if (user != null)
            {
                if (username == "Unknown User")
                {
                    username = user.Username;
                    client.BotSystem.GetUserData(state.SelectedUserId, username);
                }
                avatarUrl = user.GetAvatarUrl() ?? user.GetDefaultAvatarUrl();
            }

            var embed = new EmbedBuilder()
                .WithTitle($"Edit Bots: {username}")
                .WithDescription($"Current Bots: {userData.BotCount} {Constants.Emojis.BotDown}")
                .AddField("Options", "Type one of the following commands:")
                .AddField("add [amount]", "Add bots to the user (e.g., 'add 100')")
                .AddField("remove [amount]", "Remove bots from the user (e.g., 'remove 50')")
                .AddField("set [amount]", "Set the user's bots to a specific amount (e.g., 'set 1000')")
                .AddField("back", "Return to the user edit menu")
                .AddField("exit", "Close this menu")
                .WithColor(Color.Green);

            // Add thumbnail if available
            if (avatarUrl != null)
            {
                embed.WithThumbnailUrl(avatarUrl);
            }

            await message.Channel.SendMessageAsync(embed: embed.Build());
        }

        /// <summary>
        /// Processes input for the edit bots menu.
        /// </summary>
        private async Task ProcessEditBotsInput(SocketMessage message, EditUserMenuState state, DiscordBotClient client)
        {
            // Get the user data
            var userData = client.BotSystem.GetUserData(state.SelectedUserId);

            // Get the username from stored data
            string username = userData.Username;

            // Parse the command
            string[] parts = message.Content.Split(' ');
            if (parts.Length != 2)
            {
                await message.Channel.SendMessageAsync("Invalid format. Please use 'add [amount]', 'remove [amount]', or 'set [amount]'.");
                return;
            }

            // Parse the amount
            if (!int.TryParse(parts[1], out int amount) || amount < 0)
            {
                await message.Channel.SendMessageAsync("Invalid amount. Please enter a positive number.");
                return;
            }

            switch (parts[0].ToLower())
            {
                case "add":
                    // Add bots
                    bool fullAmountAdded = client.BotSystem.AddBots(state.SelectedUserId, amount);

                    // Get updated user data
                    userData = client.BotSystem.GetUserData(state.SelectedUserId);

                    string addMessage = $"Added {amount} {Constants.Emojis.BotDown} to {username}. New total: {userData.BotCount} {Constants.Emojis.BotDown}";

                    // Add a message if the user reached the bot limit
                    if (!fullAmountAdded)
                    {
                        addMessage += "\n**Note: User has reached the maximum limit of 1,000,000 bots.**";
                    }

                    await message.Channel.SendMessageAsync(addMessage);
                    break;

                case "remove":
                    // Remove bots
                    if (amount > userData.BotCount)
                    {
                        await message.Channel.SendMessageAsync($"Cannot remove {amount} {Constants.Emojis.BotDown} from {username}. They only have {userData.BotCount} {Constants.Emojis.BotDown}.");
                        return;
                    }

                    client.BotSystem.RemoveBots(state.SelectedUserId, amount);
                    await message.Channel.SendMessageAsync($"Removed {amount} {Constants.Emojis.BotDown} from {username}. New total: {userData.BotCount} {Constants.Emojis.BotDown}");
                    break;

                case "set":
                    // Set bots
                    long difference = amount - userData.BotCount;
                    bool limitReached = false;

                    // Check if the amount exceeds the limit
                    if (amount > 1000000)
                    {
                        amount = 1000000;
                        limitReached = true;
                    }

                    if (difference > 0)
                    {
                        limitReached = !client.BotSystem.AddBots(state.SelectedUserId, difference);
                    }
                    else if (difference < 0)
                    {
                        client.BotSystem.RemoveBots(state.SelectedUserId, -difference);
                    }

                    // Get updated user data
                    userData = client.BotSystem.GetUserData(state.SelectedUserId);

                    string setMessage = $"Set {username}'s {Constants.Emojis.BotDown} to {userData.BotCount}.";

                    // Add a message if the user reached the bot limit
                    if (limitReached)
                    {
                        setMessage += "\n**Note: User has reached the maximum limit of 1,000,000 bots.**";
                    }

                    await message.Channel.SendMessageAsync(setMessage);
                    break;

                default:
                    await message.Channel.SendMessageAsync("Invalid command. Please use 'add', 'remove', or 'set'.");
                    break;
            }
        }

        /// <summary>
        /// Shows the edit items menu for the selected user.
        /// </summary>
        private async Task ShowEditItemsMenu(SocketMessage message, EditUserMenuState state, DiscordBotClient client)
        {
            // Get the user data
            var userData = client.BotSystem.GetUserData(state.SelectedUserId);

            // Try to get the Discord user if available
            var user = client._client.GetUser(state.SelectedUserId);
            string username = userData.Username;
            string avatarUrl = null;

            // If we found the user in Discord, update their stored username and get their avatar
            if (user != null)
            {
                if (username == "Unknown User")
                {
                    username = user.Username;
                    client.BotSystem.GetUserData(state.SelectedUserId, username);
                }
                avatarUrl = user.GetAvatarUrl() ?? user.GetDefaultAvatarUrl();
            }

            // Get all consumables
            var allConsumables = client.BotSystem.GetAllConsumables();

            var embed = new EmbedBuilder()
                .WithTitle($"Edit Items: {username}")
                .WithDescription($"Current Items: {userData.Inventory.Count}")
                .WithColor(Color.Purple);

            // Add thumbnail if available
            if (avatarUrl != null)
            {
                embed.WithThumbnailUrl(avatarUrl);
            }

            // Add user's inventory
            var sb = new StringBuilder();
            if (userData.Inventory.Count > 0)
            {
                sb.AppendLine("**Current Inventory:**");
                foreach (var item in userData.Inventory)
                {
                    var consumable = allConsumables.FirstOrDefault(c => c.Id == item.ConsumableId);
                    if (consumable != null)
                    {
                        sb.AppendLine($"• ID: {item.InventoryId} - {consumable.Name} {consumable.Emoji}");
                    }
                }
            }
            else
            {
                sb.AppendLine("**Current Inventory:** Empty");
            }

            embed.AddField("Inventory", sb.ToString());

            // Add available consumables
            sb.Clear();
            sb.AppendLine("**Available Consumables:**");
            foreach (var consumable in allConsumables)
            {
                sb.AppendLine($"• ID: {consumable.Id} - {consumable.Name} {consumable.Emoji}");
            }

            embed.AddField("Available Consumables", sb.ToString());

            // Add commands
            embed.AddField("Options", "Type one of the following commands:")
                .AddField("add [consumable_id]", "Add a consumable to the user (e.g., 'add 1')")
                .AddField("remove [item_id]", "Remove an item from the user's inventory (e.g., 'remove 5')")
                .AddField("clear", "Remove all items from the user's inventory")
                .AddField("back", "Return to the user edit menu")
                .AddField("exit", "Close this menu");

            await message.Channel.SendMessageAsync(embed: embed.Build());
        }

        /// <summary>
        /// Processes input for the edit items menu.
        /// </summary>
        private async Task ProcessEditItemsInput(SocketMessage message, EditUserMenuState state, DiscordBotClient client)
        {
            // Get the user data
            var userData = client.BotSystem.GetUserData(state.SelectedUserId);

            // Get the username from stored data
            string username = userData.Username;

            // Check for clear command
            if (message.Content.Equals("clear", StringComparison.OrdinalIgnoreCase))
            {
                // Clear all items
                userData.Inventory.Clear();
                client.BotSystem.SaveUserData();
                await message.Channel.SendMessageAsync($"Cleared all items from {username}'s inventory.");
                return;
            }

            // Parse the command
            string[] parts = message.Content.Split(' ');
            if (parts.Length != 2)
            {
                await message.Channel.SendMessageAsync("Invalid format. Please use 'add [consumable_id]', 'remove [item_id]', or 'clear'.");
                return;
            }

            // Parse the ID
            if (!int.TryParse(parts[1], out int id) || id < 0)
            {
                await message.Channel.SendMessageAsync("Invalid ID. Please enter a positive number.");
                return;
            }

            switch (parts[0].ToLower())
            {
                case "add":
                    // Add consumable
                    var consumable = client.BotSystem.GetConsumable(id);
                    if (consumable == null)
                    {
                        await message.Channel.SendMessageAsync($"Consumable with ID {id} not found.");
                        return;
                    }

                    // Add the consumable to the user's inventory
                    userData.AddConsumable(consumable.Id, consumable.DefaultUses);
                    client.BotSystem.SaveUserData();

                    await message.Channel.SendMessageAsync($"Added {consumable.Name} {consumable.Emoji} to {username}'s inventory.");
                    break;

                case "remove":
                    // Remove item
                    var item = userData.Inventory.FirstOrDefault(i => i.InventoryId == id);
                    if (item == null)
                    {
                        await message.Channel.SendMessageAsync($"Item with ID {id} not found in {username}'s inventory.");
                        return;
                    }

                    // Get the consumable name for the message
                    var itemConsumable = client.BotSystem.GetConsumable(item.ConsumableId);
                    string itemName = itemConsumable?.Name ?? "Unknown Item";
                    string itemEmoji = itemConsumable?.Emoji ?? "";

                    // Remove the item
                    userData.Inventory.Remove(item);
                    client.BotSystem.SaveUserData();

                    await message.Channel.SendMessageAsync($"Removed {itemName} {itemEmoji} from {username}'s inventory.");
                    break;

                default:
                    await message.Channel.SendMessageAsync("Invalid command. Please use 'add', 'remove', or 'clear'.");
                    break;
            }
        }
    }

    /// <summary>
    /// Represents the current menu in the edit user command.
    /// </summary>
    public enum EditUserMenu
    {
        Main,
        FindUser,
        ListUsers,
        EditUser,
        EditBots,
        EditItems,
        SelectFoundUser
    }

    /// <summary>
    /// Represents the state of the edit user menu.
    /// </summary>
    public class EditUserMenuState
    {
        /// <summary>
        /// The current menu being displayed.
        /// </summary>
        public EditUserMenu CurrentMenu { get; set; } = EditUserMenu.Main;

        /// <summary>
        /// The ID of the selected user.
        /// </summary>
        public ulong SelectedUserId { get; set; }

        /// <summary>
        /// The current page for paginated menus.
        /// </summary>
        public int CurrentPage { get; set; } = 1;

        /// <summary>
        /// The last time the user interacted with the menu.
        /// </summary>
        public DateTime LastActivity { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// The action that needs confirmation.
        /// </summary>
        public string? ConfirmAction { get; set; }

        /// <summary>
        /// List of Discord users found in a search.
        /// </summary>
        public List<SocketUser> FoundUsers { get; set; } = new List<SocketUser>();

        /// <summary>
        /// List of user IDs found in a database search.
        /// </summary>
        public List<ulong> FoundUserIds { get; set; } = new List<ulong>();
    }
}
