namespace BotChan.Models
{
    /// <summary>
    /// Configuration settings for the Discord bot.
    /// This class holds settings loaded from appsettings.json.
    /// </summary>
    public class BotConfig
    {
        /// <summary>
        /// The Discord bot token used to authenticate with Discord's API.
        /// This should be kept secret and not committed to version control.
        /// </summary>
        public string Token { get; set; } = string.Empty;

        /// <summary>
        /// The prefix character(s) that users must type before commands.
        /// For example, if Prefix is "%", users would type "%help" to use the help command.
        /// </summary>
        public string Prefix { get; set; } = string.Empty;

        /// <summary>
        /// The Discord user ID of the bot owner.
        /// The owner has full permissions for all commands.
        /// </summary>
        public ulong OwnerId { get; set; }
    }
}
