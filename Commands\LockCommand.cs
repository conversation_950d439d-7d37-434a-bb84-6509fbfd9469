using Discord;
using Discord.WebSocket;
using BotChan.Services;

namespace BotChan.Commands
{
    /// <summary>
    /// LockCommand - Activates emergency lockdown mode, restricting command usage to staff only
    /// Usage: %lock [reason]
    /// Example: %lock Emergency maintenance
    /// </summary>
    public class LockCommand : Command
    {
        // Override the default category
        public override string Category => "Admin";

        // Override the default usage
        public override string Usage => "%lock [reason]";

        // Override the default examples
        public override string Examples => "%lock Emergency maintenance\n%lock Bot abuse detected";

        // This command is exempt from lockdown restrictions
        public override bool IsExemptFromLockdown => true;

        public LockCommand() : base("lock", "Activates emergency lockdown mode (staff only)")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Check if the user is a staff member
            if (!client.Permissions.IsStaffUser(message.Author.Id))
            {
                await message.Channel.SendMessageAsync("You don't have permission to use this command.");
                return;
            }

            // Check if the bot is already in lockdown
            if (client.BotSystem.IsInLockdown())
            {
                var lockdownInfo = client.BotSystem.GetLockdownInfo();
                var activator = client._client.GetUser(lockdownInfo.ActivatedBy);
                string activatorName = activator != null ? activator.Username : "Unknown User";

                var embed = new EmbedBuilder()
                    .WithTitle("⚠️ Bot Already in Lockdown")
                    .WithDescription("The bot is already in emergency lockdown mode.")
                    .WithColor(Color.Red)
                    .AddField("Activated By", activatorName, true)
                    .AddField("Activated At", $"<t:{((DateTimeOffset)lockdownInfo.ActivatedAt).ToUnixTimeSeconds()}:F>", true)
                    .WithFooter($"Use %unlock to deactivate lockdown mode");

                if (!string.IsNullOrEmpty(lockdownInfo.Reason))
                {
                    embed.AddField("Reason", lockdownInfo.Reason);
                }

                await message.Channel.SendMessageAsync(embed: embed.Build());
                return;
            }

            // Get the reason (all arguments after the command name)
            string reason = args.Length > 1
                ? string.Join(" ", args.Skip(1))
                : "No reason provided";

            // Activate lockdown
            client.BotSystem.ActivateLockdown(message.Author.Id, reason);

            // Create an embed for the response
            var responseEmbed = new EmbedBuilder()
                .WithTitle("🔒 Emergency Lockdown Activated")
                .WithDescription("The bot is now in emergency lockdown mode. Only staff members can use commands until lockdown is deactivated.")
                .WithColor(Color.Red)
                .AddField("Activated By", message.Author.Username, true)
                .AddField("Reason", reason)
                .WithFooter("Use %unlock to deactivate lockdown mode");

            await message.Channel.SendMessageAsync(embed: responseEmbed.Build());
        }
    }
}
