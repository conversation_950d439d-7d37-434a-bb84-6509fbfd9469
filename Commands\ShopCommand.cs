using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;
using System.Text;

namespace BotChan.Commands
{
    /// <summary>
    /// ShopCommand - A command that shows the shop for purchasing consumables
    /// Usage: %shop
    /// Example: %shop
    /// </summary>
    public class ShopCommand : Command
    {
        // Number of items to display per page
        private const int ItemsPerPage = 10;

        // Override the default category
        public override string Category => "Economy";

        // Override the default usage
        public override string Usage => "%shop - Show shop categories\n%shop [category] - Show items in category\n%shop buy [category] [id] - Buy an item\n%shop [category] page [number] - Navigate pages";

        // Override the default examples
        public override string Examples => "%shop\n%shop consumables\n%shop packs\n%shop bombs\n%shop buy consumables 1\n%shop consumables page 2";

        public ShopCommand() : base("shop", "Browse and purchase consumables with your bots")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // If no arguments, show the main shop menu
            if (args.Length == 1)
            {
                await ShowMainShopMenu(message, client);
                return;
            }

            // Check if the user wants to buy something
            if (args.Length > 1 && args[1].ToLower() == "buy")
            {
                if (args.Length < 4)
                {
                    await message.Channel.SendMessageAsync("Please specify a category and item ID to buy.\n\nExample: `%shop buy consumables 1`\nExample: `%shop buy packs 2`\nExample: `%shop buy bombs 1`");
                    return;
                }

                string categoryName = args[2].ToLower();
                if (!int.TryParse(args[3], out int itemId))
                {
                    await message.Channel.SendMessageAsync("Invalid item ID. Please provide a numeric ID.\n\nExample: `%shop buy consumables 1`");
                    return;
                }

                await BuyConsumable(message, categoryName, itemId, client);
                return;
            }

            // Parse category and check for page command
            string categoryArg = args[1].ToLower();
            int category = GetCategoryFromString(categoryArg);

            if (category == -1)
            {
                await message.Channel.SendMessageAsync("Invalid category. Please use one of the following:\n• `%shop consumables` or `%shop 1`\n• `%shop packs` or `%shop 2`\n• `%shop bombs` or `%shop 3`");
                return;
            }

            // Check if the user wants to see a specific page
            if (args.Length > 2 && args[2].ToLower() == "page")
            {
                if (args.Length < 4 || !int.TryParse(args[3], out int pageNumber))
                {
                    await message.Channel.SendMessageAsync($"Please specify a valid page number. Example: `%shop {categoryArg} page 2`");
                    return;
                }

                await ShowCategoryShop(message, category, pageNumber, client);
                return;
            }

            // Otherwise, show the category shop
            await ShowCategoryShop(message, category, 1, client);
        }

        private int GetCategoryFromString(string categoryArg)
        {
            return categoryArg switch
            {
                "consumables" or "consumable" or "multipliers" or "multiplier" or "mult" or "1" => 1,
                "vanity" or "van" or "botvanity" or "botvan" or "2" => 2,
                "bombs" or "bomb" or "3" => 3,
                _ => -1
            };
        }

        private string GetCategoryName(int category)
        {
            return category switch
            {
                1 => "Consumables",
                2 => "Vanity",
                3 => "Bombs",
                _ => "Unknown"
            };
        }

        private string GetCategoryEmoji(int category)
        {
            return category switch
            {
                1 => "⭐",
                2 => "📦",
                3 => "💣",
                _ => "❓"
            };
        }

        private string FormatNumber(int number)
        {
            if (number >= 1000000)
                return $"{number / 1000000}M";
            else if (number >= 1000)
                return $"{number / 1000}K";
            else
                return number.ToString();
        }

        private async Task ShowMainShopMenu(SocketMessage message, DiscordBotClient client)
        {
            // Get the user's data
            var userData = client.BotSystem.GetUserData(message.Author.Id);

            // Get all visible consumables
            var allConsumables = client.BotSystem.GetAllConsumables()
                .Where(c => c.IsVisibleInShop)
                .ToList();

            // Count items by category
            int multipliersCount = allConsumables.Count(c => c.Category == 1);
            int packsCount = allConsumables.Count(c => c.Category == 2);
            int bombsCount = allConsumables.Count(c => c.Category == 3);

            // Build the description for the main shop
            var description = new StringBuilder();
            description.AppendLine("**Available Commands:**");
            description.AppendLine("`%shop Consumables`");
            description.AppendLine("`%shop Vanity`");
            description.AppendLine("`%shop Bombs`");
            description.AppendLine("");
            description.AppendLine("`%shop buy {Category} {id}` to buy an item");
            description.AppendLine("`%sell {inventory id}` to sell an item for 50% of its original price");
            description.AppendLine("`%shop {Category} page [1-2]` to navigate pages");
            description.AppendLine("════════════════════════════════");

            // Add categories in the same format as items
            string[] categories = {
                $"1 ⭐ Consumables",
                $"2 📦 Vanity",
                $"3 💣 Bombs"
            };

            int[] counts = { multipliersCount, packsCount, bombsCount };

            for (int i = 0; i < categories.Length; i++)
            {
                string categoryLine = categories[i];
                int targetWidth = 40; // Same target width as items
                int currentLength = GetDisplayLength(categoryLine);
                int dashCount = Math.Max(1, targetWidth - currentLength);
                string dashes = new string('-', dashCount);

                description.AppendLine($"{categoryLine}{dashes} {counts[i]} items");
            }

            var embed = new EmbedBuilder()
                .WithAuthor("Bot-Chan's Secret Armory", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                .WithTitle("Hello! I'm Bot-Chan! Please select the shop you would like to check out today!")
                .WithDescription(description.ToString())
                .WithColor(Color.Blue)
                .WithFooter($"You have {userData.BotCount:N0} bots")
                .WithTimestamp(DateTimeOffset.Now);

            await message.Channel.SendMessageAsync(embed: embed.Build());
        }

        /// <summary>
        /// Calculates the display length of a string, treating Discord custom emojis as single characters.
        /// </summary>
        private int GetDisplayLength(string text)
        {
            if (string.IsNullOrEmpty(text))
                return 0;

            // Replace Discord custom emojis with single characters for length calculation
            // Discord custom emojis have the format <:name:id> or <a:name:id>
            var customEmojiPattern = @"<a?:[^:]+:\d+>";
            var withoutCustomEmojis = System.Text.RegularExpressions.Regex.Replace(text, customEmojiPattern, "E");

            return withoutCustomEmojis.Length;
        }

        private async Task ShowCategoryShop(SocketMessage message, int category, int page, DiscordBotClient client)
        {
            // Get the user's data
            var userData = client.BotSystem.GetUserData(message.Author.Id);

            // Get all visible consumables for this category
            var consumables = client.BotSystem.GetAllConsumables()
                .Where(c => c.IsVisibleInShop && c.Category == category)
                .OrderBy(c => c.Cost) // Order by cost like in the original
                .ToList();

            // Check if category has no items
            if (!consumables.Any())
            {
                var noItemsEmbed = new EmbedBuilder()
                    .WithAuthor("Bot-Chan's Secret Armory", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                    .WithTitle(GetCategoryName(category))
                    .WithDescription("This shop has no items available at the moment.")
                    .WithColor(Color.Orange)
                    .WithFooter($"You have {userData.BotCount:N0} bots")
                    .WithTimestamp(DateTimeOffset.Now);

                await message.Channel.SendMessageAsync(embed: noItemsEmbed.Build());
                return;
            }

            // Calculate pagination
            const int itemsPerPage = 10; // Standardized pagination
            int totalPages = (int)Math.Ceiling((double)consumables.Count / itemsPerPage);
            page = Math.Max(1, Math.Min(page, totalPages));

            var pageConsumables = consumables
                .Skip((page - 1) * itemsPerPage)
                .Take(itemsPerPage)
                .ToList();

            // Build the description like the original format
            var description = new StringBuilder();

            // Category-specific descriptions
            if (category == 1) // Consumables/Multipliers
            {
                description.AppendLine("Purchase a consumable for more bots!");
                description.AppendLine("The better the consumable the more bots you acquire!");
            }
            else if (category == 2) // vanity
            {
                description.AppendLine("Purchase vanity items to customize your inventory!");
                description.AppendLine("Vanity can be turned back into bots when you don't want them anymore!");
            }
            else if (category == 3) // Bombs
            {
                description.AppendLine("Purchase bombs and gift bombs for strategic gameplay!");
                description.AppendLine("Attack enemies or gift bots to allies!");
            }
            description.AppendLine($"`%shop buy {{category}} {{id}}` to buy an item");
            description.AppendLine($"`%sell {{id}}` to sell an item for 50% of its original price");
            description.AppendLine($"`%shop {{category}} page [1-{totalPages}]` to navigate pages");
            description.AppendLine("════════════════════════════════");

            // Add items in the original format
            int displayIndex = (page - 1) * itemsPerPage + 1;
            foreach (var consumable in pageConsumables)
            {
                string costDisplay = FormatNumber(consumable.Cost);
                string itemLine = $"{displayIndex} {consumable.Emoji} {consumable.Name}";

                // Add dashes to align the cost - use display length calculation that handles Discord emojis
                int targetWidth = 40; // Target width for alignment
                int currentLength = GetDisplayLength(itemLine);
                int dashCount = Math.Max(1, targetWidth - currentLength);
                string dashes = new string('-', dashCount);

                description.AppendLine($"{itemLine}{dashes} {costDisplay} {Constants.Emojis.BotDown}");
                displayIndex++;
            }

            var embed = new EmbedBuilder()
                .WithAuthor("Bot-Chan's Secret Armory", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                .WithTitle(GetCategoryName(category))
                .WithDescription(description.ToString())
                .WithColor(Color.Blue)
                .WithFooter($"Page {page}/{totalPages} | You have {userData.BotCount:N0} bots")
                .WithTimestamp(DateTimeOffset.Now);

            await message.Channel.SendMessageAsync(embed: embed.Build());
        }

        private async Task ShowShop(SocketMessage message, int page, DiscordBotClient client)
        {
            // Get the user's data
            var userData = client.BotSystem.GetUserData(message.Author.Id);

            // Get all consumables that are visible in the shop
            var allConsumables = client.BotSystem.Config.AvailableConsumables.Where(c => c.IsVisibleInShop).ToList();

            // Calculate total pages
            int totalPages = (int)Math.Ceiling(allConsumables.Count / (double)ItemsPerPage);
            if (totalPages == 0) totalPages = 1; // At least one page even if empty

            // Ensure page is within valid range
            page = Math.Max(1, Math.Min(page, totalPages));

            // Get consumables for the current page
            var pageConsumables = allConsumables
                .Skip((page - 1) * ItemsPerPage)
                .Take(ItemsPerPage)
                .ToList();

            // Build the shop message content
            var sb = new StringBuilder();

            // Add the header
            sb.AppendLine("Purchase a consumable for more bots!");
            sb.AppendLine("The better the consumable the more bots you acquire!");
            sb.AppendLine("%shop buy {id} to buy an item");
            sb.AppendLine("%shop sell {id} to sell an item for 50% of its original price");

            // Add navigation instructions if there are multiple pages
            if (totalPages > 1)
            {
                sb.AppendLine($"%shop page [1-{totalPages}] to navigate pages");
            }

            sb.AppendLine("════════════════════════════════");

            // Add the consumables
            for (int i = 0; i < pageConsumables.Count; i++)
            {
                var consumable = pageConsumables[i];
                int displayId = (page - 1) * ItemsPerPage + i + 1; // Calculate the display ID
                string formattedPrice = FormatNumber(consumable.Cost);

                // Format: ID :emoji: name----------------------- price :BotDown:
                // Calculate dashes needed for alignment using display length that handles Discord emojis
                string itemLine = $"{displayId} {consumable.Emoji} {consumable.Name}";
                int targetWidth = 40; // Target width for alignment
                int currentLength = GetDisplayLength(itemLine);
                int dashCount = Math.Max(1, targetWidth - currentLength);
                string dashes = new string('-', dashCount);

                sb.AppendLine($"{itemLine}{dashes} {formattedPrice} {Constants.Emojis.BotDown}");
            }

            // Add a blank line at the end for spacing
            sb.AppendLine();

            // Create an embed for the shop
            var embed = new EmbedBuilder()
                .WithAuthor("Bot-Chan's Secret Armory", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                .WithDescription(sb.ToString())
                .WithColor(Color.Gold)
                .WithFooter($"Page {page}/{totalPages} | You have {userData.BotCount} bots");

            // Send the embed message
            var shopMessage = await message.Channel.SendMessageAsync(embed: embed.Build());

            // No need to add reactions or handlers - using command-based pagination
        }

        private async Task BuyConsumable(SocketMessage message, string categoryName, int displayIndex, DiscordBotClient client)
        {
            // Get category number
            int category = GetCategoryFromString(categoryName);
            if (category == -1)
            {
                await message.Channel.SendMessageAsync("Invalid category. Please use: consumables, packs, or bombs.");
                return;
            }

            // Get all visible consumables for this category, ordered by cost
            var consumables = client.BotSystem.GetAllConsumables()
                .Where(c => c.IsVisibleInShop && c.Category == category)
                .OrderBy(c => c.Cost)
                .ToList();

            // Check if the display index is valid
            if (displayIndex < 1 || displayIndex > consumables.Count)
            {
                await message.Channel.SendMessageAsync($"Invalid item number. Please choose a number between 1 and {consumables.Count} for {GetCategoryName(category)}.");
                return;
            }

            // Get the consumable by display index (1-based)
            var consumable = consumables[displayIndex - 1];

            // Get the user's data
            var userData = client.BotSystem.GetUserData(message.Author.Id);

            // Check if the user has enough bots
            if (userData.BotCount < consumable.Cost)
            {
                await message.Channel.SendMessageAsync($"You don't have enough bots to purchase **{consumable.Name}**. You need **{consumable.Cost}** bots, but you only have **{userData.BotCount}**.");
                return;
            }

            // Purchase the consumable
            bool success = client.BotSystem.PurchaseConsumable(message.Author.Id, consumable.Id);

            if (success)
            {
                // Create an embed for the response
                var description = new StringBuilder();

                description.AppendLine($"You've purchased **{consumable.Name}** {consumable.Emoji} for **{consumable.Cost}** {Constants.Emojis.BotDown}!");
                description.AppendLine();

                // Different text based on item type
                if (consumable.Category == 1) // Multiplier
                {
                    description.AppendLine($"• This consumable gives you a **{consumable.Multiplier}x** multiplier for **{consumable.DefaultUses}** uses.");
                    description.AppendLine($"• Use it with the `/use [inventory_id]` command.");
                }
                else if (consumable.Category == 2) // Vanity
                {
                    description.AppendLine($"• This vanity item can be turned back into **{consumable.BotReward:N0}** bots.");
                    description.AppendLine($"• Use it with the `/use [inventory_id]` command to claim back bots");
                }
                else if (consumable.Category == 3) // Bomb
                {
                    description.AppendLine($"• This bomb has a **100%** success rate.");
                    description.AppendLine($"• Use it with the `/bomb [target_user_id] [inventory_id]` command to attack other players.");
                }

                var embed = new EmbedBuilder()
                    .WithAuthor("Bot-Chan's Secret Armory", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                    .WithTitle("✅ Purchase Successful!")
                    .WithDescription(description.ToString())
                    .WithColor(Color.Green)
                    .WithThumbnailUrl("https://cdn.discordapp.com/attachments/1363245551184908468/1363389520245952672/botchan_icon.png")
                    .WithFooter($"Remaining Bots: {userData.BotCount}")
                    .WithTimestamp(DateTimeOffset.Now);

                // Send the response
                await message.Channel.SendMessageAsync(embed: embed.Build());
            }
            else
            {
                await message.Channel.SendMessageAsync("Error: Failed to purchase the consumable.");
            }
        }



        /// <summary>
        /// Formats a number with K, M, B suffixes for thousands, millions, billions
        /// </summary>
        private string FormatNumber(long number)
        {
            if (number >= 1_000_000_000)
                return $"{number / 1_000_000_000}B";
            if (number >= 1_000_000)
                return $"{number / 1_000_000}M";
            if (number >= 1_000)
                return $"{number / 1_000}K";
            return number.ToString();
        }
    }
}
