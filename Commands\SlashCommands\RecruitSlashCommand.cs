using Discord;
using Discord.WebSocket;
using BotChan.Services;
using BotChan.Models;

namespace BotChan.Commands.SlashCommands
{
    /// <summary>
    /// RecruitSlashCommand - Slash command version of the recruit command
    /// Usage: /recruit
    /// Recruit bots to add to your collection
    /// </summary>
    public class RecruitSlashCommand
    {
        /// <summary>
        /// Executes the recruit slash command.
        /// </summary>
        /// <param name="command">The slash command that was executed</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task ExecuteAsync(SocketSlashCommand command, DiscordBotClient client)
        {
            try
            {
                // Check cooldown before allowing the command (same as %recruit)
                if (!client.Permissions.HasPermission(command.User.Id, "recruit", out string cooldownMessage))
                {
                    if (!string.IsNullOrEmpty(cooldownMessage))
                    {
                        await command.RespondAsync($"⏰ **Recruit command is on cooldown!** {cooldownMessage}", ephemeral: true);
                    }
                    else
                    {
                        await command.RespondAsync("❌ You don't have permission to use this command.", ephemeral: true);
                    }
                    return;
                }

                // Update last usage time for cooldown tracking
                client.Permissions.UpdateCommandUsage(command.User.Id, "recruit");

                // Get the user's data
                var userData = client.BotSystem.GetUserData(command.User.Id);

                // Check if the user has an active consumable
                double multiplier = 1.0;
                string activeConsumableName = string.Empty;

                // If the user has an active consumable, use it
                if (!string.IsNullOrEmpty(userData.ActiveConsumableId))
                {
                    var consumable = client.BotSystem.GetConsumable(userData.ActiveConsumableId);

                    if (consumable != null && userData.ActiveConsumableUsesRemaining > 0)
                    {
                        // Use the active consumable
                        multiplier = userData.ActiveConsumableMultiplier;
                        activeConsumableName = consumable.Name;

                        // Reduce the uses remaining
                        userData.ActiveConsumableUsesRemaining--;

                        // If the consumable has been fully used, clear the active consumable
                        if (userData.ActiveConsumableUsesRemaining <= 0)
                        {
                            userData.ActiveConsumableId = string.Empty;
                            userData.ActiveConsumableMultiplier = 1.0;
                            userData.ActiveConsumableUsesRemaining = 0;
                        }

                        // Save the user data
                        client.BotSystem.SaveUserData();
                    }
                    else
                    {
                        // Clear invalid active consumable
                        userData.ActiveConsumableId = string.Empty;
                        userData.ActiveConsumableMultiplier = 1.0;
                        userData.ActiveConsumableUsesRemaining = 0;
                        client.BotSystem.SaveUserData();
                    }
                }

                // Calculate the reward (exact same as %recruit)
                long baseReward = client.BotSystem.Config.BaseRecruitReward;
                long totalReward = (long)Math.Ceiling(baseReward * multiplier);

                // Add the bots to the user's count
                bool fullAmountAdded = client.BotSystem.AddBots(command.User.Id, totalReward);

                // Get the updated user data
                userData = client.BotSystem.GetUserData(command.User.Id);

                // Build the bot emoji string with directly embedded emoji (max 20 emojis, same as %recruit)
                string botEmojis = string.Empty;
                long emojiCount = Math.Min(totalReward, 20); // Limit to maximum 20 emojis
                for (long i = 0; i < emojiCount; i++)
                {
                    // add randomized bot emojis
                    int RandomBot = new Random().Next(1, 5);

                    if (RandomBot == 1)
                    {
                        botEmojis += "<:BotDown:1410320403469176913> ";
                    }
                    else if (RandomBot == 2)
                    {
                        botEmojis += "<:RBotDown:1411710381755207701> ";
                    }
                    else if (RandomBot == 3)
                    {
                        botEmojis += "<:GBotDown:1411710401464369153> ";
                    }
                    else if (RandomBot == 4)
                    {
                        botEmojis += "<:BBotDown:1411710393277087744> ";
                    }
                }

                // Create the message with the specified format and directly embedded emojis (exact same as %recruit)
                string responseMessage;

                // Check if a consumable is being used
                if (!string.IsNullOrEmpty(activeConsumableName))
                {
                    // Get the consumable to access its emoji
                    var consumable = client.BotSystem.GetConsumable(userData.ActiveConsumableId);

                    // Check if this was the last use of the consumable
                    bool isLastUse = userData.ActiveConsumableUsesRemaining == 0;

                    if (isLastUse)
                    {
                        // This was the last use of the consumable
                        responseMessage = $"**<a:BM_BotChanDance2:1410331782607540245> | {command.User.Username} found the following bots!** **This consumable has run out!**\n**<:Blank:1410316480905609256> |** {botEmojis}";
                    }
                    else if (consumable != null && !string.IsNullOrEmpty(consumable.Emoji))
                    {
                        // Format with the consumable info and emoji
                        responseMessage = $"**<a:BM_BotChanDance2:1410331782607540245> | {command.User.Username} found the following bots!** **Empowered by {consumable.Emoji}!**\n**<:Blank:1410316480905609256> |** {botEmojis}";
                    }
                    else
                    {
                        // Format with just the consumable name if emoji is not available
                        responseMessage = $"**<a:BM_BotChanDance2:1410331782607540245> | {command.User.Username} found the following bots!** **Empowered by {activeConsumableName}!**\n**<:Blank:1410316480905609256> |** {botEmojis}";
                    }
                }
                else
                {
                    // Format without consumable info
                    responseMessage = $"**<a:BM_BotChanDance2:1410331782607540245> | {command.User.Username} found the following bots!**\n**<:Blank:1410316480905609256> |** {botEmojis}";
                }

                // Add tip about consumables if the user has any but no active consumable
                if (userData.Inventory.Count > 0 && string.IsNullOrEmpty(userData.ActiveConsumableId))
                {
                    responseMessage += "\n\n*Tip: Use `/use [consumable_id]` to activate a consumable for more bots!*";
                }

                // Add a message if the user reached the bot limit
                if (!fullAmountAdded)
                {
                    responseMessage += "\n\n**🏆 Congratulations! You've reached the maximum limit of 1,000,000 bots! Any other bots found will be discarded. Use boxes to store them! 🏆**";
                }

                // Send the response
                await command.RespondAsync(responseMessage);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing recruit slash command: {ex.Message}");
                
                if (!command.HasResponded)
                {
                    await command.RespondAsync("❌ An error occurred while processing the recruit command.", ephemeral: true);
                }
            }
        }
    }
}
