using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;

namespace BotChan.Commands
{
    /// <summary>
    /// UseCodeCommand - A command that redeems a code for items
    /// Usage: %usecode [code]
    /// Example: %usecode BOTCHAN2023
    /// </summary>
    public class UseCodeCommand : Command
    {
        // Override the default category
        public override string Category => "Economy";

        // Override the default usage
        public override string Usage => "%usecode [code]";

        // Override the default examples
        public override string Examples => "%usecode NEWBEGINNINGS";

        public UseCodeCommand() : base("usecode", "Redeem a code for items")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Check if the user provided a code
            if (args.Length < 2)
            {
                await message.Channel.SendMessageAsync("Please specify a code to redeem. Example: `%usecode NEWBEGINNINGS`");
                return;
            }

            // Get the code
            string code = args[1];

            // Try to redeem the code
            var result = client.BotSystem.RedeemCode(message.Author.Id, code);

            if (result.Success && result.Consumable != null)
            {
                // Create a success message
                string responseMessage;

                if (result.Consumable.BotReward > 0)
                {
                    // This is a bot reward item
                    int totalBotReward = result.Consumable.BotReward * result.Quantity;
                    responseMessage = $"<a:BM_BotChanHappy:1158391055998259270> ✨ | {message.Author.Username}, you redeemed the code **{code}** and received:\n\n" +
                        $"**{result.Quantity}x {result.Consumable.Name}** {result.Consumable.Emoji}\n" +
                        $"This gives you a total of **{totalBotReward}** <:Bot:1363239519914164264>!\n\n" +
                        $"The item(s) have been added to your inventory. Use `%use [inventory_id]` to use them.";
                }
                else
                {
                    // This is a consumable item
                    responseMessage = $"<a:BM_BotChanHappy:1410317358852997233> ✨ | {message.Author.Username}, you redeemed the code **{code}** and received:\n\n" +
                        $"**{result.Quantity}x {result.Consumable.Name}** {result.Consumable.Emoji}\n" +
                        $"This gives you a **{result.Consumable.Multiplier}x** consumable for **{result.Consumable.DefaultUses}** uses!\n\n" +
                        $"The item(s) have been added to your inventory. Use `%use [inventory_id]` to use them.";
                }

                // Send the response
                await message.Channel.SendMessageAsync(responseMessage);
            }
            else
            {
                // Send the error message
                await message.Channel.SendMessageAsync($"<:BM_BotChanDespair:1410316642180796446> | {message.Author.Username}, {result.ErrorMessage}");
            }
        }
    }
}
