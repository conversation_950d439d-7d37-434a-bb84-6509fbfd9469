using Discord;
using Discord.WebSocket;
using BotChan.Services;
using BotChan.Models;
using System.Text;

namespace BotChan.Commands.SlashCommands
{
    /// <summary>
    /// LeaderboardsSlashCommand - Slash command version of the leaderboards command
    /// Usage: /leaderboards [page]
    /// Shows the global leaderboard of users ranked by bot count
    /// </summary>
    public class LeaderboardsSlashCommand
    {
        // Number of users to display per page
        private const int UsersPerPage = 10;

        /// <summary>
        /// Executes the leaderboards slash command.
        /// </summary>
        /// <param name="command">The slash command that was executed</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task ExecuteAsync(SocketSlashCommand command, DiscordBotClient client)
        {
            try
            {
                // Check cooldown before allowing the command (same as %leaderboards)
                if (!client.Permissions.HasPermission(command.User.Id, "leaderboards", out string cooldownMessage))
                {
                    if (!string.IsNullOrEmpty(cooldownMessage))
                    {
                        await command.RespondAsync($"⏰ **Leaderboards command is on cooldown!** {cooldownMessage}", ephemeral: true);
                    }
                    else
                    {
                        await command.RespondAsync("❌ You don't have permission to use this command.", ephemeral: true);
                    }
                    return;
                }

                // Update last usage time for cooldown tracking
                client.Permissions.UpdateCommandUsage(command.User.Id, "leaderboards");

                // Check if a page number was provided
                var pageOption = command.Data.Options.FirstOrDefault(x => x.Name == "page");
                int page = 1;

                if (pageOption != null)
                {
                    if (!int.TryParse(pageOption.Value.ToString(), out page))
                    {
                        await command.RespondAsync("Please specify a valid page number.", ephemeral: true);
                        return;
                    }
                }

                // Show the leaderboards
                await ShowLeaderboards(command, page, client);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing leaderboards slash command: {ex.Message}");
                
                if (!command.HasResponded)
                {
                    await command.RespondAsync("❌ An error occurred while processing the leaderboards command.", ephemeral: true);
                }
            }
        }

        private static async Task ShowLeaderboards(SocketSlashCommand command, int page, DiscordBotClient client)
        {
            // Get all user data (exact same as %leaderboards)
            var allUsers = client.BotSystem.GetAllUserData()
                .OrderByDescending(u => u.BotCount)
                .ToList();

            // Calculate total pages
            int totalPages = (int)Math.Ceiling(allUsers.Count / (double)UsersPerPage);
            if (totalPages == 0) totalPages = 1; // At least one page even if empty

            // Ensure page is within valid range
            page = Math.Max(1, Math.Min(page, totalPages));

            // Get users for the current page
            var pageUsers = allUsers
                .Skip((page - 1) * UsersPerPage)
                .Take(UsersPerPage)
                .ToList();

            // Find the current user's rank
            int userRank = allUsers.FindIndex(u => u.UserId == command.User.Id) + 1;
            long userBotCount = allUsers.FirstOrDefault(u => u.UserId == command.User.Id)?.BotCount ?? 0;

            // Build the leaderboards message content (exact same as %leaderboards)
            var sb = new StringBuilder();

            // Add navigation instructions if there are multiple pages
            if (totalPages > 1)
            {
                sb.AppendLine($"/leaderboards page [1-{totalPages}] to navigate pages");
            }

            sb.AppendLine("════════════════════════════════");

            // Add the users
            for (int i = 0; i < pageUsers.Count; i++)
            {
                var userData = pageUsers[i];
                int rank = (page - 1) * UsersPerPage + i + 1; // Calculate the rank
                string formattedBotCount = FormatNumber(userData.BotCount);

                // Get the username from stored data
                string username = userData.GetDisplayName();

                // Try to update user info if possible
                var user = client._client.GetUser(userData.UserId);
                if (user != null)
                {
                    // Update the user information in the database
                    client.BotSystem.GetUserData(user);
                    username = user.Username;
                }

                // Format: Rank Username----------------------- BotCount :BotDown:
                // Calculate dashes needed for alignment
                int dashCount = Math.Max(1, 40 - username.Length - formattedBotCount.Length);
                string dashes = new string('-', dashCount);

                sb.AppendLine($"{rank} {username}{dashes} {formattedBotCount} {Constants.Emojis.BotDown}");
            }

            // Add a blank line at the end for spacing
            sb.AppendLine();

            // Create an embed for the leaderboards (exact same as %leaderboards)
            var embed = new EmbedBuilder()
                .WithAuthor("Global Leaderboards", command.User.GetAvatarUrl() ?? command.User.GetDefaultAvatarUrl())
                .WithDescription(sb.ToString())
                .WithColor(Color.Gold)
                .WithFooter($"Page {page}/{totalPages} | You are #{userRank} with {userBotCount} bots");

            // Send the embed message
            await command.RespondAsync(embed: embed.Build());
        }

        /// <summary>
        /// Formats a number with K, M, B suffixes for thousands, millions, billions (exact same as %leaderboards)
        /// </summary>
        private static string FormatNumber(long number)
        {
            if (number >= 1_000_000_000)
                return $"{number / 1_000_000_000}B";
            if (number >= 1_000_000)
                return $"{number / 1_000_000}M";
            if (number >= 1_000)
                return $"{number / 1_000}K";
            return number.ToString();
        }
    }
}
