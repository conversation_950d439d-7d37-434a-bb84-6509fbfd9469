using Discord;
using Discord.WebSocket;
using BotChan.Services;
using System.Reflection;
using System.Text;

namespace BotChan.Commands.SlashCommands
{
    /// <summary>
    /// HelpSlashCommand - Slash command version of the help command
    /// Usage: /help [command]
    /// Displays information about available commands
    /// </summary>
    public class HelpSlashCommand
    {
        // Maximum number of commands to show per page
        private const int CommandsPerPage = 10;

        /// <summary>
        /// Executes the help slash command.
        /// </summary>
        /// <param name="command">The slash command that was executed</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task ExecuteAsync(SocketSlashCommand command, DiscordBotClient client)
        {
            try
            {
                // Get command options
                var commandOption = command.Data.Options.FirstOrDefault(x => x.Name == "command");

                // If a specific command is requested, show detailed help for that command
                if (commandOption != null)
                {
                    string commandName = commandOption.Value.ToString();
                    await ShowCommandHelp(command, commandName, client);
                    return;
                }

                // Otherwise, show the general help menu that lists all commands
                await ShowCommandList(command, client);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing help slash command: {ex.Message}");
                
                if (!command.HasResponded)
                {
                    await command.RespondAsync("❌ An error occurred while processing the help command.", ephemeral: true);
                }
            }
        }

        /// <summary>
        /// Shows a list of all available commands organized by category.
        /// </summary>
        private static async Task ShowCommandList(SocketSlashCommand command, DiscordBotClient client)
        {
            // Find all command classes using reflection
            var commandTypes = Assembly.GetExecutingAssembly().GetTypes()
                .Where(t => t.IsClass && !t.IsAbstract && t.IsSubclassOf(typeof(Commands.Command)))
                .OrderBy(t => t.Name)
                .ToList();

            // Extract command information
            var commands = new List<(string Name, string Description, string Category)>();

            foreach (var type in commandTypes)
            {
                if (Activator.CreateInstance(type) is Commands.Command cmd)
                {
                    commands.Add((cmd.Name, cmd.Description, cmd.Category));
                }
            }

            // Group commands by category
            var commandsByCategory = commands
                .GroupBy(c => c.Category)
                .OrderBy(g => g.Key == "General" ? 0 : 1)
                .ThenBy(g => g.Key)
                .ToList();

            // Build the help message
            var embed = new EmbedBuilder()
                .WithTitle("📚 Bot-Chan Command Help")
                .WithDescription("Here are all the available commands organized by category:")
                .WithColor(Color.Blue)
                .WithThumbnailUrl("https://cdn.discordapp.com/attachments/1363245551184908468/1363389520245952672/botchan_icon.png")
                .WithTimestamp(DateTimeOffset.Now);

            // Add each category as a field
            foreach (var category in commandsByCategory)
            {
                var sb = new StringBuilder();
                
                foreach (var cmd in category.Take(CommandsPerPage))
                {
                    sb.AppendLine($"`/{cmd.Name}` - {cmd.Description}");
                }

                // If there are more commands than can fit, add a note
                if (category.Count() > CommandsPerPage)
                {
                    sb.AppendLine($"... and {category.Count() - CommandsPerPage} more commands");
                }

                embed.AddField($"📂 {category.Key}", sb.ToString(), false);
            }

            // Add footer with usage information
            embed.WithFooter($"Use `/help [command]` or `%help [command]` for detailed help on a specific command");

            await command.RespondAsync(embed: embed.Build());
        }

        /// <summary>
        /// Shows detailed help for a specific command.
        /// </summary>
        private static async Task ShowCommandHelp(SocketSlashCommand command, string commandName, DiscordBotClient client)
        {
            // Try to find the command in the registered commands
            if (client._commands.TryGetValue(commandName.ToLower(), out var cmd))
            {
                var embed = new EmbedBuilder()
                    .WithTitle($"📖 Help for `/{cmd.Name}`")
                    .WithDescription(cmd.Description)
                    .WithColor(Color.Green)
                    .WithThumbnailUrl("https://cdn.discordapp.com/attachments/1363245551184908468/1363389520245952672/botchan_icon.png")
                    .WithTimestamp(DateTimeOffset.Now);

                // Add usage information (convert % to / for slash commands)
                if (!string.IsNullOrEmpty(cmd.Usage))
                {
                    string slashUsage = cmd.Usage.Replace("%", "/");
                    embed.AddField("📝 Usage", $"`{slashUsage}`", false);
                }

                // Add examples (convert % to / for slash commands)
                if (!string.IsNullOrEmpty(cmd.Examples))
                {
                    string slashExamples = cmd.Examples.Replace("%", "/");
                    embed.AddField("💡 Examples", $"`{slashExamples}`", false);
                }

                // Add category
                embed.AddField("📂 Category", cmd.Category, true);

                // Add aliases if any (skip for now as AliasCommand structure is unclear)
                // if (cmd is Commands.AliasCommand aliasCmd)
                // {
                //     embed.AddField("🔗 Alias for", aliasCmd.TargetCommand, true);
                // }

                await command.RespondAsync(embed: embed.Build());
            }
            else
            {
                // Command not found
                var embed = new EmbedBuilder()
                    .WithTitle("❌ Command Not Found")
                    .WithDescription($"The command `{commandName}` was not found.")
                    .WithColor(Color.Red)
                    .WithTimestamp(DateTimeOffset.Now);

                embed.AddField("💡 Suggestion", $"Use `/help` to see all available commands, or check your spelling.");

                await command.RespondAsync(embed: embed.Build(), ephemeral: true);
            }
        }
    }
}
