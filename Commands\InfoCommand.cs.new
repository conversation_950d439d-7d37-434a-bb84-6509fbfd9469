using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;
using System.Text;

namespace BotChan.Commands
{
    /// <summary>
    /// InfoCommand - Displays detailed information about items
    /// Usage: %info [item_id]
    /// Example: %info 1
    /// </summary>
    public class InfoCommand : Command
    {
        // Override the default category
        public override string Category => "Economy";

        // Override the default usage
        public override string Usage => "%info [item_id]";

        // Override the default examples
        public override string Examples => "%info 1\n%info 2";

        public InfoCommand() : base("info", "View detailed information about an item")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            try
            {
                // Get all consumables that are visible in the shop
                var consumables = client.BotSystem.GetAllConsumables().Where(c => c.IsVisibleInShop).ToList();

                // Log the number of consumables found
                Console.WriteLine($"INFO COMMAND: Found {consumables.Count} visible consumables in the shop");

                // Debug: Print all consumables
                for (int i = 0; i < consumables.Count; i++)
                {
                    Console.WriteLine($"INFO COMMAND: Consumable #{i+1}: {consumables[i].Name} (ID: {consumables[i].Id})");
                }

                // Check if an item ID was provided
                if (args.Length < 2)
                {
                    Console.WriteLine("INFO COMMAND: No item ID provided, showing item list");
                    await ShowItemList(message, client);
                    return;
                }

                Console.WriteLine($"INFO COMMAND: Args provided: {string.Join(", ", args)}");

                // Try to parse the item ID (args[1] is the first argument after the command name)
                if (!int.TryParse(args[1], out int itemId))
                {
                    Console.WriteLine($"INFO COMMAND: Failed to parse item ID: {args[1]}");
                    await message.Channel.SendMessageAsync($"<:BM_BotChanReaction:1143303450369736754> | **{message.Author.Username}**, please provide a valid item ID. Use `%info` to see all available items.");
                    return;
                }

                if (itemId < 1 || itemId > consumables.Count)
                {
                    Console.WriteLine($"INFO COMMAND: Item ID out of range: {itemId} (valid range: 1-{consumables.Count})");
                    await message.Channel.SendMessageAsync($"<:BM_BotChanReaction:1143303450369736754> | **{message.Author.Username}**, please provide a valid item ID between 1 and {consumables.Count}. Use `%info` to see all available items.");
                    return;
                }

                // Get the consumable (adjust for 0-based index)
                var consumable = consumables[itemId - 1];
                Console.WriteLine($"INFO COMMAND: Found consumable: {consumable.Name} (ID: {consumable.Id})");

                // Show the item details
                await ShowItemDetails(message, consumable, itemId, client);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"INFO COMMAND ERROR: {ex.Message}\n{ex.StackTrace}");
                await message.Channel.SendMessageAsync($"<:BM_BotChanReaction:1143303450369736754> | **{message.Author.Username}**, an error occurred while processing your request. Please try again later.");
            }
        }

        /// <summary>
        /// Shows a list of all available items.
        /// </summary>
        private async Task ShowItemList(SocketMessage message, DiscordBotClient client)
        {
            try
            {
                // Get all consumables that are visible in the shop
                var consumables = client.BotSystem.GetAllConsumables().Where(c => c.IsVisibleInShop).ToList();
                Console.WriteLine($"INFO COMMAND: ShowItemList called with {consumables.Count} visible consumables");

                if (consumables.Count == 0)
                {
                    Console.WriteLine("INFO COMMAND: No consumables available");
                    await message.Channel.SendMessageAsync($"<:BM_BotChanReaction:1143303450369736754> | **{message.Author.Username}**, there are no items available in the shop yet.");
                    return;
                }

                // Create the embed
                var embed = new EmbedBuilder()
                    .WithTitle("Bot-Chan's Secret Armory")
                    .WithDescription("Use `%info [item_id]` to view detailed information about an item.")
                    .WithColor(Color.Gold)
                    .WithThumbnailUrl("https://cdn.discordapp.com/attachments/1363245551184908468/1363389520245952672/botchan_icon.png?ex=6805daf0&is=68048970&hm=38ddf686a16b86789e8b2e80f7db77ca40b0daa4ef0624fae373af3d0aeae0f7&");

                // Add each consumable to the embed
                for (int i = 0; i < consumables.Count; i++)
                {
                    var consumable = consumables[i];
                    int displayId = i + 1; // 1-based index for display
                    string itemType = consumable.BotReward > 0 ? "Bot Pack" : "Multiplier";

                    Console.WriteLine($"INFO COMMAND: Adding consumable to list: {consumable.Id} with name {consumable.Name} as item #{displayId}");

                    embed.AddField(
                        $"{displayId}. {consumable.Name} {consumable.Emoji} ({itemType})",
                        $"Cost: {consumable.Cost} {Constants.Emojis.BotDown}\n" +
                        $"Type `%info {displayId}` for details"
                    );
                }

                Console.WriteLine("INFO COMMAND: Sending embed with item list");
                var sentMessage = await message.Channel.SendMessageAsync(embed: embed.Build());
                Console.WriteLine($"INFO COMMAND: Embed sent successfully, message ID: {sentMessage.Id}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"INFO COMMAND ERROR in ShowItemList: {ex.Message}\n{ex.StackTrace}");
                await message.Channel.SendMessageAsync($"<:BM_BotChanReaction:1143303450369736754> | **{message.Author.Username}**, an error occurred while showing the item list. Please try again later.");
            }
        }

        /// <summary>
        /// Shows detailed information about a specific item.
        /// </summary>
        private async Task ShowItemDetails(SocketMessage message, Consumable consumable, int displayId, DiscordBotClient client)
        {
            try
            {
                // Get the user data to check if they own this item
                var userData = client.BotSystem.GetUserData(message.Author.Id);
                int ownedCount = userData.Inventory.Count(i => i.ConsumableId == consumable.Id);

                Console.WriteLine($"INFO COMMAND: Showing details for item #{displayId}: {consumable.Name} (ID: {consumable.Id})");

                // Determine item type and effect description
                string itemType;
                string effectDescription;
                string usageInstructions;
                
                if (consumable.BotReward > 0)
                {
                    // This is a bot reward item
                    itemType = "Bot Pack";
                    effectDescription = $"Gives **{consumable.BotReward}** bots instantly";
                    usageInstructions = $"1. Purchase with `%shop buy {displayId}`\n" +
                                       $"2. Activate with `%use [inventory_id]` (check your inventory with `%inventory`)\n" +
                                       $"3. Receive **{consumable.BotReward}** bots immediately!";
                }
                else
                {
                    // This is a multiplier item
                    int boostPercentage = (int)((consumable.Multiplier - 1) * 100);
                    itemType = "Multiplier";
                    effectDescription = $"Increases bot gains by **{boostPercentage}%**";
                    usageInstructions = $"1. Purchase with `%shop buy {displayId}`\n" +
                                       $"2. Activate with `%use [inventory_id]` (check your inventory with `%inventory`)\n" +
                                       $"3. Enjoy **{boostPercentage}%** more bots for **{consumable.DefaultUses}** uses!";
                }

                // Create the embed
                var embed = new EmbedBuilder()
                    .WithTitle($"{consumable.Name} {consumable.Emoji}")
                    .WithDescription(consumable.Description)
                    .WithColor(Color.Gold)
                    .WithThumbnailUrl(message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl());

                // Add item details
                embed.AddField("Item ID", displayId, true);
                embed.AddField("Type", itemType, true);
                embed.AddField("Cost", $"{consumable.Cost} {Constants.Emojis.BotDown}", true);
                embed.AddField("Effect", effectDescription, true);
                embed.AddField("Duration", $"{consumable.DefaultUses} uses", true);
                embed.AddField("You Own", $"{ownedCount} copies", true);

                // Add usage instructions
                var sb = new StringBuilder();
                sb.AppendLine("**How to use:**");
                sb.AppendLine(usageInstructions);

                // For multiplier items, show if it's currently active
                if (consumable.BotReward == 0 && userData.ActiveConsumableId == consumable.Id)
                {
                    sb.AppendLine();
                    sb.AppendLine($"**Currently Active!** {userData.ActiveConsumableUsesRemaining} uses remaining.");
                }

                embed.AddField("Usage", sb.ToString());

                Console.WriteLine("INFO COMMAND: Sending embed with item details");
                var sentMessage = await message.Channel.SendMessageAsync(embed: embed.Build());
                Console.WriteLine($"INFO COMMAND: Item details sent successfully, message ID: {sentMessage.Id}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"INFO COMMAND ERROR in ShowItemDetails: {ex.Message}\n{ex.StackTrace}");
                await message.Channel.SendMessageAsync($"<:BM_BotChanReaction:1143303450369736754> | **{message.Author.Username}**, an error occurred while showing the item details. Please try again later.");
            }
        }
    }
}
