using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;

namespace BotChan.Commands
{
    /// <summary>
    /// BombCommand - Allows users to attack other players using bomb items
    /// Usage: %bomb [target_user_id_or_name] [inventory_item_id]
    /// Example: %bomb 123456789012345678 1
    /// </summary>
    public class BombCommand : Command
    {
        // Override the default category
        public override string Category => "Economy";

        // Override the default usage
        public override string Usage => "%bomb [target_user_id_or_name] [inventory_item_id]";

        // Override the default examples
        public override string Examples => "%bomb 123456789012345678 1\n%bomb @username 2";

        // Random number generator for bomb outcomes
        private static readonly Random _random = new Random();

        public BombCommand() : base("bomb", "Attack another player's territory using bomb items")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Check if the user provided the required arguments
            if (args.Length < 3)
            {
                await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1410316642180796446> | {message.Author.Username}**, please provide a target user ID/name and inventory item ID. Usage: `%bomb [target_user_id_or_name] [inventory_item_id]`");
                return;
            }

            // Get the user's data
            var userData = client.BotSystem.GetUserData(message.Author.Id, message.Author.Username);

            // Parse the inventory item ID
            if (!int.TryParse(args[2], out int inventoryId))
            {
                await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1410316642180796446> | {message.Author.Username}**, please provide a valid inventory item ID number.");
                return;
            }

            // Get the bomb item from user's inventory
            var bombItem = userData.GetConsumableByInventoryId(inventoryId);
            if (bombItem == null)
            {
                await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1410316642180796446> | {message.Author.Username}**, you don't have an item with inventory ID {inventoryId}. Use `%inventory` to see your items.");
                return;
            }

            // Get the bomb consumable details
            var bombConsumable = client.BotSystem.GetConsumable(bombItem.ConsumableId);
            if (bombConsumable == null || bombConsumable.Category != 3)
            {
                await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1410316642180796446> | {message.Author.Username}**, the item with inventory ID {inventoryId} is not a bomb item. Only bomb items can be used with this command.");
                return;
            }

            // Parse the target user ID
            string target = args[1];
            ulong targetUserId;

            // Try to parse as user ID first
            if (!ulong.TryParse(target, out targetUserId))
            {
                // If not a user ID, try to find by username mention or name
                // For now, we'll require user ID for simplicity
                await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1410316642180796446> | {message.Author.Username}**, please provide a valid user ID.");
                return;
            }

            // Check if this is a gift (negative BotReward) or an attack
            if (bombConsumable.BotReward < 0)
            {
                // This is a gift - allow targeting yourself and don't require minimum bots
                await ExecuteBombGift(message, client, userData, targetUserId, bombItem, bombConsumable);
                return;
            }

            // Get the target user's data
            var targetUserData = client.BotSystem.GetUserData(targetUserId);

            // Check if bombing yourself or others
            bool isSelfBombing = targetUserId == message.Author.Id;

            if (!isSelfBombing && targetUserData.BotCount < 100)
            {
                await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1410316642180796446> | {message.Author.Username}**, that user doesn't have enough bots to bomb (minimum 100 required).");
                return;
            }

            // Execute the bomb attack (self-bombing has 100% success rate)
            await ExecuteBombAttack(message, client, userData, targetUserData, bombItem, bombConsumable, targetUserId, isSelfBombing);
        }

        /// <summary>
        /// Executes a bot gift using a negative BotReward bomb item
        /// </summary>
        private async Task ExecuteBombGift(SocketMessage message, DiscordBotClient client, UserData giverData, ulong targetUserId, UserConsumable giftItem, Consumable giftConsumable)
        {
            // Remove the gift item from giver's inventory (it's consumed)
            giverData.Inventory.Remove(giftItem);

            // Calculate the number of bots to give (negative BotReward becomes positive gift)
            long botsToGive = Math.Abs(giftConsumable.BotReward);

            // Gift bombs don't require additional bots from the giver - the bots are already "paid for"
            // when purchasing the gift bomb from the shop. Simply give the bots to the target.
            client.BotSystem.AddBots(targetUserId, botsToGive);

            // Get updated data
            giverData = client.BotSystem.GetUserData(message.Author.Id);
            var targetData = client.BotSystem.GetUserData(targetUserId);

            // Get target user for display name
            var targetUser = client._client.GetUser(targetUserId);
            string targetName = targetUser?.Username ?? targetData.GetDisplayName();

            // Check if giving to self
            bool givingToSelf = targetUserId == message.Author.Id;

            // Create gift embed
            var giftEmbed = new EmbedBuilder()
                .WithTitle($"🎁 Bot Gift Delivered!")
                .WithDescription(givingToSelf ?
                    $"**{message.Author.Username}** gave themselves **{botsToGive:N0}** bots!" :
                    $"**{message.Author.Username}** gave **{targetName}** **{botsToGive:N0}** bots!")
                .WithColor(Color.Green)
                .AddField("Gift Item Used", $"{giftConsumable.Name} {giftConsumable.Emoji}", true)
                .AddField("Bots Transferred", $"{botsToGive:N0} {Constants.Emojis.BotDown}", true)
                .AddField(givingToSelf ? "Your Bot Count" : $"{targetName}'s New Bot Count", $"{targetData.BotCount:N0} {Constants.Emojis.BotDown}", true)
                .WithThumbnailUrl("https://cdn.discordapp.com/attachments/1363245551184908468/1363320873498447923/Bot_Army.png")
                .WithTimestamp(DateTimeOffset.Now);

            await message.Channel.SendMessageAsync(embed: giftEmbed.Build());
        }

        /// <summary>
        /// Executes the bomb attack with success/failure mechanics
        /// </summary>
        private async Task ExecuteBombAttack(SocketMessage message, DiscordBotClient client, UserData attackerData, UserData targetData, UserConsumable bombItem, Consumable bombConsumable, ulong targetUserId, bool isSelfBombing = false)
        {
            // Remove the bomb item from attacker's inventory (it's consumed regardless of outcome)
            attackerData.Inventory.Remove(bombItem);

            // Determine if the bomb attack succeeds (self-bombing always succeeds)
            bool bombSucceeds;
            if (isSelfBombing)
            {
                bombSucceeds = true; // 100% success rate for self-bombing
            }
            else
            {
                int successRoll = _random.Next(1, 101); // 1-100
                bombSucceeds = successRoll <= bombConsumable.BombSuccessRate;
            }

            // Get target user for display name
            var targetUser = client._client.GetUser(targetUserId);
            string targetName = targetUser?.Username ?? targetData.GetDisplayName();

            if (bombSucceeds)
            {
                // Calculate damage to target using fixed win percentage
                long botsDestroyed = (long)Math.Ceiling(targetData.BotCount * (bombConsumable.BombWinPercentage / 100.0));
                botsDestroyed = Math.Max(1, Math.Min(botsDestroyed, targetData.BotCount));

                // Apply damage to target
                client.BotSystem.RemoveBots(targetUserId, botsDestroyed);

                // Get updated target data
                targetData = client.BotSystem.GetUserData(targetUserId);

                // Create success embed
                var successEmbed = new EmbedBuilder()
                    .WithTitle($"💥 Bomb Attack Successful!")
                    .WithDescription(isSelfBombing ?
                        $"**{message.Author.Username}** bombed their own territory for bot storage!" :
                        $"**{message.Author.Username}** successfully bombed **{targetName}**'s territory!")
                    .WithColor(Color.Orange)
                    .AddField("Bomb Used", $"{bombConsumable.Name} {bombConsumable.Emoji}", true)
                    .AddField("Success Rate", isSelfBombing ? "100% (Self-bombing)" : $"{bombConsumable.BombSuccessRate}%", true)
                    .AddField("Damage Dealt", $"{bombConsumable.BombWinPercentage}% ({botsDestroyed:N0} bots destroyed)", true)
                    .AddField("Target's Remaining Bots", $"{targetData.BotCount:N0} {Constants.Emojis.BotDown}", true)
                    .WithThumbnailUrl("https://cdn.discordapp.com/attachments/1363245551184908468/1363320873498447923/Bot_Army.png")
                    .WithTimestamp(DateTimeOffset.Now);

                await message.Channel.SendMessageAsync(embed: successEmbed.Build());
            }
            else
            {
                // Bomb failed - apply penalty to attacker using fixed lose percentage
                long penaltyBots = (long)Math.Ceiling(attackerData.BotCount * (bombConsumable.BombLosePercentage / 100.0));
                penaltyBots = Math.Max(1, Math.Min(penaltyBots, attackerData.BotCount));

                if (attackerData.BotCount >= penaltyBots)
                {
                    client.BotSystem.RemoveBots(message.Author.Id, penaltyBots);
                }

                // Get updated attacker data
                attackerData = client.BotSystem.GetUserData(message.Author.Id);

                // Create failure embed
                var failureEmbed = new EmbedBuilder()
                    .WithTitle($"💨 Bomb Attack Failed!")
                    .WithDescription($"**{message.Author.Username}**'s bomb attack on **{targetName}** was unsuccessful!")
                    .WithColor(Color.Red)
                    .AddField("Bomb Used", $"{bombConsumable.Name} {bombConsumable.Emoji}", true)
                    .AddField("Success Rate", $"{bombConsumable.BombSuccessRate}%", true)
                    .AddField("Penalty Applied", $"{bombConsumable.BombLosePercentage}% ({penaltyBots:N0} bots lost)", true)
                    .AddField("Your Remaining Bots", $"{attackerData.BotCount:N0} {Constants.Emojis.BotDown}", true)
                    .WithThumbnailUrl("https://cdn.discordapp.com/attachments/1363245551184908468/1363320873498447923/Bot_Army.png")
                    .WithTimestamp(DateTimeOffset.Now);

                await message.Channel.SendMessageAsync(embed: failureEmbed.Build());
            }

            // Save all changes
            client.BotSystem.SaveUserData();
        }
    }
}
