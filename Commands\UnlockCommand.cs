using Discord;
using Discord.WebSocket;
using BotChan.Services;

namespace BotChan.Commands
{
    /// <summary>
    /// UnlockCommand - Deactivates emergency lockdown mode, restoring normal command access
    /// Usage: %unlock
    /// Example: %unlock
    /// </summary>
    public class UnlockCommand : Command
    {
        // Override the default category
        public override string Category => "Admin";

        // Override the default usage
        public override string Usage => "%unlock";

        // Override the default examples
        public override string Examples => "%unlock";

        // This command is exempt from lockdown restrictions
        public override bool IsExemptFromLockdown => true;

        public UnlockCommand() : base("unlock", "Deactivates emergency lockdown mode (staff only)")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Check if the user is a staff member
            if (!client.Permissions.IsStaffUser(message.Author.Id))
            {
                await message.Channel.SendMessageAsync("You don't have permission to use this command.");
                return;
            }

            // Check if the bot is in lockdown
            if (!client.BotSystem.IsInLockdown())
            {
                await message.Channel.SendMessageAsync("The bot is not currently in lockdown mode.");
                return;
            }

            // Get lockdown info before deactivating
            var lockdownInfo = client.BotSystem.GetLockdownInfo();
            var activator = client._client.GetUser(lockdownInfo.ActivatedBy);
            string activatorName = activator != null ? activator.Username : "Unknown User";

            // Calculate lockdown duration
            TimeSpan duration = DateTime.UtcNow - lockdownInfo.ActivatedAt;
            string durationText = duration.TotalHours >= 1
                ? $"{duration.TotalHours:0.0} hours"
                : $"{duration.TotalMinutes:0.0} minutes";

            // Deactivate lockdown
            client.BotSystem.DeactivateLockdown();

            // Create an embed for the response
            var embed = new EmbedBuilder()
                .WithTitle("🔓 Emergency Lockdown Deactivated")
                .WithDescription("The bot has been returned to normal operation. All users can now use commands again.")
                .WithColor(Color.Green)
                .AddField("Deactivated By", message.Author.Username, true)
                .AddField("Previously Activated By", activatorName, true)
                .AddField("Lockdown Duration", durationText, true);

            if (!string.IsNullOrEmpty(lockdownInfo.Reason))
            {
                embed.AddField("Lockdown Reason", lockdownInfo.Reason);
            }

            await message.Channel.SendMessageAsync(embed: embed.Build());
        }
    }
}
