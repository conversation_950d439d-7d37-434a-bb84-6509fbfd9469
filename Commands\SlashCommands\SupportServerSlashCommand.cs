using Discord;
using Discord.WebSocket;
using BotChan.Services;

namespace BotChan.Commands.SlashCommands
{
    /// <summary>
    /// SupportServerSlashCommand - Slash command version of the support server command
    /// Usage: /supportserver
    /// Shows the support server link with silent mode support
    /// </summary>
    public class SupportServerSlashCommand
    {
        /// <summary>
        /// Executes the support server slash command.
        /// </summary>
        /// <param name="command">The slash command that was executed</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task ExecuteAsync(SocketSlashCommand command, DiscordBotClient client)
        {
            try
            {
                // Create the embed (exact same as %supportserver)
                var embed = new EmbedBuilder()
                    .WithTitle("🛠️ Support Server")
                    .WithDescription("Need help or want to join our community?")
                    .AddField("Support Server", "Our support server is here: https://discord.gg/DHbChVj")
                    .AddField("What you can find there:", 
                        "• Get help with bot commands\n" +
                        "• Report bugs and issues\n" +
                        "• Suggest new features\n" +
                        "• Chat with other players\n" +
                        "• Get updates and announcements")
                    .WithColor(Color.Blue)
                    .WithFooter("Click the link above to join!")
                    .WithTimestamp(DateTimeOffset.Now);

                // Respond with the embed
                await command.RespondAsync(embed: embed.Build());
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing support server slash command: {ex.Message}");

                if (!command.HasResponded)
                {
                    await command.RespondAsync("❌ An error occurred while retrieving the support server information.", ephemeral: true);
                }
            }
        }
    }
}
