using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;
using System.Text;

namespace BotChan.Commands
{
    /// <summary>
    /// InfoCommand - Displays detailed information about items
    /// Usage: %info [item_id]
    /// Example: %info 1
    /// </summary>
    public class InfoCommand : Command
    {
        // Override the default category
        public override string Category => "Economy";

        // Override the default usage
        public override string Usage => "%info [item_id]";

        // Override the default examples
        public override string Examples => "%info 1\n%info 2";

        public InfoCommand() : base("info", "View detailed information about an item")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            try
            {
                // Get all consumables that are visible in the shop, ordered by cost for consistency
                var consumables = client.BotSystem.GetAllConsumables().Where(c => c.IsVisibleInShop).OrderBy(c => c.Cost).ToList();

                // Log the number of consumables found
                Console.WriteLine($"INFO COMMAND: Found {consumables.Count} visible consumables in the shop");

                // Debug: Print all consumables
                for (int i = 0; i < consumables.Count; i++)
                {
                    Console.WriteLine($"INFO COMMAND: Consumable #{i+1}: {consumables[i].Name} (ID: {consumables[i].Id})");
                }

                // Check if arguments were provided
                if (args.Length < 2)
                {
                    Console.WriteLine("INFO COMMAND: No arguments provided, showing item list page 1");
                    await ShowItemList(message, client, consumables, 1);
                    return;
                }

                Console.WriteLine($"INFO COMMAND: Args provided: {string.Join(", ", args)}");

                // Check if this is a page request
                if (args[1].ToLower() == "page" && args.Length >= 3)
                {
                    if (!int.TryParse(args[2], out int pageNumber))
                    {
                        await message.Channel.SendMessageAsync($"<:BM_BotChanReaction:1143303450369736754> | **{message.Author.Username}**, please provide a valid page number. Example: `%info page 2`");
                        return;
                    }
                    Console.WriteLine($"INFO COMMAND: Page request for page {pageNumber}");
                    await ShowItemList(message, client, consumables, pageNumber);
                    return;
                }

                // Try to parse the item ID (args[1] is the first argument after the command name)
                if (!int.TryParse(args[1], out int itemId))
                {
                    Console.WriteLine($"INFO COMMAND: Failed to parse item ID: {args[1]}");
                    await message.Channel.SendMessageAsync($"<:BM_BotChanReaction:1143303450369736754> | **{message.Author.Username}**, please provide a valid item ID or use `%info page [number]` for pagination. Use `%info` to see all available items.");
                    return;
                }

                if (itemId < 1 || itemId > consumables.Count)
                {
                    Console.WriteLine($"INFO COMMAND: Item ID out of range: {itemId} (valid range: 1-{consumables.Count})");
                    await message.Channel.SendMessageAsync($"<:BM_BotChanReaction:1143303450369736754> | **{message.Author.Username}**, please provide a valid item ID between 1 and {consumables.Count}. Use `%info` to see all available items.");
                    return;
                }

                // Get the consumable (adjust for 0-based index)
                var consumable = consumables[itemId - 1];
                Console.WriteLine($"INFO COMMAND: Found consumable: {consumable.Name} (ID: {consumable.Id})");

                // Show the item details
                await ShowItemDetails(message, consumable, itemId, client);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"INFO COMMAND ERROR: {ex.Message}\n{ex.StackTrace}");
                await message.Channel.SendMessageAsync($"<:BM_BotChanReaction:1143303450369736754> | **{message.Author.Username}**, an error occurred while processing your request. Please try again later.");
            }
        }

        /// <summary>
        /// Shows a paginated list of available items.
        /// </summary>
        private async Task ShowItemList(SocketMessage message, DiscordBotClient client, List<Consumable> consumables, int page = 1)
        {
            try
            {
                // Consumables list is already sorted by cost from the main method
                Console.WriteLine($"INFO COMMAND: ShowItemList called with {consumables.Count} visible consumables, page {page}");

                if (consumables.Count == 0)
                {
                    Console.WriteLine("INFO COMMAND: No consumables available");
                    await message.Channel.SendMessageAsync($"<:BM_BotChanReaction:1143303450369736754> | **{message.Author.Username}**, there are no items available in the shop yet.");
                    return;
                }

                // Calculate pagination
                const int itemsPerPage = 10; // Standardized pagination
                int totalPages = (int)Math.Ceiling((double)consumables.Count / itemsPerPage);
                page = Math.Max(1, Math.Min(page, totalPages));

                var pageConsumables = consumables
                    .Skip((page - 1) * itemsPerPage)
                    .Take(itemsPerPage)
                    .ToList();

                // Build description with shop-style formatting
                var description = new StringBuilder();
                description.AppendLine("**Available Items:**");
                description.AppendLine("Use `%info [item_id]` to get detailed information about a specific item.");
                description.AppendLine($"Use `%info page [1-{totalPages}]` to navigate pages.");
                description.AppendLine("════════════════════════════════");

                // Add items with shop-style formatting and dashes
                int displayIndex = (page - 1) * itemsPerPage + 1;
                foreach (var consumable in pageConsumables)
                {
                    string costDisplay = FormatNumber(consumable.Cost);
                    string itemLine = $"{displayIndex} {consumable.Emoji} {consumable.Name}";

                    // Calculate dashes for alignment (same as shop)
                    int targetWidth = 40; // Target width for alignment
                    int currentLength = GetDisplayLength(itemLine);
                    int dashCount = Math.Max(1, targetWidth - currentLength);
                    string dashes = new string('-', dashCount);

                    description.AppendLine($"{itemLine}{dashes} {costDisplay} {Constants.Emojis.BotDown}");

                    Console.WriteLine($"INFO COMMAND: Adding consumable to list: {consumable.Id} with name {consumable.Name} as item #{displayIndex}");
                    displayIndex++;
                }

                // Create the embed
                var embed = new EmbedBuilder()
                    .WithAuthor("Bot-Chan's Item Database", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                    .WithTitle("📋 Available Items")
                    .WithDescription(description.ToString())
                    .WithColor(Color.Blue)
                    .WithFooter($"Page {page}/{totalPages} | Use %info [item_id] for detailed information")
                    .WithTimestamp(DateTimeOffset.Now);

                Console.WriteLine("INFO COMMAND: Sending embed with item list");
                var sentMessage = await message.Channel.SendMessageAsync(embed: embed.Build());
                Console.WriteLine($"INFO COMMAND: Embed sent successfully, message ID: {sentMessage.Id}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"INFO COMMAND ERROR in ShowItemList: {ex.Message}\n{ex.StackTrace}");
                await message.Channel.SendMessageAsync($"<:BM_BotChanReaction:1143303450369736754> | **{message.Author.Username}**, an error occurred while showing the item list. Please try again later.");
            }
        }

        /// <summary>
        /// Shows detailed information about a specific item.
        /// </summary>
        private async Task ShowItemDetails(SocketMessage message, Consumable consumable, int displayId, DiscordBotClient client)
        {
            try
            {
                // Get the user data to check if they own this item
                var userData = client.BotSystem.GetUserData(message.Author.Id);
                int ownedCount = userData.Inventory.Count(i => i.ConsumableId == consumable.Id);

                Console.WriteLine($"INFO COMMAND: Showing details for item #{displayId}: {consumable.Name} (ID: {consumable.Id})");

                // Determine item type and effect description
                string itemType;
                string effectDescription;
                string usageInstructions;
                
                if (consumable.Category == 2) // Bot Pack
                {
                    itemType = "Bot Pack/Vanity Item";
                    effectDescription = $"Gives **{consumable.BotReward}** bots instantly and can be stored on your inventory!";
                    usageInstructions = $"1. Purchase with `%shop buy {displayId}`\n" +
                                       $"2. Activate with `%use [inventory_id]` (check your inventory with `%inventory`)\n" +
                                       $"3. Receive **{consumable.BotReward}** bots immediately!";
                }
                else if (consumable.Category == 3) // Bomb
                {
                    if (consumable.BotReward < 0) // Gift Bomb
                    {
                        itemType = "Gift Bomb";
                        long botsToGive = Math.Abs(consumable.BotReward);
                        effectDescription = $"Gives **{botsToGive:N0}** bots to another player (or yourself)";
                        usageInstructions = $"1. Purchase with `%shop buy {displayId}`\n" +
                                           $"2. Use with `%bomb [target_user_id] [inventory_id]`\n" +
                                           $"3. Transfers **{botsToGive:N0}** bots from you to the target\n" +
                                           $"4. Perfect for alliances and helping other players!";
                    }
                    else // Regular Attack Bomb
                    {
                        itemType = "Bomb";
                        effectDescription = $"Attacks enemy territories with **{consumable.BombSuccessRate}%** success rate";
                        usageInstructions = $"1. Purchase with `%shop buy {displayId}`\n" +
                                           $"2. Use with `%bomb [target_user_id] [inventory_id]`\n" +
                                           $"3. **Success:** Destroys {consumable.BombWinPercentage}% of enemy bots\n" +
                                           $"4. **Failure:** You lose {consumable.BombLosePercentage}% of your own bots";
                    }
                }
                else // Consumable (Category 1)
                {
                    int multiplierPercentage = (int)(consumable.Multiplier * 100);
                    itemType = "Consumable";
                    effectDescription = $"Provides **{multiplierPercentage}%** consumable to bot recruitment";
                    usageInstructions = $"1. Purchase with `%shop buy {displayId}`\n" +
                                       $"2. Activate with `%use [inventory_id]` (check your inventory with `%inventory`)\n" +
                                       $"3. Enjoy **{multiplierPercentage}%** consumable to bot recruitment for **{consumable.DefaultUses}** uses!";
                }

                // Create the embed
                var embed = new EmbedBuilder()
                    .WithTitle($"{consumable.Name} {consumable.Emoji}")
                    .WithDescription(consumable.Description)
                    .WithColor(Color.Gold)
                    .WithThumbnailUrl(message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl());

                // Add item details
                embed.AddField("Item ID", displayId, true);
                embed.AddField("Type", itemType, true);
                embed.AddField("Cost", $"{consumable.Cost} {Constants.Emojis.BotDown}", true);
                embed.AddField("Effect", effectDescription, true);
                embed.AddField("Duration", $"{consumable.DefaultUses} uses", true);
                embed.AddField("You Own", $"{ownedCount} copies", true);

                // Add usage instructions
                var sb = new StringBuilder();
                sb.AppendLine("**How to use:**");
                sb.AppendLine(usageInstructions);

                // For consumable items, show if it's currently active
                if (consumable.BotReward == 0 && userData.ActiveConsumableId == consumable.Id)
                {
                    sb.AppendLine();
                    sb.AppendLine($"**Currently Active!** {userData.ActiveConsumableUsesRemaining} uses remaining.");
                }

                embed.AddField("Usage", sb.ToString());

                Console.WriteLine("INFO COMMAND: Sending embed with item details");
                var sentMessage = await message.Channel.SendMessageAsync(embed: embed.Build());
                Console.WriteLine($"INFO COMMAND: Item details sent successfully, message ID: {sentMessage.Id}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"INFO COMMAND ERROR in ShowItemDetails: {ex.Message}\n{ex.StackTrace}");
                await message.Channel.SendMessageAsync($"<:BM_BotChanReaction:1143303450369736754> | **{message.Author.Username}**, an error occurred while showing the item details. Please try again later.");
            }
        }

        /// <summary>
        /// Calculates the display length of a string, treating Discord custom emojis as single characters.
        /// </summary>
        private int GetDisplayLength(string text)
        {
            if (string.IsNullOrEmpty(text))
                return 0;

            // Replace Discord custom emojis with single characters for length calculation
            // Discord custom emojis have the format <:name:id> or <a:name:id>
            var customEmojiPattern = @"<a?:[^:]+:\d+>";
            var withoutCustomEmojis = System.Text.RegularExpressions.Regex.Replace(text, customEmojiPattern, "E");

            return withoutCustomEmojis.Length;
        }

        /// <summary>
        /// Formats a number with appropriate suffixes (K, M, B, T) - matches shop formatting
        /// </summary>
        private string FormatNumber(long number)
        {
            if (number >= 1_000_000_000_000) // Trillion
                return $"{number / 1_000_000_000_000}T";
            if (number >= 1_000_000_000) // Billion
                return $"{number / 1_000_000_000}B";
            if (number >= 1_000_000) // Million
                return $"{number / 1_000_000}M";
            if (number >= 1_000) // Thousand
                return $"{number / 1_000}K";
            return number.ToString();
        }
    }
}
