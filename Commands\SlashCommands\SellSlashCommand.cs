using Discord;
using Discord.WebSocket;
using BotChan.Services;
using BotChan.Models;
using System.Text;

namespace BotChan.Commands.SlashCommands
{
    /// <summary>
    /// SellSlashCommand - Slash command version of the sell command
    /// Usage: /sell item_id
    /// Sell items from your inventory for 50% of their original price
    /// </summary>
    public class SellSlashCommand
    {
        // Dictionary to track pending sell confirmations
        private static readonly Dictionary<ulong, (int InventoryId, string ItemName, string ItemEmoji, long SellPrice, DateTime Expiry)> _pendingConfirmations = new Dictionary<ulong, (int, string, string, long, DateTime)>();

        // Timeout for confirmations (30 seconds)
        private const int ConfirmationTimeoutSeconds = 30;

        // Static reference to the client for button handling
        private static DiscordBotClient? _clientInstance;

        // Flag to track if the button handler has been registered
        private static bool _buttonHandlerRegistered = false;
        /// <summary>
        /// Executes the sell slash command.
        /// </summary>
        /// <param name="command">The slash command that was executed</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task ExecuteAsync(SocketSlashCommand command, DiscordBotClient client)
        {
            // Register the button handler if not already done
            if (!_buttonHandlerRegistered)
            {
                _clientInstance = client;
                client._client.ButtonExecuted += OnButtonExecuted;
                _buttonHandlerRegistered = true;
            }

            try
            {
                // Check cooldown before allowing the command (same as %sell)
                if (!client.Permissions.HasPermission(command.User.Id, "sell", out string cooldownMessage))
                {
                    if (!string.IsNullOrEmpty(cooldownMessage))
                    {
                        await command.RespondAsync($"⏰ **Sell command is on cooldown!** {cooldownMessage}", ephemeral: true);
                    }
                    else
                    {
                        await command.RespondAsync("❌ You don't have permission to use this command.", ephemeral: true);
                    }
                    return;
                }

                // Update last usage time for cooldown tracking
                client.Permissions.UpdateCommandUsage(command.User.Id, "sell");

                // Get command options
                var itemIdOption = command.Data.Options.FirstOrDefault(x => x.Name == "item_id");

                if (itemIdOption == null)
                {
                    await command.RespondAsync("Please specify an inventory ID to sell. Use `/inventory` to see your items and their IDs.\n\nExample: `/sell item_id:1`", ephemeral: true);
                    return;
                }

                // Try to parse the ID as an integer
                if (!int.TryParse(itemIdOption.Value.ToString(), out int inventoryId))
                {
                    await command.RespondAsync("Invalid inventory ID. Please provide a numeric ID. Example: `/sell item_id:1`", ephemeral: true);
                    return;
                }

                await ShowSellConfirmation(command, inventoryId, client);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing sell slash command: {ex.Message}");
                
                if (!command.HasResponded)
                {
                    await command.RespondAsync("❌ An error occurred while processing the sell command.", ephemeral: true);
                }
            }
        }

        private static async Task ShowSellConfirmation(SocketSlashCommand command, int inventoryId, DiscordBotClient client)
        {
            // Get the user's data
            var userData = client.BotSystem.GetUserData(command.User.Id, command.User.Username);

            // Find the item in the user's inventory
            var inventoryItem = userData.Inventory.FirstOrDefault(item => item.InventoryId == inventoryId);
            if (inventoryItem == null)
            {
                await command.RespondAsync($"**<:BotChanDespair:1410316642180796446> | {command.User.Username}**, you don't have an item with inventory ID {inventoryId}. Use `/inventory` to see your items.", ephemeral: true);
                return;
            }

            // Get the consumable details
            var consumable = client.BotSystem.GetConsumable(inventoryItem.ConsumableId);
            if (consumable == null)
            {
                await command.RespondAsync($"**<:BotChanDespair:1410316642180796446> | {command.User.Username}**, there was an error retrieving the item information. Please try again.", ephemeral: true);
                return;
            }

            // Calculate sell price (50% of original cost)
            long sellPrice = consumable.Cost / 2;

            // Check if the item is currently active
            if (userData.ActiveConsumableId == consumable.Id && userData.ActiveConsumableUsesRemaining > 0)
            {
                await command.RespondAsync($"**<:BotChanDespair:1410316642180796446> | {command.User.Username}**, you cannot sell an item that is currently active. Please use `/dismantle` to remove the active multiplier first, or wait for it to expire.", ephemeral: true);
                return;
            }

            // Store the confirmation data
            _pendingConfirmations[command.User.Id] = (
                inventoryId,
                consumable.Name,
                consumable.Emoji,
                sellPrice,
                DateTime.UtcNow.AddSeconds(ConfirmationTimeoutSeconds)
            );

            // Create buttons for confirmation
            var sellButton = new ButtonBuilder()
                .WithLabel("Sell")
                .WithStyle(ButtonStyle.Danger)
                .WithCustomId($"sell_slash_confirm_{command.User.Id}")
                .WithEmote(new Emoji("💰"));

            var keepButton = new ButtonBuilder()
                .WithLabel("Keep")
                .WithStyle(ButtonStyle.Success)
                .WithCustomId($"sell_slash_cancel_{command.User.Id}")
                .WithEmote(new Emoji("✅"));

            var components = new ComponentBuilder()
                .WithButton(sellButton)
                .WithButton(keepButton)
                .Build();

            // Send confirmation message with buttons
            await command.RespondAsync(
                text: $"**<:BotChanReaction:1410313252826779648> | {command.User.Username}**, are you sure you want to sell **{consumable.Name}** {consumable.Emoji} for **{sellPrice:N0}** {Constants.Emojis.BotDown}? This action cannot be undone!",
                components: components);
        }

        /// <summary>
        /// Handles button interactions for sell confirmations
        /// </summary>
        private static async Task OnButtonExecuted(SocketMessageComponent component)
        {
            try
            {
                var customId = component.Data.CustomId;
                var userId = component.User.Id;

                // Check if this is a sell slash button
                if (!customId.StartsWith("sell_slash_"))
                    return;

                // Extract the user ID from the custom ID
                var parts = customId.Split('_');
                if (parts.Length != 4 || !ulong.TryParse(parts[3], out var targetUserId))
                    return;

                // Only allow the original user to interact with their buttons
                if (userId != targetUserId)
                {
                    await component.RespondAsync("❌ You can only interact with your own sell confirmation.", ephemeral: true);
                    return;
                }

                // Check if there's a pending confirmation for this user
                if (!_pendingConfirmations.TryGetValue(userId, out var confirmationData))
                {
                    await component.RespondAsync("❌ This sell confirmation has expired or is no longer valid.", ephemeral: true);
                    return;
                }

            // Check if the confirmation has expired
            if (DateTime.UtcNow > confirmationData.Expiry)
            {
                _pendingConfirmations.Remove(userId);
                await component.RespondAsync("❌ This sell confirmation has expired. Please try again.", ephemeral: true);
                return;
            }

            var action = parts[2]; // "confirm" or "cancel"

            if (action == "confirm")
            {
                // Check if client instance is available
                if (_clientInstance == null)
                {
                    await component.RespondAsync("❌ An error occurred. Please try again.", ephemeral: true);
                    return;
                }

                // Perform the sell
                var userData = _clientInstance.BotSystem.GetUserData(userId, component.User.Username);

                // Find and remove the item from inventory
                var inventoryItem = userData.Inventory.FirstOrDefault(item => item.InventoryId == confirmationData.InventoryId);
                if (inventoryItem != null)
                {
                    userData.Inventory.Remove(inventoryItem);

                    // Add bots to the user's account
                    userData.BotCount += confirmationData.SellPrice;

                    // Save the user data
                    _clientInstance.BotSystem.SaveUserData();

                    // Create success embed
                    var description = new StringBuilder();
                    description.AppendLine($"You've sold **{confirmationData.ItemName}** {confirmationData.ItemEmoji} for **{confirmationData.SellPrice:N0}** {Constants.Emojis.BotDown}!");
                    description.AppendLine();
                    description.AppendLine($"**New Bot Count:** {userData.BotCount:N0} {Constants.Emojis.BotDown}");

                    var embed = new EmbedBuilder()
                        .WithTitle("💰 Item Sold!")
                        .WithDescription(description.ToString())
                        .WithColor(Color.Green)
                        .WithThumbnailUrl(component.User.GetAvatarUrl() ?? component.User.GetDefaultAvatarUrl())
                        .WithTimestamp(DateTimeOffset.Now);

                    await component.RespondAsync(embed: embed.Build());
                }
                else
                {
                    await component.RespondAsync($"**<:BotChanDespair:1410316642180796446> | {component.User.Username}**, the item no longer exists in your inventory.", ephemeral: true);
                }
            }
            else if (action == "cancel")
            {
                await component.RespondAsync($"**<:BotChanReaction:1410313252826779648> | {component.User.Username}**, you've decided to keep **{confirmationData.ItemName}** {confirmationData.ItemEmoji}. The item remains in your inventory.");
            }

            // Remove the confirmation data
            _pendingConfirmations.Remove(userId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in sell button handler: {ex.Message}");

                // Try to respond to the interaction if it hasn't been responded to yet
                try
                {
                    if (!component.HasResponded)
                    {
                        await component.RespondAsync("❌ An error occurred while processing your sell request.", ephemeral: true);
                    }
                }
                catch
                {
                    // If we can't respond, just log it
                    Console.WriteLine("Failed to send error response for sell button interaction");
                }
            }
        }
    }
}
