using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;

namespace BotChan.Commands
{
    /// <summary>
    /// BotsCommand - A command that shows the user's bot count
    /// Usage: %bots
    /// Example: %bots
    /// </summary>
    public class BotsCommand : Command
    {
        // Override the default category
        public override string Category => "Economy";

        // Override the default usage
        public override string Usage => "%bots";

        // Override the default examples
        public override string Examples => "%bots";

        public BotsCommand() : base("bots", "Check how many bots you have collected")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Get the user's data
            var userData = client.BotSystem.GetUserData(message.Author.Id);

            // Create an embed for the response
            var embed = new EmbedBuilder()
                .WithTitle("Your own Bot Army!")
                .WithColor(Color.Blue)
                .WithThumbnailUrl("https://cdn.discordapp.com/attachments/1363245551184908468/1363320873498447923/Bot_Army.png?ex=68059b01&is=68044981&hm=2e478e2f9c02fea6bb21f47b6c6f82e8aeb1f54a7bf1a29e2dd0778bf2ddcb09&")
                .WithDescription($"You have collected **{userData.BotCount}** bots!")
                .WithFooter("Use %recruit to get more bots")
                .WithCurrentTimestamp();

            // Send the response
            await message.Channel.SendMessageAsync(embed: embed.Build());
        }
    }
}
