using Discord;
using Discord.WebSocket;

namespace BotChan.Models
{
    /// <summary>
    /// A simple wrapper that provides the essential properties needed for command execution.
    /// This allows slash commands to work with the existing command structure.
    /// </summary>
    public class SlashCommandMessage
    {
        private readonly SocketSlashCommand _slashCommand;

        public SlashCommandMessage(SocketSlashCommand slashCommand)
        {
            _slashCommand = slashCommand;
        }

        public string Content => $"/{_slashCommand.Data.Name}";

        public ISocketMessageChannel Channel => _slashCommand.Channel;

        public SocketUser Author => _slashCommand.User;

        public ulong Id => _slashCommand.Id;

        /// <summary>
        /// Gets the underlying slash command for advanced operations.
        /// </summary>
        public SocketSlashCommand SlashCommand => _slashCommand;
    }
}
