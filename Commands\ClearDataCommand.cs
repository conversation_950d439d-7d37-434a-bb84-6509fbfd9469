using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;
using System.Text;

namespace BotChan.Commands
{
    /// <summary>
    /// ClearDataCommand - Allows the Creator to clean up player data
    /// Usage: %cleardata
    /// Example: %cleardata
    /// </summary>
    public class ClearDataCommand : Command
    {
        // Override the default category
        public override string Category => "Admin";

        // Override the default usage
        public override string Usage => "%cleardata";

        // Override the default examples
        public override string Examples => "%cleardata";

        // Dictionary to track active menus
        private static readonly Dictionary<ulong, ClearDataMenuState> _activeMenus = new();

        // Dictionary to track message handlers for each user
        private static readonly Dictionary<ulong, Func<SocketMessage, Task>> _messageHandlers = new();

        public ClearDataCommand() : base("cleardata", "Clean up player data (Creator only)")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Only the creator can use this command
            bool isCreator = message.Author.Id == 513793435807907841;

            if (!isCreator)
            {
                await message.Channel.SendMessageAsync("You don't have permission to use this command.");
                return;
            }

            // If the user already has an active menu, close it first
            if (_activeMenus.ContainsKey(message.Author.Id))
            {
                await CloseMenu(message.Author.Id, client);
                await message.Channel.SendMessageAsync("Previous menu closed.");
            }

            // Create a new menu state for this user
            var menuState = new ClearDataMenuState();
            _activeMenus[message.Author.Id] = menuState;

            // Show the main menu
            await ShowMainMenu(message, client);

            // Create a message handler for this menu
            Func<SocketMessage, Task> handler = async (msg) =>
            {
                // Only process messages from the original user
                if (msg.Author.Id != message.Author.Id || msg.Author.IsBot)
                    return;

                // Check if the user has an active menu
                if (!_activeMenus.TryGetValue(msg.Author.Id, out var state))
                    return;

                // Check for menu timeout (2 minutes)
                if (DateTime.UtcNow - state.LastActivity > TimeSpan.FromMinutes(2))
                {
                    await CloseMenu(msg.Author.Id, client);
                    await msg.Channel.SendMessageAsync("Menu closed due to inactivity.");
                    return;
                }

                // Process the menu input
                await ProcessMenuInput(msg, state, client);
            };

            // Store the handler so we can remove it later
            _messageHandlers[message.Author.Id] = handler;

            // Register the handler
            client._client.MessageReceived += handler;
        }

        /// <summary>
        /// Closes the menu for a user and cleans up resources.
        /// </summary>
        private async Task CloseMenu(ulong userId, DiscordBotClient client)
        {
            // Remove the menu state
            _activeMenus.Remove(userId);

            // Get and remove the message handler
            if (_messageHandlers.TryGetValue(userId, out var handler))
            {
                // Unregister the handler
                client._client.MessageReceived -= handler;
                _messageHandlers.Remove(userId);
            }
        }

        /// <summary>
        /// Shows the main menu for the clear data command.
        /// </summary>
        private async Task ShowMainMenu(SocketMessage message, DiscordBotClient client)
        {
            var embed = new EmbedBuilder()
                .WithTitle("Clear Data Menu")
                .WithDescription("Select an option:")
                .AddField("1️⃣ Remove Inactive Users", "Remove users with 0 bots and empty inventory")
                .AddField("2️⃣ Remove Users Below Threshold", "Remove users with bot count below a specified threshold")
                .AddField("3️⃣ Reset All User Data", "WARNING: This will reset ALL user data")
                .AddField("4️⃣ View Database Stats", "View statistics about the current database")
                .AddField("exit", "Close this menu")
                .WithColor(Color.Red)
                .WithFooter("Type the number of your choice");

            await message.Channel.SendMessageAsync(embed: embed.Build());

            // Update last activity time
            if (_activeMenus.TryGetValue(message.Author.Id, out var state))
            {
                state.LastActivity = DateTime.UtcNow;
            }
        }

        /// <summary>
        /// Processes menu input based on the current menu state.
        /// </summary>
        private async Task ProcessMenuInput(SocketMessage message, ClearDataMenuState state, DiscordBotClient client)
        {
            // Update last activity time
            state.LastActivity = DateTime.UtcNow;

            // Check for exit command
            if (message.Content.Equals("exit", StringComparison.OrdinalIgnoreCase))
            {
                await CloseMenu(message.Author.Id, client);
                await message.Channel.SendMessageAsync("Data cleanup menu closed.");
                return;
            }

            // Check for back command
            if (message.Content.Equals("back", StringComparison.OrdinalIgnoreCase))
            {
                // Reset the state
                state.CurrentMenu = ClearDataMenu.Main;

                // Show the main menu
                await ShowMainMenu(message, client);
                return;
            }

            // Process input based on current menu
            switch (state.CurrentMenu)
            {
                case ClearDataMenu.Main:
                    await ProcessMainMenuInput(message, state, client);
                    break;

                case ClearDataMenu.RemoveInactive:
                    await ProcessRemoveInactiveInput(message, state, client);
                    break;

                case ClearDataMenu.RemoveBelowThreshold:
                    await ProcessRemoveBelowThresholdInput(message, state, client);
                    break;

                case ClearDataMenu.ResetAll:
                    await ProcessResetAllInput(message, state, client);
                    break;
            }
        }

        /// <summary>
        /// Processes input for the main menu.
        /// </summary>
        private async Task ProcessMainMenuInput(SocketMessage message, ClearDataMenuState state, DiscordBotClient client)
        {
            switch (message.Content)
            {
                case "1":
                    // Remove inactive users
                    state.CurrentMenu = ClearDataMenu.RemoveInactive;
                    await message.Channel.SendMessageAsync("Are you sure you want to remove all inactive users (0 bots and empty inventory)? Type 'confirm' to proceed or 'back' to cancel.");
                    break;

                case "2":
                    // Remove users below threshold
                    state.CurrentMenu = ClearDataMenu.RemoveBelowThreshold;
                    await message.Channel.SendMessageAsync("Enter the bot count threshold. Users with fewer bots than this will be removed:");
                    break;

                case "3":
                    // Reset all user data
                    state.CurrentMenu = ClearDataMenu.ResetAll;
                    await message.Channel.SendMessageAsync("⚠️ **WARNING** ⚠️ This will reset ALL user data! Type 'CONFIRM_RESET_ALL' (all caps) to proceed or 'back' to cancel.");
                    break;

                case "4":
                    // View database stats
                    await ShowDatabaseStats(message, client);
                    break;

                default:
                    await message.Channel.SendMessageAsync("Invalid option. Please select a number from 1-4, or type 'exit' to close the menu.");
                    break;
            }
        }

        /// <summary>
        /// Shows statistics about the current database.
        /// </summary>
        private async Task ShowDatabaseStats(SocketMessage message, DiscordBotClient client)
        {
            // Get all user data
            var allUsers = client.BotSystem.GetAllUserData();

            // Calculate statistics
            int totalUsers = allUsers.Count;
            int activeUsers = allUsers.Count(u => u.BotCount > 0 || u.Inventory.Count > 0);
            int inactiveUsers = totalUsers - activeUsers;
            long totalBots = allUsers.Sum(u => u.BotCount);
            int totalItems = allUsers.Sum(u => u.Inventory.Count);

            // Calculate distribution
            int usersWithNoItems = allUsers.Count(u => u.Inventory.Count == 0);
            int usersWithNoBots = allUsers.Count(u => u.BotCount == 0);
            int usersWithBothEmpty = allUsers.Count(u => u.BotCount == 0 && u.Inventory.Count == 0);

            // Create the embed
            var embed = new EmbedBuilder()
                .WithTitle("Database Statistics")
                .WithDescription("Current state of the user database:")
                .AddField("User Counts",
                    $"Total Users: **{totalUsers}**\n" +
                    $"Active Users: **{activeUsers}**\n" +
                    $"Inactive Users: **{inactiveUsers}**")
                .AddField("Economy Stats",
                    $"Total Bots: **{totalBots}** {Constants.Emojis.BotDown}\n" +
                    $"Total Items: **{totalItems}**\n" +
                    $"Average Bots per User: **{(totalUsers > 0 ? (double)totalBots / totalUsers : 0):F1}** {Constants.Emojis.BotDown}")
                .AddField("Distribution",
                    $"Users with No Items: **{usersWithNoItems}** ({(totalUsers > 0 ? (double)usersWithNoItems / totalUsers * 100 : 0):F1}%)\n" +
                    $"Users with No Bots: **{usersWithNoBots}** ({(totalUsers > 0 ? (double)usersWithNoBots / totalUsers * 100 : 0):F1}%)\n" +
                    $"Users with Empty Accounts: **{usersWithBothEmpty}** ({(totalUsers > 0 ? (double)usersWithBothEmpty / totalUsers * 100 : 0):F1}%)")
                .WithColor(Color.Blue)
                .WithFooter("Type 'back' to return to the main menu");

            await message.Channel.SendMessageAsync(embed: embed.Build());
        }

        /// <summary>
        /// Processes input for the remove inactive users menu.
        /// </summary>
        private async Task ProcessRemoveInactiveInput(SocketMessage message, ClearDataMenuState state, DiscordBotClient client)
        {
            if (message.Content.Equals("confirm", StringComparison.OrdinalIgnoreCase))
            {
                // Get all user data
                var allUsers = client.BotSystem.GetAllUserData();

                // Find inactive users
                var inactiveUsers = allUsers.Where(u => u.BotCount == 0 && u.Inventory.Count == 0).ToList();

                // Get the list of user IDs to remove
                var userIdsToRemove = inactiveUsers.Select(u => u.UserId).ToList();

                // Log the operation
                Console.WriteLine($"Removing {userIdsToRemove.Count} inactive users");

                // Remove inactive users
                int removedCount = client.BotSystem.RemoveUsers(userIdsToRemove);

                // Force an immediate save to update the leaderboards
                client.BotSystem.ForceSaveUserData();

                await message.Channel.SendMessageAsync($"Removed {removedCount} inactive users from the database.");

                // Return to main menu
                state.CurrentMenu = ClearDataMenu.Main;
                await ShowMainMenu(message, client);
            }
            else
            {
                await message.Channel.SendMessageAsync("Operation cancelled. Type 'back' to return to the main menu.");
                state.CurrentMenu = ClearDataMenu.Main;
                await ShowMainMenu(message, client);
            }
        }

        /// <summary>
        /// Processes input for the remove users below threshold menu.
        /// </summary>
        private async Task ProcessRemoveBelowThresholdInput(SocketMessage message, ClearDataMenuState state, DiscordBotClient client)
        {
            // If we're waiting for confirmation
            if (state.ThresholdValue > 0 && message.Content.Equals("confirm", StringComparison.OrdinalIgnoreCase))
            {
                // Get all user data
                var allUsers = client.BotSystem.GetAllUserData();

                // Find users below threshold
                var usersToRemove = allUsers.Where(u => u.BotCount < state.ThresholdValue).ToList();

                // Get the list of user IDs to remove
                var userIdsToRemove = usersToRemove.Select(u => u.UserId).ToList();

                // Log the operation
                Console.WriteLine($"Removing {userIdsToRemove.Count} users with fewer than {state.ThresholdValue} bots");

                // Remove users
                int removedCount = client.BotSystem.RemoveUsers(userIdsToRemove);

                // Force an immediate save to update the leaderboards
                client.BotSystem.ForceSaveUserData();

                await message.Channel.SendMessageAsync($"Removed {removedCount} users with fewer than {state.ThresholdValue} bots from the database.");

                // Reset threshold and return to main menu
                state.ThresholdValue = 0;
                state.CurrentMenu = ClearDataMenu.Main;
                await ShowMainMenu(message, client);
                return;
            }

            // If we're getting the threshold value
            if (int.TryParse(message.Content, out int threshold) && threshold > 0)
            {
                state.ThresholdValue = threshold;

                // Get all user data
                var allUsers = client.BotSystem.GetAllUserData();

                // Count users below threshold
                int usersToRemove = allUsers.Count(u => u.BotCount < threshold);

                await message.Channel.SendMessageAsync($"This will remove {usersToRemove} users with fewer than {threshold} bots. Type 'confirm' to proceed or 'back' to cancel.");
            }
            else
            {
                await message.Channel.SendMessageAsync("Please enter a valid positive number for the threshold.");
            }
        }

        /// <summary>
        /// Processes input for the reset all user data menu.
        /// </summary>
        private async Task ProcessResetAllInput(SocketMessage message, ClearDataMenuState state, DiscordBotClient client)
        {
            if (message.Content.Equals("CONFIRM_RESET_ALL", StringComparison.Ordinal))
            {
                // Log the operation
                Console.WriteLine("Resetting all user data");

                // Reset all user data
                int resetCount = client.BotSystem.ResetAllUsers();

                // Force an immediate save to update the leaderboards
                client.BotSystem.ForceSaveUserData();

                // Log completion
                Console.WriteLine($"Reset complete. Reset {resetCount} users.");

                await message.Channel.SendMessageAsync($"⚠️ **RESET COMPLETE** ⚠️\nReset data for {resetCount} users. All bot counts and inventories have been cleared.");

                // Return to main menu
                state.CurrentMenu = ClearDataMenu.Main;
                await ShowMainMenu(message, client);
            }
            else
            {
                await message.Channel.SendMessageAsync("Reset operation cancelled. Type 'back' to return to the main menu.");
                state.CurrentMenu = ClearDataMenu.Main;
                await ShowMainMenu(message, client);
            }
        }
    }

    /// <summary>
    /// Represents the current menu in the clear data command.
    /// </summary>
    public enum ClearDataMenu
    {
        Main,
        RemoveInactive,
        RemoveBelowThreshold,
        ResetAll
    }

    /// <summary>
    /// Represents the state of the clear data menu.
    /// </summary>
    public class ClearDataMenuState
    {
        /// <summary>
        /// The current menu being displayed.
        /// </summary>
        public ClearDataMenu CurrentMenu { get; set; } = ClearDataMenu.Main;

        /// <summary>
        /// The threshold value for removing users.
        /// </summary>
        public int ThresholdValue { get; set; } = 0;

        /// <summary>
        /// The last time the user interacted with the menu.
        /// </summary>
        public DateTime LastActivity { get; set; } = DateTime.UtcNow;
    }
}
