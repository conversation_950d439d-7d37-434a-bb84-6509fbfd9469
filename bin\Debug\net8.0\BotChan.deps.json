{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"BotChan/1.0.0": {"dependencies": {"Discord.Net": "3.17.2", "Microsoft.Extensions.Configuration.Json": "9.0.4"}, "runtime": {"BotChan.dll": {}}}, "Discord.Net/3.17.2": {"dependencies": {"Discord.Net.Commands": "3.17.2", "Discord.Net.Core": "3.17.2", "Discord.Net.Interactions": "3.17.2", "Discord.Net.Rest": "3.17.2", "Discord.Net.WebSocket": "3.17.2", "Discord.Net.Webhook": "3.17.2"}}, "Discord.Net.Commands/3.17.2": {"dependencies": {"Discord.Net.Core": "3.17.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Discord.Net.Commands.dll": {"assemblyVersion": "3.17.2.0", "fileVersion": "3.17.2.0"}}}, "Discord.Net.Core/3.17.2": {"dependencies": {"Newtonsoft.Json": "13.0.3", "System.Collections.Immutable": "8.0.0", "System.Interactive.Async": "6.0.1", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/net8.0/Discord.Net.Core.dll": {"assemblyVersion": "3.17.2.0", "fileVersion": "3.17.2.0"}}}, "Discord.Net.Interactions/3.17.2": {"dependencies": {"Discord.Net.Core": "3.17.2", "Discord.Net.Rest": "3.17.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "System.Collections.Immutable": "8.0.0", "System.Reactive": "6.0.1"}, "runtime": {"lib/net8.0/Discord.Net.Interactions.dll": {"assemblyVersion": "3.17.2.0", "fileVersion": "3.17.2.0"}}}, "Discord.Net.Rest/3.17.2": {"dependencies": {"Discord.Net.Core": "3.17.2"}, "runtime": {"lib/net8.0/Discord.Net.Rest.dll": {"assemblyVersion": "3.17.2.0", "fileVersion": "3.17.2.0"}}}, "Discord.Net.Webhook/3.17.2": {"dependencies": {"Discord.Net.Core": "3.17.2", "Discord.Net.Rest": "3.17.2"}, "runtime": {"lib/net8.0/Discord.Net.Webhook.dll": {"assemblyVersion": "3.17.2.0", "fileVersion": "3.17.2.0"}}}, "Discord.Net.WebSocket/3.17.2": {"dependencies": {"Discord.Net.Core": "3.17.2", "Discord.Net.Rest": "3.17.2"}, "runtime": {"lib/net8.0/Discord.Net.WebSocket.dll": {"assemblyVersion": "3.17.2.0", "fileVersion": "3.17.2.0"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Configuration/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Physical": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.Json/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "System.Text.Json": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.4": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.FileSystemGlobbing": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.4": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Primitives/9.0.4": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "System.Collections.Immutable/8.0.0": {}, "System.Interactive.Async/6.0.1": {"dependencies": {"System.Linq.Async": "6.0.1"}, "runtime": {"lib/net6.0/System.Interactive.Async.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1.35981"}}}, "System.IO.Pipelines/9.0.4": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Linq.Async/6.0.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0"}, "runtime": {"lib/net6.0/System.Linq.Async.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1.35981"}}}, "System.Reactive/6.0.1": {"runtime": {"lib/net6.0/System.Reactive.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1.7420"}}}, "System.Text.Encodings.Web/9.0.4": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.Text.Json/9.0.4": {"dependencies": {"System.IO.Pipelines": "9.0.4", "System.Text.Encodings.Web": "9.0.4"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "System.ValueTuple/4.5.0": {}}}, "libraries": {"BotChan/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Discord.Net/3.17.2": {"type": "package", "serviceable": true, "sha512": "sha512-jDyclg2hLkzB+bjVQlNo55wQUisTDG5vDwAjTMyn69djR0xG72Glln0xgjWMLHvdjVER4zSgwl6uq2r5jljmGA==", "path": "discord.net/3.17.2", "hashPath": "discord.net.3.17.2.nupkg.sha512"}, "Discord.Net.Commands/3.17.2": {"type": "package", "serviceable": true, "sha512": "sha512-carBI4BDwWwGogRnM1w4k6AZBqZ42jeMdY9O9/KqIRwX2e7B+Yt8bh99c/Ilrnonea17glQ3chsX3pwT4AzecA==", "path": "discord.net.commands/3.17.2", "hashPath": "discord.net.commands.3.17.2.nupkg.sha512"}, "Discord.Net.Core/3.17.2": {"type": "package", "serviceable": true, "sha512": "sha512-mTjcgYqk0Qzd7Vq7m8fRmjjgAoravmxuIsxzbWGqYZYK30s6rDC//RFJYNTcBA3ZY9PSuX34S8gMo7nI4tqMVw==", "path": "discord.net.core/3.17.2", "hashPath": "discord.net.core.3.17.2.nupkg.sha512"}, "Discord.Net.Interactions/3.17.2": {"type": "package", "serviceable": true, "sha512": "sha512-Cn8uKXSDZ5cmzaYmQUN3KaA9YKvfqb3H26HwDzZMU8WoKkLlbLa3Vmgf3BmMBCDoQGWuLHq9+r9OdR6rashluw==", "path": "discord.net.interactions/3.17.2", "hashPath": "discord.net.interactions.3.17.2.nupkg.sha512"}, "Discord.Net.Rest/3.17.2": {"type": "package", "serviceable": true, "sha512": "sha512-8aq2lja/OLPKoesQnethQ1UltsIvaHBKfwPFxWd/tojcAP9Fju0OOTK6I9U7Wkq7szGASjNKuAjE+YO+lyx5Yg==", "path": "discord.net.rest/3.17.2", "hashPath": "discord.net.rest.3.17.2.nupkg.sha512"}, "Discord.Net.Webhook/3.17.2": {"type": "package", "serviceable": true, "sha512": "sha512-XcJ8wgvQ2gfMsWq9WuLfIpBIZN2Yji6G2QUqAD7D9FrxHBwcOxof9eVCukw+vU3wLT5r5cmjDax6FJMg2WhRBw==", "path": "discord.net.webhook/3.17.2", "hashPath": "discord.net.webhook.3.17.2.nupkg.sha512"}, "Discord.Net.WebSocket/3.17.2": {"type": "package", "serviceable": true, "sha512": "sha512-yq/IfGJ3TfFa15dDzyoAOHEbXSDPRDf/HdNFxQj+P1h7Q95V5lIWSJqYHtgg01E8eRW6IFvWfBDhfcFatffe+Q==", "path": "discord.net.websocket/3.17.2", "hashPath": "discord.net.websocket.3.17.2.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-KIVBrMbItnCJDd1RF4KEaE8jZwDJcDUJW5zXpbwQ05HNYTK1GveHxHK0B3SjgDJuR48GRACXAO+BLhL8h34S7g==", "path": "microsoft.extensions.configuration/9.0.4", "hashPath": "microsoft.extensions.configuration.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-0LN/DiIKvBrkqp7gkF3qhGIeZk6/B63PthAHjQsxymJfIBcz0kbf4/p/t4lMgggVxZ+flRi5xvTwlpPOoZk8fg==", "path": "microsoft.extensions.configuration.abstractions/9.0.4", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-UY864WQ3AS2Fkc8fYLombWnjrXwYt+BEHHps0hY4sxlgqaVW06AxbpgRZjfYf8PyRbplJqruzZDB/nSLT+7RLQ==", "path": "microsoft.extensions.configuration.fileextensions/9.0.4", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-vVXI70CgT/dmXV3MM+n/BR2rLXEoAyoK0hQT+8MrbCMuJBiLRxnTtSrksNiASWCwOtxo/Tyy7CO8AGthbsYxnw==", "path": "microsoft.extensions.configuration.json/9.0.4", "hashPath": "microsoft.extensions.configuration.json.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-gQN2o/KnBfVk6Bd71E2YsvO5lsqrqHmaepDGk+FB/C4aiQY9B0XKKNKfl5/TqcNOs9OEithm4opiMHAErMFyEw==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.4", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-qkQ9V7KFZdTWNThT7ke7E/Jad38s46atSs3QUYZB8f3thBTrcrousdY4Y/tyCtcH5YjsPSiByjuN+L8W/ThMQg==", "path": "microsoft.extensions.fileproviders.physical/9.0.4", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-05Lh2ItSk4mzTdDWATW9nEcSybwprN8Tz42Fs5B+jwdXUpauktdAQUI1Am4sUQi2C63E5hvQp8gXvfwfg9mQGQ==", "path": "microsoft.extensions.filesystemglobbing/9.0.4", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-SPFyMjyku1nqTFFJ928JAMd0QnRe4xjE7KeKnZMWXf3xk+6e0WiOZAluYtLdbJUXtsl2cCRSi8cBquJ408k8RA==", "path": "microsoft.extensions.primitives/9.0.4", "hashPath": "microsoft.extensions.primitives.9.0.4.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.Interactive.Async/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-f8H1O4ZWDQo344y5NQU76G4SIjWMuKDVXL9OM1dg6K5YZnLkc8iCdQDybBvMcC6ufk61jzXGVAX6UCDu0qDSjA==", "path": "system.interactive.async/6.0.1", "hashPath": "system.interactive.async.6.0.1.nupkg.sha512"}, "System.IO.Pipelines/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-luF2Xba+lTe2GOoNQdZLe8q7K6s7nSpWZl9jIwWNMszN4/Yv0lmxk9HISgMmwdyZ83i3UhAGXaSY9o6IJBUuuA==", "path": "system.io.pipelines/9.0.4", "hashPath": "system.io.pipelines.9.0.4.nupkg.sha512"}, "System.Linq.Async/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-0YhHcaroWpQ9UCot3Pizah7ryAzQhNvobLMSxeDIGmnXfkQn8u5owvpOH0K6EVB+z9L7u6Cc4W17Br/+jyttEQ==", "path": "system.linq.async/6.0.1", "hashPath": "system.linq.async.6.0.1.nupkg.sha512"}, "System.Reactive/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-rHaWtKDwCi9qJ3ObKo8LHPMuuwv33YbmQi7TcUK1C264V3MFnOr5Im7QgCTdLniztP3GJyeiSg5x8NqYJFqRmg==", "path": "system.reactive/6.0.1", "hashPath": "system.reactive.6.0.1.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-V+5cCPpk1S2ngekUs9nDrQLHGiWFZMg8BthADQr+Fwi59a8DdHFu26S2oi9Bfgv+d67bqmkPqctJXMEXiimXUg==", "path": "system.text.encodings.web/9.0.4", "hashPath": "system.text.encodings.web.9.0.4.nupkg.sha512"}, "System.Text.Json/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-pYtmpcO6R3Ef1XilZEHgXP2xBPVORbYEzRP7dl0IAAbN8Dm+kfwio8aCKle97rAWXOExr292MuxWYurIuwN62g==", "path": "system.text.json/9.0.4", "hashPath": "system.text.json.9.0.4.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}}}