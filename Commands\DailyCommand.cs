using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;

namespace BotChan.Commands
{
    /// <summary>
    /// DailyCommand - A command that gives the user daily bot points
    /// Usage: %daily
    /// Example: %daily
    /// </summary>
    public class DailyCommand : Command
    {
        // Override the default category
        public override string Category => "Economy";

        // Override the default usage
        public override string Usage => "%daily";

        // Override the default examples
        public override string Examples => "%daily";

        // The amount of bots to give for the daily reward
        private const long DailyReward = 25;

        public DailyCommand() : base("daily", "Claim your daily bot points")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Add the daily reward to the user's account
            bool fullAmountAdded = client.BotSystem.AddBots(message.Author.Id, DailyReward);

            // Get the user's updated data
            var userData = client.BotSystem.GetUserData(message.Author.Id, message.Author.Username);

            // Create the response message with the specified format and directly embedded emojis
            string responseMessage = $"<a:BM_BotChanDance2:1410331782607540245> | **{message.Author.Username}**, you claimed your daily reward of **{DailyReward}** {Constants.Emojis.BotDown}!\n\n**Total Bots:** {userData.BotCount} {Constants.Emojis.BotDown}";

            // Add a message if the user reached the bot limit
            if (!fullAmountAdded)
            {
                responseMessage += "\n\n**🏆 Congratulations! You've reached the maximum limit of 1,000,000 bots! 🏆**";
            }

            // Send the response
            await message.Channel.SendMessageAsync(responseMessage);

            // Get the daily summary message and always reset the stats
            // This way, using daily again will show stats since the last reset
            string summaryMessage = userData.GetDailySummary(true);

            // Send the summary message
            await message.Channel.SendMessageAsync(summaryMessage);
        }
    }
}
