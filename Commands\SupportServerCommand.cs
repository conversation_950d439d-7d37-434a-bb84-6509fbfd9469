using Discord;
using Discord.WebSocket;
using BotChan.Services;

namespace BotChan.Commands
{
    /// <summary>
    /// SupportServerCommand - Provides the support server link
    /// Usage: %supportserver
    /// Example: %supportserver
    /// </summary>
    public class SupportServerCommand : Command
    {
        // Override the default category
        public override string Category => "General";

        // Override the default usage
        public override string Usage => "%supportserver";

        // Override the default examples
        public override string Examples => "%supportserver";

        public SupportServerCommand() : base("supportserver", "Get the link to our support server")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            var embed = new EmbedBuilder()
                .WithTitle("🛠️ Support Server")
                .WithDescription("Need help or want to join our community?")
                .AddField("Support Server", "Our support server is here: https://discord.gg/DHbChVj")
                .AddField("What you can find there:", 
                    "• Get help with bot commands\n" +
                    "• Report bugs and issues\n" +
                    "• Suggest new features\n" +
                    "• Chat with other players\n" +
                    "• Get updates and announcements")
                .WithColor(Color.Blue)
                .WithFooter("Click the link above to join!")
                .WithTimestamp(DateTimeOffset.Now);

            await message.Channel.SendMessageAsync(embed: embed.Build());
        }
    }
}
