using System.Text.Json;
using BotChan.Models;

namespace BotChan.Services
{
    /// <summary>
    /// Service for managing command permissions.
    /// </summary>
    public class PermissionsService
    {
        private Permissions _permissions;
        private readonly string _permissionsFilePath;
        private readonly string _cooldownsFilePath;
        private readonly ulong _ownerId;

        /// <summary>
        /// Initializes a new instance of the PermissionsService.
        /// </summary>
        /// <param name="ownerId">The Discord user ID of the bot owner.</param>
        public PermissionsService(ulong ownerId)
        {
            _ownerId = ownerId;
            _permissionsFilePath = Path.Combine(Directory.GetCurrentDirectory(), "permissions.json");
            _cooldownsFilePath = Path.Combine(Directory.GetCurrentDirectory(), "cooldowns.json");
            _permissions = LoadPermissions();
            _permissions.OwnerId = _ownerId;

            // Load cooldowns from file
            LoadCooldowns();
        }

        /// <summary>
        /// Gets the current permissions.
        /// </summary>
        public Permissions CurrentPermissions => _permissions;

        /// <summary>
        /// Checks if a user has permission to use a command.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="commandName">The name of the command.</param>
        /// <returns>True if the user has permission, false otherwise.</returns>
        public bool HasPermission(ulong userId, string commandName)
        {
            return _permissions.HasPermission(userId, commandName);
        }

        /// <summary>
        /// Checks if a user has permission to use a command, including cooldown check.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="commandName">The name of the command.</param>
        /// <param name="cooldownMessage">If cooldown is active, contains a message with remaining time.</param>
        /// <returns>True if the user has permission, false otherwise.</returns>
        public bool HasPermission(ulong userId, string commandName, out string cooldownMessage)
        {
            return _permissions.HasPermission(userId, commandName, out cooldownMessage, true);
        }

        /// <summary>
        /// Checks if a user has hit the rate limit.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="commandName">The name of the command being executed.</param>
        /// <returns>True if the user has hit the rate limit, false otherwise.</returns>
        public bool IsRateLimited(ulong userId, string commandName)
        {
            return _permissions.IsRateLimited(userId, commandName);
        }

        /// <summary>
        /// Updates the last usage time for a command by a user.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="commandName">The name of the command.</param>
        public void UpdateCommandUsage(ulong userId, string commandName)
        {
            _permissions.UpdateCommandUsage(userId, commandName);
            SaveCooldowns();
        }

        /// <summary>
        /// Sets the permission level for a command.
        /// </summary>
        /// <param name="commandName">The name of the command.</param>
        /// <param name="level">The permission level to set.</param>
        public void SetCommandPermission(string commandName, PermissionLevel level)
        {
            _permissions.CommandPermissions[commandName] = level;
            SavePermissions();
        }

        /// <summary>
        /// Adds a user to the staff list.
        /// </summary>
        /// <param name="userId">The ID of the user to add.</param>
        /// <returns>True if the user was added, false if they were already on the list.</returns>
        public bool AddStaffUser(ulong userId)
        {
            if (_permissions.StaffUsers.Contains(userId))
            {
                return false;
            }

            _permissions.StaffUsers.Add(userId);
            SavePermissions();
            return true;
        }

        /// <summary>
        /// Removes a user from the staff list.
        /// </summary>
        /// <param name="userId">The ID of the user to remove.</param>
        /// <returns>True if the user was removed, false if they weren't on the list.</returns>
        public bool RemoveStaffUser(ulong userId)
        {
            bool removed = _permissions.StaffUsers.Remove(userId);
            if (removed)
            {
                SavePermissions();
            }
            return removed;
        }

        /// <summary>
        /// Adds a user to the whitelist for a command.
        /// </summary>
        /// <param name="commandName">The name of the command.</param>
        /// <param name="userId">The ID of the user to add.</param>
        /// <returns>True if the user was added, false if they were already on the list.</returns>
        public bool AddWhitelistedUser(string commandName, ulong userId)
        {
            if (!_permissions.WhitelistedUsers.ContainsKey(commandName))
            {
                _permissions.WhitelistedUsers[commandName] = new List<ulong>();
            }

            if (_permissions.WhitelistedUsers[commandName].Contains(userId))
            {
                return false;
            }

            _permissions.WhitelistedUsers[commandName].Add(userId);
            SavePermissions();
            return true;
        }

        /// <summary>
        /// Removes a user from the whitelist for a command.
        /// </summary>
        /// <param name="commandName">The name of the command.</param>
        /// <param name="userId">The ID of the user to remove.</param>
        /// <returns>True if the user was removed, false if they weren't on the list.</returns>
        public bool RemoveWhitelistedUser(string commandName, ulong userId)
        {
            if (!_permissions.WhitelistedUsers.ContainsKey(commandName))
            {
                return false;
            }

            bool removed = _permissions.WhitelistedUsers[commandName].Remove(userId);
            if (removed)
            {
                SavePermissions();
            }
            return removed;
        }

        /// <summary>
        /// Adds a user to the blacklist for a command.
        /// </summary>
        /// <param name="commandName">The name of the command.</param>
        /// <param name="userId">The ID of the user to add.</param>
        /// <returns>True if the user was added, false if they were already on the list.</returns>
        public bool AddBlacklistedUser(string commandName, ulong userId)
        {
            if (!_permissions.BlacklistedUsers.ContainsKey(commandName))
            {
                _permissions.BlacklistedUsers[commandName] = new List<ulong>();
            }

            if (_permissions.BlacklistedUsers[commandName].Contains(userId))
            {
                return false;
            }

            _permissions.BlacklistedUsers[commandName].Add(userId);
            SavePermissions();
            return true;
        }

        /// <summary>
        /// Removes a user from the blacklist for a command.
        /// </summary>
        /// <param name="commandName">The name of the command.</param>
        /// <param name="userId">The ID of the user to remove.</param>
        /// <returns>True if the user was removed, false if they weren't on the list.</returns>
        public bool RemoveBlacklistedUser(string commandName, ulong userId)
        {
            if (!_permissions.BlacklistedUsers.ContainsKey(commandName))
            {
                return false;
            }

            bool removed = _permissions.BlacklistedUsers[commandName].Remove(userId);
            if (removed)
            {
                SavePermissions();
            }
            return removed;
        }

        /// <summary>
        /// Gets the permission level for a command.
        /// </summary>
        /// <param name="commandName">The name of the command.</param>
        /// <returns>The permission level for the command.</returns>
        public PermissionLevel GetCommandPermissionLevel(string commandName)
        {
            if (_permissions.CommandPermissions.TryGetValue(commandName, out var level))
            {
                return level;
            }
            return PermissionLevel.Everyone;
        }

        /// <summary>
        /// Checks if a user is a staff member.
        /// </summary>
        /// <param name="userId">The ID of the user to check.</param>
        /// <returns>True if the user is a staff member, false otherwise.</returns>
        public bool IsStaffUser(ulong userId)
        {
            return userId == _ownerId || _permissions.StaffUsers.Contains(userId);
        }

        /// <summary>
        /// Sets the cooldown time for a command.
        /// </summary>
        /// <param name="commandName">The name of the command.</param>
        /// <param name="cooldownSeconds">The cooldown time in seconds.</param>
        public void SetCommandCooldown(string commandName, int cooldownSeconds)
        {
            _permissions.CommandCooldowns[commandName] = cooldownSeconds;
            SavePermissions();
        }

        /// <summary>
        /// Gets the cooldown time for a command.
        /// </summary>
        /// <param name="commandName">The name of the command.</param>
        /// <returns>The cooldown time in seconds, or 0 if no cooldown is set.</returns>
        public int GetCommandCooldown(string commandName)
        {
            if (_permissions.CommandCooldowns.TryGetValue(commandName, out var cooldown))
            {
                return cooldown;
            }
            return 0;
        }

        /// <summary>
        /// Gets the current rate limit window in seconds.
        /// </summary>
        /// <returns>The rate limit window in seconds.</returns>
        public int GetRateLimitWindow()
        {
            return _permissions.RateLimitWindowSeconds;
        }

        /// <summary>
        /// Sets the rate limit window in seconds.
        /// </summary>
        /// <param name="seconds">The new rate limit window in seconds.</param>
        public void SetRateLimitWindow(int seconds)
        {
            if (seconds < 1)
            {
                seconds = 1; // Minimum 1 second
            }
            _permissions.RateLimitWindowSeconds = seconds;
            SavePermissions();
        }

        /// <summary>
        /// Gets the maximum number of different commands a user can execute within the rate limit window.
        /// </summary>
        /// <returns>The maximum number of different commands.</returns>
        public int GetMaxDifferentCommands()
        {
            return _permissions.MaxDifferentCommandsPerWindow;
        }

        /// <summary>
        /// Sets the maximum number of different commands a user can execute within the rate limit window.
        /// </summary>
        /// <param name="count">The new maximum number of different commands.</param>
        public void SetMaxDifferentCommands(int count)
        {
            if (count < 1)
            {
                count = 1; // Minimum 1 command
            }
            _permissions.MaxDifferentCommandsPerWindow = count;
            SavePermissions();
        }

        /// <summary>
        /// Gets the maximum number of times a user can execute the same command within the rate limit window.
        /// </summary>
        /// <returns>The maximum number of same command executions.</returns>
        public int GetMaxSameCommand()
        {
            return _permissions.MaxSameCommandPerWindow;
        }

        /// <summary>
        /// Sets the maximum number of times a user can execute the same command within the rate limit window.
        /// </summary>
        /// <param name="count">The new maximum number of same command executions.</param>
        public void SetMaxSameCommand(int count)
        {
            if (count < 1)
            {
                count = 1; // Minimum 1 command
            }
            _permissions.MaxSameCommandPerWindow = count;
            SavePermissions();
        }

        /// <summary>
        /// Loads permissions from the JSON file.
        /// </summary>
        /// <returns>The loaded permissions, or a new instance if the file doesn't exist.</returns>
        private Permissions LoadPermissions()
        {
            if (!File.Exists(_permissionsFilePath))
            {
                return new Permissions();
            }

            try
            {
                string json = File.ReadAllText(_permissionsFilePath);
                var permissions = JsonSerializer.Deserialize<Permissions>(json);
                return permissions ?? new Permissions();
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error loading permissions: {ex.Message}");
                return new Permissions();
            }
        }

        /// <summary>
        /// Saves the current permissions to the JSON file.
        /// </summary>
        private void SavePermissions()
        {
            try
            {
                // Validate rate limit settings before saving
                ValidateRateLimitSettings();

                var options = new JsonSerializerOptions { WriteIndented = true };
                string json = JsonSerializer.Serialize(_permissions, options);

                // Create a backup of the current file before overwriting
                if (File.Exists(_permissionsFilePath))
                {
                    string backupPath = _permissionsFilePath + ".bak";
                    File.Copy(_permissionsFilePath, backupPath, true);
                }

                File.WriteAllText(_permissionsFilePath, json);
                Console.WriteLine("Permissions saved successfully.");
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error saving permissions: {ex.Message}");
                Console.Error.WriteLine(ex.StackTrace);
            }
        }

        /// <summary>
        /// Validates rate limit settings to ensure they are within acceptable ranges.
        /// </summary>
        private void ValidateRateLimitSettings()
        {
            // Ensure rate limit window is at least 1 second
            if (_permissions.RateLimitWindowSeconds < 1)
            {
                _permissions.RateLimitWindowSeconds = 1;
                Console.WriteLine("Warning: Rate limit window was set below minimum. Adjusted to 1 second.");
            }

            // Cap rate limit window at a reasonable maximum (5 minutes)
            if (_permissions.RateLimitWindowSeconds > 300)
            {
                _permissions.RateLimitWindowSeconds = 300;
                Console.WriteLine("Warning: Rate limit window was set above maximum. Adjusted to 300 seconds (5 minutes).");
            }

            // Ensure max different commands is at least 1
            if (_permissions.MaxDifferentCommandsPerWindow < 1)
            {
                _permissions.MaxDifferentCommandsPerWindow = 1;
                Console.WriteLine("Warning: Max different commands was set below minimum. Adjusted to 1.");
            }

            // Ensure max same command is at least 1
            if (_permissions.MaxSameCommandPerWindow < 1)
            {
                _permissions.MaxSameCommandPerWindow = 1;
                Console.WriteLine("Warning: Max same command was set below minimum. Adjusted to 1.");
            }
        }

        /// <summary>
        /// Loads cooldowns from the JSON file.
        /// </summary>
        private void LoadCooldowns()
        {
            if (!File.Exists(_cooldownsFilePath))
            {
                return;
            }

            try
            {
                string json = File.ReadAllText(_cooldownsFilePath);
                var cooldownData = JsonSerializer.Deserialize<CooldownData>(json);

                if (cooldownData != null && cooldownData.Entries.Count > 0)
                {
                    // Clear existing cooldowns
                    _permissions.LastCommandUsage.Clear();

                    // Add cooldowns from file
                    foreach (var entry in cooldownData.Entries)
                    {
                        _permissions.LastCommandUsage[entry.Key] = entry.Timestamp;
                    }

                    // Clean up expired cooldowns
                    CleanupExpiredCooldowns();
                }
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error loading cooldowns: {ex.Message}");
            }
        }

        /// <summary>
        /// Saves the current cooldowns to the JSON file.
        /// </summary>
        private void SaveCooldowns()
        {
            try
            {
                // Clean up expired cooldowns before saving
                CleanupExpiredCooldowns();

                // Create cooldown data for serialization
                var cooldownData = new CooldownData
                {
                    Entries = _permissions.LastCommandUsage.Select(kv => new CooldownEntry
                    {
                        Key = kv.Key,
                        Timestamp = kv.Value
                    }).ToList()
                };

                var options = new JsonSerializerOptions { WriteIndented = true };
                string json = JsonSerializer.Serialize(cooldownData, options);
                File.WriteAllText(_cooldownsFilePath, json);
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error saving cooldowns: {ex.Message}");
            }
        }

        /// <summary>
        /// Removes expired cooldowns from the LastCommandUsage dictionary.
        /// </summary>
        private void CleanupExpiredCooldowns()
        {
            var now = DateTime.UtcNow;
            var expiredKeys = new List<string>();

            foreach (var kv in _permissions.LastCommandUsage)
            {
                // Extract command name from the key
                string[] parts = kv.Key.Split(':');
                if (parts.Length != 2) continue;

                string commandName = parts[1];

                // Get cooldown for this command
                if (_permissions.CommandCooldowns.TryGetValue(commandName, out int cooldownSeconds) && cooldownSeconds > 0)
                {
                    // Check if cooldown has expired
                    TimeSpan elapsed = now - kv.Value;
                    if (elapsed.TotalSeconds >= cooldownSeconds)
                    {
                        expiredKeys.Add(kv.Key);
                    }
                }
                else
                {
                    // If command has no cooldown, remove the entry
                    expiredKeys.Add(kv.Key);
                }
            }

            // Remove expired cooldowns
            foreach (var key in expiredKeys)
            {
                _permissions.LastCommandUsage.Remove(key);
            }
        }
    }
}
