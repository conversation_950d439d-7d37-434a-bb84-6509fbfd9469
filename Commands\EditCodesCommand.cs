using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;
using System.Text;
using System.Text.RegularExpressions;

namespace BotChan.Commands
{
    /// <summary>
    /// EditCodesCommand - A command that allows staff to manage redemption codes
    /// Usage: %editcodes
    /// Example: %editcodes
    /// </summary>
    public class EditCodesCommand : Command
    {
        // Number of codes to display per page
        private const int CodesPerPage = 7;

        // Override the default category
        public override string Category => "Admin";

        // Override the default usage
        public override string Usage => "%editcodes\n%editcodes add [id] [code] [item_id] [quantity] [expiry_days]\n%editcodes edit [id] [code] [item_id] [quantity] [expiry_days]\n%editcodes remove [id]\n%editcodes page [number]";

        // Override the default examples
        public override string Examples => "%editcodes\n%editcodes add code1 BOTCHAN2023 bot_pack 1 30\n%editcodes edit code1 BOTCHAN2023 bot_pack 2 15\n%editcodes remove code1\n%editcodes page 2";

        public EditCodesCommand() : base("editcodes", "Manage redemption codes (staff only)")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Check if the user is a staff member
            if (!client.Permissions.IsStaffUser(message.Author.Id))
            {
                await message.Channel.SendMessageAsync("You don't have permission to use this command.");
                return;
            }

            // If no arguments, show the list of codes
            if (args.Length == 1)
            {
                await ShowCodes(message, 1, client);
                return;
            }

            // Handle subcommands
            string subCommand = args[1].ToLower();

            switch (subCommand)
            {
                case "add":
                    await AddCode(message, args, client);
                    break;

                case "edit":
                    await EditCode(message, args, client);
                    break;

                case "remove":
                    await RemoveCode(message, args, client);
                    break;

                case "page":
                    // Check if a page number was provided
                    if (args.Length < 3 || !int.TryParse(args[2], out int pageNumber))
                    {
                        await message.Channel.SendMessageAsync("Please specify a valid page number. Example: `%editcodes page 2`");
                        return;
                    }

                    // Show the specified page
                    await ShowCodes(message, pageNumber, client);
                    break;

                default:
                    await message.Channel.SendMessageAsync("Unknown subcommand. Available subcommands: `add`, `edit`, `remove`, `page`");
                    break;
            }
        }

        private async Task ShowCodes(SocketMessage message, int page, DiscordBotClient client)
        {
            // Get all codes
            var allCodes = client.BotSystem.GetAllRedemptionCodes();

            // Calculate total pages
            int totalPages = (int)Math.Ceiling(allCodes.Count / (double)CodesPerPage);
            if (totalPages == 0) totalPages = 1; // At least one page even if empty

            // Ensure page is within valid range
            page = Math.Max(1, Math.Min(page, totalPages));

            // Get codes for the current page
            var pageCodes = allCodes
                .Skip((page - 1) * CodesPerPage)
                .Take(CodesPerPage)
                .ToList();

            // Build the codes list
            var sb = new StringBuilder();

            // Add the header
            sb.AppendLine("%editcodes add [id] [code] [item_id] [quantity]");
            sb.AppendLine("%editcodes edit [id] [code] [item_id] [quantity]");
            sb.AppendLine("%editcodes remove [id]");

            // Add navigation instructions if there are multiple pages
            if (totalPages > 1)
            {
                sb.AppendLine($"%editcodes page [1-{totalPages}] to navigate pages");
            }

            sb.AppendLine("Here are all the available redemption codes:");
            sb.AppendLine("════════════════════════════════");

            // Add the codes
            for (int i = 0; i < pageCodes.Count; i++)
            {
                var code = pageCodes[i];
                int displayId = (page - 1) * CodesPerPage + i + 1; // Calculate the display ID

                // Get the item associated with this code
                var item = client.BotSystem.GetConsumable(code.ConsumableId);
                string itemName = item != null ? item.Name : "Unknown Item";
                string itemEmoji = item != null ? item.Emoji : "❓";

                // Format: ID. CODE - ItemName (Quantity) [Redeemed by X users]
                sb.AppendLine($"{displayId}. **{code.Code}** - {itemEmoji} {itemName} (x{code.Quantity}) [Redeemed by {code.RedeemedBy.Count} users]");
                sb.AppendLine($"   ID: {code.Id}, Item ID: {code.ConsumableId}");

                // Add expiry date if set
                if (code.ExpiryDate.HasValue)
                {
                    string expiryStatus = code.HasExpired() ? "**EXPIRED**" : "Active";
                    sb.AppendLine($"   Expires: {code.ExpiryDate.Value.ToString("yyyy-MM-dd HH:mm")} UTC ({expiryStatus})");
                }
                else
                {
                    sb.AppendLine($"   Expires: Never");
                }
            }

            // Add a blank line at the end for spacing
            sb.AppendLine();

            // Create an embed for the codes list
            var embed = new EmbedBuilder()
                .WithAuthor("Bot-Chan's Code Management", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                .WithDescription(sb.ToString())
                .WithColor(Color.Gold)
                .WithFooter($"Page {page}/{totalPages} |");

            // Send the embed message
            var codesMessage = await message.Channel.SendMessageAsync(embed: embed.Build());
        }

        private async Task AddCode(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Get the full command text
            string fullCommand = message.Content;

            // Parse the command using regex to handle quoted strings properly
            var matches = Regex.Matches(fullCommand, @"[\""].+?[\""]|[^ ]+");
            var parsedArgs = new List<string>();

            foreach (Match match in matches)
            {
                string arg = match.Value;
                // Remove quotes from quoted arguments
                if (arg.StartsWith("\"") && arg.EndsWith("\""))
                {
                    arg = arg.Substring(1, arg.Length - 2);
                }
                parsedArgs.Add(arg);
            }

            // Check if we have enough arguments
            if (parsedArgs.Count < 6)
            {
                await message.Channel.SendMessageAsync("Not enough arguments. Usage: `%editcodes add [id] [code] [item_id] [quantity] [expiry_days]`");
                return;
            }

            // Parse the arguments
            string id = parsedArgs[2];
            string codeValue = parsedArgs[3];
            string itemId = parsedArgs[4];

            // Parse the quantity
            if (!int.TryParse(parsedArgs[5], out int quantity) || quantity <= 0)
            {
                await message.Channel.SendMessageAsync($"Invalid quantity value: '{parsedArgs[5]}'. Must be a positive number.");
                return;
            }

            // Parse the expiry days (optional)
            DateTime? expiryDate = null;
            if (parsedArgs.Count > 6)
            {
                if (int.TryParse(parsedArgs[6], out int expiryDays) && expiryDays > 0)
                {
                    expiryDate = DateTime.UtcNow.AddDays(expiryDays);
                }
                else if (parsedArgs[6].ToLower() != "never" && parsedArgs[6].ToLower() != "0")
                {
                    await message.Channel.SendMessageAsync($"Invalid expiry days value: '{parsedArgs[6]}'. Must be a positive number, 'never', or '0' for no expiry.");
                    return;
                }
            }

            // Check if the item exists
            var item = client.BotSystem.GetConsumable(itemId);
            if (item == null)
            {
                await message.Channel.SendMessageAsync($"Error: No item with ID `{itemId}` found.");
                return;
            }

            // Create the redemption code
            var code = new RedemptionCode
            {
                Id = id,
                Code = codeValue,
                ConsumableId = itemId,
                Quantity = quantity,
                ExpiryDate = expiryDate
            };

            // Add the code
            bool success = client.BotSystem.AddRedemptionCode(code);

            if (success)
            {
                // Create an embed for the response
                var embed = new EmbedBuilder()
                    .WithAuthor("Bot-Chan's Code Management", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                    .WithTitle("Redemption Code Added")
                    .WithDescription($"**{codeValue}** has been added!\n\n" +
                                    $"• ID: `{id}`\n" +
                                    $"• Item: {item.Name} {item.Emoji}\n" +
                                    $"• Quantity: **{quantity}**\n" +
                                    $"• Expires: {(expiryDate.HasValue ? expiryDate.Value.ToString("yyyy-MM-dd HH:mm") + " UTC" : "Never")}")
                    .WithColor(Color.Green)
                    .WithFooter("Users can redeem this code with %usecode");

                await message.Channel.SendMessageAsync(embed: embed.Build());
            }
            else
            {
                await message.Channel.SendMessageAsync($"Error: A redemption code with ID `{id}` already exists.");
            }
        }

        private async Task EditCode(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Get the full command text
            string fullCommand = message.Content;

            // Parse the command using regex to handle quoted strings properly
            var matches = Regex.Matches(fullCommand, @"[\""].+?[\""]|[^ ]+");
            var parsedArgs = new List<string>();

            foreach (Match match in matches)
            {
                string arg = match.Value;
                // Remove quotes from quoted arguments
                if (arg.StartsWith("\"") && arg.EndsWith("\""))
                {
                    arg = arg.Substring(1, arg.Length - 2);
                }
                parsedArgs.Add(arg);
            }

            // Check if we have enough arguments
            if (parsedArgs.Count < 6)
            {
                await message.Channel.SendMessageAsync("Not enough arguments. Usage: `%editcodes edit [id] [code] [item_id] [quantity] [expiry_days]`");
                return;
            }

            // Parse the arguments
            string id = parsedArgs[2];
            string codeValue = parsedArgs[3];
            string itemId = parsedArgs[4];

            // Parse the quantity
            if (!int.TryParse(parsedArgs[5], out int quantity) || quantity <= 0)
            {
                await message.Channel.SendMessageAsync($"Invalid quantity value: '{parsedArgs[5]}'. Must be a positive number.");
                return;
            }

            // Parse the expiry days (optional)
            DateTime? expiryDate = null;
            if (parsedArgs.Count > 6)
            {
                if (int.TryParse(parsedArgs[6], out int expiryDays) && expiryDays > 0)
                {
                    expiryDate = DateTime.UtcNow.AddDays(expiryDays);
                }
                else if (parsedArgs[6].ToLower() == "never" || parsedArgs[6].ToLower() == "0")
                {
                    // No expiry
                    expiryDate = null;
                }
                else
                {
                    await message.Channel.SendMessageAsync($"Invalid expiry days value: '{parsedArgs[6]}'. Must be a positive number, 'never', or '0' for no expiry.");
                    return;
                }
            }
            else
            {
                // If not specified, keep the existing expiry date
                var currentCode = client.BotSystem.GetRedemptionCode(id);
                if (currentCode != null)
                {
                    expiryDate = currentCode.ExpiryDate;
                }
            }

            // Check if the code exists
            var existingCode = client.BotSystem.GetRedemptionCode(id);
            if (existingCode == null)
            {
                await message.Channel.SendMessageAsync($"Error: No redemption code with ID `{id}` found.");
                return;
            }

            // Check if the item exists
            var item = client.BotSystem.GetConsumable(itemId);
            if (item == null)
            {
                await message.Channel.SendMessageAsync($"Error: No item with ID `{itemId}` found.");
                return;
            }

            // Create the updated redemption code
            var code = new RedemptionCode
            {
                Id = id,
                Code = codeValue,
                ConsumableId = itemId,
                Quantity = quantity,
                RedeemedBy = existingCode.RedeemedBy, // Preserve the list of users who have redeemed this code
                ExpiryDate = expiryDate  // Use the new expiry date or keep the existing one
            };

            // Update the code
            bool success = client.BotSystem.UpdateRedemptionCode(code);

            if (success)
            {
                // Create an embed for the response
                var embed = new EmbedBuilder()
                    .WithAuthor("Bot-Chan's Code Management", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                    .WithTitle("Redemption Code Updated")
                    .WithDescription($"**{codeValue}** has been updated!\n\n" +
                                    $"• ID: `{id}`\n" +
                                    $"• Item: {item.Name} {item.Emoji}\n" +
                                    $"• Quantity: **{quantity}**\n" +
                                    $"• Redeemed by: **{code.RedeemedBy.Count}** users\n" +
                                    $"• Expires: {(code.ExpiryDate.HasValue ? code.ExpiryDate.Value.ToString("yyyy-MM-dd HH:mm") + " UTC" : "Never")}")
                    .WithColor(Color.Blue)
                    .WithFooter("Users can redeem this code with %usecode");

                await message.Channel.SendMessageAsync(embed: embed.Build());
            }
            else
            {
                await message.Channel.SendMessageAsync($"Error: Failed to update redemption code with ID `{id}`.");
            }
        }

        private async Task RemoveCode(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Check if we have enough arguments
            if (args.Length < 3)
            {
                await message.Channel.SendMessageAsync("Not enough arguments. Usage: `%editcodes remove [id]`");
                return;
            }

            // Parse the arguments
            string id = args[2];

            // Get the code before removing it
            var code = client.BotSystem.GetRedemptionCode(id);
            if (code == null)
            {
                await message.Channel.SendMessageAsync($"Error: No redemption code with ID `{id}` found.");
                return;
            }

            // Remove the code
            bool success = client.BotSystem.RemoveRedemptionCode(id);

            if (success)
            {
                // Create an embed for the response
                var embed = new EmbedBuilder()
                    .WithAuthor("Bot-Chan's Code Management", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                    .WithTitle("Redemption Code Removed")
                    .WithDescription($"**{code.Code}** has been removed.")
                    .WithColor(Color.Red)
                    .WithFooter("This code can no longer be redeemed");

                await message.Channel.SendMessageAsync(embed: embed.Build());
            }
            else
            {
                await message.Channel.SendMessageAsync($"Error: Failed to remove redemption code with ID `{id}`.");
            }
        }
    }
}
