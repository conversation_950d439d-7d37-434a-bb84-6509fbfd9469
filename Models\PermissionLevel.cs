namespace BotChan.Models
{
    /// <summary>
    /// Defines the permission levels for commands.
    /// </summary>
    public enum PermissionLevel
    {
        /// <summary>
        /// Everyone can use this command (default).
        /// </summary>
        Everyone = 0,

        /// <summary>
        /// Only whitelisted users can use this command.
        /// </summary>
        Whitelist = 1,

        /// <summary>
        /// Only staff members can use this command.
        /// </summary>
        Staff = 2,

        /// <summary>
        /// Command is disabled for everyone except the owner.
        /// </summary>
        Disabled = 3,

        /// <summary>
        /// Only the creator (513793435807907841) can use this command.
        /// </summary>
        Creator = 4
    }
}
