using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;

namespace BotChan.Commands
{
    /// <summary>
    /// DismantleCommand - Allows users to remove their currently active recruitment multiplier
    /// Usage: %dismantle
    /// Example: %dismantle
    /// </summary>
    public class DismantleCommand : Command
    {
        // Override the default category
        public override string Category => "Economy";

        // Override the default usage
        public override string Usage => "%dismantle";

        // Override the default examples
        public override string Examples => "%dismantle";

        // Dictionary to track pending dismantle confirmations
        private static readonly Dictionary<ulong, (string ConsumableId, string ConsumableName, string ConsumableEmoji, DateTime Expiry)> _pendingConfirmations = new Dictionary<ulong, (string, string, string, DateTime)>();

        // Timeout for confirmations (30 seconds)
        private const int ConfirmationTimeoutSeconds = 30;

        // Static reference to the client for button handling
        private static DiscordBotClient _clientInstance;

        // Flag to track if the button handler has been registered
        private static bool _buttonHandlerRegistered = false;

        public DismantleCommand() : base("dismantle", "Remove your currently active recruitment multiplier")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Register the button handler if not already done
            if (!_buttonHandlerRegistered)
            {
                _clientInstance = client;
                client._client.ButtonExecuted += OnButtonExecuted;
                _buttonHandlerRegistered = true;
            }

            // Get the user's data
            var userData = client.BotSystem.GetUserData(message.Author.Id, message.Author.Username);

            // Check if the user has an active multiplier
            if (string.IsNullOrEmpty(userData.ActiveConsumableId) || userData.ActiveConsumableUsesRemaining <= 0)
            {
                await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1410316642180796446> | {message.Author.Username}**, you don't have a multiplier currently active. This command is used to dismantle multipliers for %recruit command! Please use a multiplier and try again!");
                return;
            }

            // Get the active consumable details
            var activeConsumable = client.BotSystem.GetConsumable(userData.ActiveConsumableId);
            if (activeConsumable == null)
            {
                await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1410316642180796446> | {message.Author.Username}**, there was an error retrieving your active multiplier information. Please try again.");
                return;
            }

            // Store the confirmation data
            _pendingConfirmations[message.Author.Id] = (
                userData.ActiveConsumableId,
                activeConsumable.Name,
                activeConsumable.Emoji,
                DateTime.UtcNow.AddSeconds(ConfirmationTimeoutSeconds)
            );

            // Create buttons for confirmation
            var dismantleButton = new ButtonBuilder()
                .WithLabel("Dismantle")
                .WithStyle(ButtonStyle.Danger)
                .WithCustomId($"dismantle_confirm_{message.Author.Id}")
                .WithEmote(new Emoji("🗑️"));

            var keepButton = new ButtonBuilder()
                .WithLabel("Keep")
                .WithStyle(ButtonStyle.Success)
                .WithCustomId($"dismantle_cancel_{message.Author.Id}")
                .WithEmote(new Emoji("✅"));

            var components = new ComponentBuilder()
                .WithButton(dismantleButton)
                .WithButton(keepButton)
                .Build();

            // Send confirmation message with buttons
            await message.Channel.SendMessageAsync(
                text: $"**<:BM_BotChanReaction:1143303450369736754> | {message.Author.Username}**, would you like to dismantle the current multiplier item? You're currently using **{activeConsumable.Name}** {activeConsumable.Emoji}. The item will be lost permanently!",
                components: components);
        }

        /// <summary>
        /// Handles button interactions for dismantle confirmations
        /// </summary>
        private static async Task OnButtonExecuted(SocketMessageComponent component)
        {
            var customId = component.Data.CustomId;
            var userId = component.User.Id;

            // Check if this is a dismantle button
            if (!customId.StartsWith("dismantle_"))
                return;

            // Extract the user ID from the custom ID
            var parts = customId.Split('_');
            if (parts.Length != 3 || !ulong.TryParse(parts[2], out var targetUserId))
                return;

            // Only allow the original user to interact with their buttons
            if (userId != targetUserId)
            {
                await component.RespondAsync("❌ You can only interact with your own dismantle confirmation.", ephemeral: true);
                return;
            }

            // Check if the confirmation still exists and hasn't expired
            if (!_pendingConfirmations.TryGetValue(userId, out var confirmation) || DateTime.UtcNow > confirmation.Expiry)
            {
                _pendingConfirmations.Remove(userId);
                await component.RespondAsync("❌ This dismantle confirmation has expired. Please use the command again.", ephemeral: true);
                return;
            }

            var action = parts[1]; // "confirm" or "cancel"

            if (action == "confirm")
            {
                // Remove the confirmation
                _pendingConfirmations.Remove(userId);

                // Get the user's data
                var userData = _clientInstance.BotSystem.GetUserData(userId);

                // Verify the user still has the same active consumable
                if (userData.ActiveConsumableId != confirmation.ConsumableId)
                {
                    await component.RespondAsync("❌ Your active multiplier has changed since the confirmation was requested. Please try again.", ephemeral: true);
                    return;
                }

                // Remove the active consumable
                userData.ActiveConsumableId = string.Empty;
                userData.ActiveConsumableUsesRemaining = 0;
                userData.ActiveConsumableMultiplier = 1.0;

                // Save the user data
                _clientInstance.BotSystem.SaveUserData();

                // Update the message to show completion
                await component.UpdateAsync(msg =>
                {
                    msg.Content = $"**<a:BM_BotChanHappy:1158391055998259270> | {component.User.Username}**, you have successfully dismantled **{confirmation.ConsumableName}** {confirmation.ConsumableEmoji}! Your multiplier has been removed.";
                    msg.Components = new ComponentBuilder().Build(); // Remove buttons
                });
            }
            else if (action == "cancel")
            {
                // Remove the confirmation
                _pendingConfirmations.Remove(userId);

                // Update the message to show cancellation
                await component.UpdateAsync(msg =>
                {
                    msg.Content = $"**<:BM_BotChanReaction:1143303450369736754> | {component.User.Username}**, dismantle cancelled. Your **{confirmation.ConsumableName}** {confirmation.ConsumableEmoji} multiplier remains active.";
                    msg.Components = new ComponentBuilder().Build(); // Remove buttons
                });
            }
        }
    }
}
