using Discord;
using Discord.WebSocket;
using BotChan.Services;
using BotChan.Models;

namespace BotChan.Commands.SlashCommands
{
    /// <summary>
    /// BotsSlashCommand - Slash command version of the bots command
    /// Usage: /bots
    /// Shows the user's bot count with silent mode support
    /// </summary>
    public class BotsSlashCommand
    {
        /// <summary>
        /// Executes the bots slash command.
        /// </summary>
        /// <param name="command">The slash command that was executed</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task ExecuteAsync(SocketSlashCommand command, DiscordBotClient client)
        {
            try
            {
                // Check cooldown before allowing the command (same as %bots)
                if (!client.Permissions.HasPermission(command.User.Id, "bots", out string cooldownMessage))
                {
                    if (!string.IsNullOrEmpty(cooldownMessage))
                    {
                        await command.RespondAsync($"⏰ **Bots command is on cooldown!** {cooldownMessage}", ephemeral: true);
                    }
                    else
                    {
                        await command.RespondAsync("❌ You don't have permission to use this command.", ephemeral: true);
                    }
                    return;
                }

                // Update last usage time for cooldown tracking
                client.Permissions.UpdateCommandUsage(command.User.Id, "bots");

                // Get user data
                var userData = client.BotSystem.GetUserData(command.User.Id, command.User.Username);

                // Create the embed response (match %bots format exactly)
                var embed = new EmbedBuilder()
                    .WithTitle("Your own Bot Army!")
                    .WithColor(Color.Blue)
                    .WithThumbnailUrl("https://cdn.discordapp.com/attachments/1363245551184908468/1363320873498447923/Bot_Army.png?ex=68059b01&is=68044981&hm=2e478e2f9c02fea6bb21f47b6c6f82e8aeb1f54a7bf1a29e2dd0778bf2ddcb09&")
                    .WithDescription($"You have collected **{userData.BotCount}** bots!")
                    .WithFooter("Use %recruit to get more bots")
                    .WithCurrentTimestamp();



                // Respond with the embed
                await command.RespondAsync(embed: embed.Build());
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing bots slash command: {ex.Message}");
                
                if (!command.HasResponded)
                {
                    await command.RespondAsync("❌ An error occurred while retrieving your bot count.", ephemeral: true);
                }
            }
        }
    }
}
