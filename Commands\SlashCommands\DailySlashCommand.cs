using Discord;
using Discord.WebSocket;
using BotChan.Services;
using BotChan.Models;

namespace BotChan.Commands.SlashCommands
{
    /// <summary>
    /// DailySlashCommand - Slash command version of the daily command
    /// Usage: /daily
    /// Gives the user daily bot points
    /// </summary>
    public class DailySlashCommand
    {
        // The amount of bots to give for the daily reward
        private const long DailyReward = 25;

        /// <summary>
        /// Executes the daily slash command.
        /// </summary>
        /// <param name="command">The slash command that was executed</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task ExecuteAsync(SocketSlashCommand command, DiscordBotClient client)
        {
            try
            {
                // Check cooldown before allowing the command (same as %daily)
                if (!client.Permissions.HasPermission(command.User.Id, "daily", out string cooldownMessage))
                {
                    if (!string.IsNullOrEmpty(cooldownMessage))
                    {
                        await command.RespondAsync($"⏰ **Daily command is on cooldown!** {cooldownMessage}", ephemeral: true);
                    }
                    else
                    {
                        await command.RespondAsync("❌ You don't have permission to use this command.", ephemeral: true);
                    }
                    return;
                }

                // Add the daily reward to the user's account
                bool fullAmountAdded = client.BotSystem.AddBots(command.User.Id, DailyReward);

                // Get the user's updated data
                var userData = client.BotSystem.GetUserData(command.User.Id, command.User.Username);

                // Create the response message with the specified format and directly embedded emojis (exact same as %daily)
                string responseMessage = $"<a:BotChanDance2:1410331782607540245> | **{command.User.Username}**, you claimed your daily reward of **{DailyReward}** {Constants.Emojis.BotDown}!\n\n**Total Bots:** {userData.BotCount} {Constants.Emojis.BotDown}";

                // Check if the user hit the bot limit
                if (!fullAmountAdded)
                {
                    responseMessage += $"\n\n⚠️ **Note:** You've reached the maximum bot limit of 1,000,000. Some bots couldn't be added.";
                }

                await command.RespondAsync(responseMessage);

                // Update the command usage time for cooldown tracking (same as %daily)
                client.Permissions.UpdateCommandUsage(command.User.Id, "daily");

                // Get the daily summary message and always reset the stats
                // This way, using daily again will show stats since the last reset
                string summaryMessage = userData.GetDailySummary(true);

                // Send the summary message as a follow-up
                await command.FollowupAsync(summaryMessage);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing daily slash command: {ex.Message}");
                
                if (!command.HasResponded)
                {
                    await command.RespondAsync("❌ An error occurred while processing the daily command.", ephemeral: true);
                }
            }
        }
    }
}
