using Discord;
using Discord.WebSocket;
using BotChan.Services;
using BotChan.Models;

namespace BotChan.Commands.SlashCommands
{
    /// <summary>
    /// UseCodeSlashCommand - Slash command version of the usecode command
    /// Usage: /usecode code
    /// Redeem a code for items
    /// </summary>
    public class UseCodeSlashCommand
    {
        /// <summary>
        /// Executes the usecode slash command.
        /// </summary>
        /// <param name="command">The slash command that was executed</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task ExecuteAsync(SocketSlashCommand command, DiscordBotClient client)
        {
            try
            {
                // Check cooldown before allowing the command (same as %usecode)
                if (!client.Permissions.HasPermission(command.User.Id, "usecode", out string cooldownMessage))
                {
                    if (!string.IsNullOrEmpty(cooldownMessage))
                    {
                        await command.RespondAsync($"⏰ **Use code command is on cooldown!** {cooldownMessage}", ephemeral: true);
                    }
                    else
                    {
                        await command.RespondAsync("❌ You don't have permission to use this command.", ephemeral: true);
                    }
                    return;
                }

                // Update last usage time for cooldown tracking
                client.Permissions.UpdateCommandUsage(command.User.Id, "usecode");

                // Get command options
                var codeOption = command.Data.Options.FirstOrDefault(x => x.Name == "code");

                if (codeOption == null)
                {
                    await command.RespondAsync("Please specify a code to redeem. Example: `/usecode NEWBEGINNINGS`", ephemeral: true);
                    return;
                }

                // Get the code
                string code = codeOption.Value.ToString();

                // Try to redeem the code
                var result = client.BotSystem.RedeemCode(command.User.Id, code);

                if (result.Success && result.Consumable != null)
                {
                    // Create a success message (exact same as %usecode)
                    string responseMessage;

                    if (result.Consumable.BotReward > 0)
                    {
                        // This is a bot reward item
                        int totalBotReward = result.Consumable.BotReward * result.Quantity;
                        responseMessage = $"<a:BotChanHappy:1410317358852997233> ✨ | {command.User.Username}, you redeemed the code **{code}** and received:\n\n" +
                            $"**{result.Quantity}x {result.Consumable.Name}** {result.Consumable.Emoji}\n" +
                            $"This gives you a total of **{totalBotReward}** <:BotDown:1410320403469176913>!\n\n" +
                            $"The item(s) have been added to your inventory. Use `/use item_id:[inventory_id]` to use them.";
                    }
                    else
                    {
                        // This is a consumable item
                        responseMessage = $"<a:BotChanHappy:1410317358852997233> ✨ | {command.User.Username}, you redeemed the code **{code}** and received:\n\n" +
                            $"**{result.Quantity}x {result.Consumable.Name}** {result.Consumable.Emoji}\n" +
                            $"This gives you a **{result.Consumable.Multiplier}x** consumable for **{result.Consumable.DefaultUses}** uses!\n\n" +
                            $"The item(s) have been added to your inventory. Use `/use item_id:[inventory_id]` to use them.";
                    }

                    // Send the response
                    await command.RespondAsync(responseMessage);
                }
                else
                {
                    // Send the error message (exact same as %usecode)
                    await command.RespondAsync($"<:BotChanDespair:1410316642180796446> | {command.User.Username}, {result.ErrorMessage}", ephemeral: true);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing usecode slash command: {ex.Message}");
                
                if (!command.HasResponded)
                {
                    await command.RespondAsync("❌ An error occurred while processing the usecode command.", ephemeral: true);
                }
            }
        }
    }
}
