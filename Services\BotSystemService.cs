using System.Text.Json;
using BotChan.Models;

namespace BotChan.Services
{
    /// <summary>
    /// Service for managing the bot system.
    /// </summary>
    public class BotSystemService
    {
        private Dictionary<ulong, UserData> _userData = new Dictionary<ulong, UserData>();
        private BotSystemConfig _config;
        private readonly string _userDataFilePath;
        private readonly string _configFilePath;

        // Lock object for thread safety
        private readonly object _saveLock = new object();

        // Timer for delayed saving
        private System.Timers.Timer _saveTimer;

        // Flag to track if data has been modified and needs saving
        private bool _dataModified = false;

        /// <summary>
        /// Initializes a new instance of the BotSystemService.
        /// </summary>
        public BotSystemService()
        {
            _userDataFilePath = Path.Combine(Directory.GetCurrentDirectory(), "userdata.json");
            _configFilePath = Path.Combine(Directory.GetCurrentDirectory(), "botsystemconfig.json");

            LoadUserData();
            LoadConfig();

            // Initialize and configure the save timer (5 minutes = 300,000 ms)
            _saveTimer = new System.Timers.Timer(300000);
            _saveTimer.Elapsed += (sender, e) => SaveUserDataIfModified();
            _saveTimer.AutoReset = true;
            _saveTimer.Start();

            // Log timer initialization
            Console.WriteLine("User data save timer initialized (5 minute interval)");

            // Register application exit event to save data
            AppDomain.CurrentDomain.ProcessExit += (sender, e) => ForceSaveUserData();
        }

        /// <summary>
        /// Gets the user data for a specific user.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="username">Optional username to update in the user data.</param>
        /// <returns>The user's data.</returns>
        public UserData GetUserData(ulong userId, string username = null)
        {
            if (!_userData.TryGetValue(userId, out var userData))
            {
                // Create new user data if it doesn't exist
                userData = new UserData { UserId = userId };
                if (!string.IsNullOrEmpty(username))
                {
                    userData.Username = username;
                }
                _userData[userId] = userData;
                SaveUserData();
            }
            else if (!string.IsNullOrEmpty(username) && userData.Username != username)
            {
                // Update the username if it has changed
                userData.Username = username;
                userData.LastSeen = DateTime.UtcNow;
                SaveUserData();
            }

            return userData;
        }

        /// <summary>
        /// Gets the user data for a specific user and updates it with Discord user information.
        /// </summary>
        /// <param name="user">The Discord user object.</param>
        /// <returns>The user's data.</returns>
        public UserData GetUserData(Discord.IUser user)
        {
            if (user == null) return null;

            if (!_userData.TryGetValue(user.Id, out var userData))
            {
                // Create new user data if it doesn't exist
                userData = new UserData { UserId = user.Id };
                _userData[user.Id] = userData;
            }

            // Update the user information
            userData.UpdateUserInfo(user);
            SaveUserData();

            return userData;
        }

        /// <summary>
        /// Gets all user data for leaderboards.
        /// </summary>
        /// <returns>A list of all user data.</returns>
        public List<UserData> GetAllUserData()
        {
            return _userData.Values.ToList();
        }

        /// <summary>
        /// Updates user information for all users in the system from Discord.
        /// </summary>
        /// <param name="client">The Discord client to use for fetching user information.</param>
        /// <returns>The number of users that were updated.</returns>
        public int UpdateAllUserInfo(Discord.WebSocket.DiscordSocketClient client)
        {
            if (client == null) return 0;

            int updatedCount = 0;

            foreach (var userData in _userData.Values.ToList())
            {
                var user = client.GetUser(userData.UserId);
                if (user != null)
                {
                    // Update the user information
                    userData.UpdateUserInfo(user);
                    updatedCount++;
                }

                // Check if we need to reset war stats (if it's a new day)
                DateTime now = DateTime.UtcNow;
                if (userData.LastWarStatsReset.Date < now.Date)
                {
                    // It's a new day, reset the war stats
                    userData.ResetWarStats();
                }
            }

            // Save changes if any users were updated
            if (updatedCount > 0)
            {
                SaveUserData();
            }

            return updatedCount;
        }

        /// <summary>
        /// Resets a user's data (bots and inventory).
        /// </summary>
        /// <param name="userId">The ID of the user to reset.</param>
        public void ResetUser(ulong userId)
        {
            if (_userData.TryGetValue(userId, out var userData))
            {
                // Reset bot count
                userData.BotCount = 0;

                // Clear inventory
                userData.Inventory.Clear();

                // Clear active consumable
                userData.ActiveConsumableId = string.Empty;
                userData.ActiveConsumableUsesRemaining = 0;
                userData.ActiveConsumableMultiplier = 1.0;

                // Save changes
                SaveUserData();
            }
        }

        /// <summary>
        /// Resets all users' data (bots and inventory).
        /// </summary>
        /// <returns>The number of users that were reset.</returns>
        public int ResetAllUsers()
        {
            int count = 0;

            // Log before reset
            Console.WriteLine($"Resetting all users. Current user count: {_userData.Count}");

            foreach (var userData in _userData.Values)
            {
                // Reset bot count
                userData.BotCount = 0;

                // Clear inventory
                userData.Inventory.Clear();

                // Clear active consumable
                userData.ActiveConsumableId = string.Empty;
                userData.ActiveConsumableUsesRemaining = 0;
                userData.ActiveConsumableMultiplier = 1.0;

                count++;
            }

            // Mark data as modified but don't force an immediate save
            // The calling code should call ForceSaveUserData() if an immediate save is needed
            lock (_saveLock)
            {
                _dataModified = true;
            }

            // Log after reset
            Console.WriteLine($"Reset complete. Reset {count} users.");

            return count;
        }

        /// <summary>
        /// Removes users from the database.
        /// </summary>
        /// <param name="userIds">The IDs of the users to remove.</param>
        /// <returns>The number of users that were removed.</returns>
        public int RemoveUsers(List<ulong> userIds)
        {
            int count = 0;

            foreach (var userId in userIds)
            {
                if (_userData.Remove(userId))
                {
                    count++;
                }
            }

            // Save changes if any users were removed
            if (count > 0)
            {
                // Mark data as modified but don't force an immediate save
                // The calling code should call ForceSaveUserData() if an immediate save is needed
                lock (_saveLock)
                {
                    _dataModified = true;
                }

                // Log the removal for debugging
                Console.WriteLine($"Removed {count} users from the database. Current user count: {_userData.Count}");
            }

            return count;
        }

        /// <summary>
        /// Gets the bot system configuration.
        /// </summary>
        public BotSystemConfig Config => _config;

        /// <summary>
        /// Activates lockdown mode, preventing non-staff users from using commands.
        /// </summary>
        /// <param name="userId">The ID of the user activating lockdown.</param>
        /// <param name="reason">The reason for the lockdown.</param>
        /// <returns>True if lockdown was activated, false if it was already active.</returns>
        public bool ActivateLockdown(ulong userId, string reason)
        {
            if (_config.IsLockdownActive)
            {
                return false; // Already in lockdown
            }

            _config.IsLockdownActive = true;
            _config.LockdownReason = reason;
            _config.LockdownActivatedBy = userId;
            _config.LockdownActivatedAt = DateTime.UtcNow;

            SaveConfig();
            return true;
        }

        /// <summary>
        /// Deactivates lockdown mode, allowing all users to use commands again.
        /// </summary>
        /// <returns>True if lockdown was deactivated, false if it wasn't active.</returns>
        public bool DeactivateLockdown()
        {
            if (!_config.IsLockdownActive)
            {
                return false; // Not in lockdown
            }

            _config.IsLockdownActive = false;
            _config.LockdownReason = string.Empty;

            SaveConfig();
            return true;
        }

        /// <summary>
        /// Checks if the bot is currently in lockdown mode.
        /// </summary>
        /// <returns>True if in lockdown, false otherwise.</returns>
        public bool IsInLockdown()
        {
            return _config.IsLockdownActive;
        }

        /// <summary>
        /// Gets information about the current lockdown.
        /// </summary>
        /// <returns>A tuple containing the reason, activator ID, and activation time.</returns>
        public (string Reason, ulong ActivatedBy, DateTime ActivatedAt) GetLockdownInfo()
        {
            return (_config.LockdownReason, _config.LockdownActivatedBy, _config.LockdownActivatedAt);
        }

        /// <summary>
        /// Gets a consumable by its ID.
        /// </summary>
        /// <param name="consumableId">The ID of the consumable.</param>
        /// <returns>The consumable, or null if it doesn't exist.</returns>
        public Consumable GetConsumable(string consumableId)
        {
            return _config.AvailableConsumables.FirstOrDefault(c => c.Id == consumableId);
        }

        /// <summary>
        /// Gets a consumable by its numeric ID.
        /// </summary>
        /// <param name="id">The numeric ID of the consumable.</param>
        /// <returns>The consumable, or null if it doesn't exist.</returns>
        public Consumable GetConsumable(int id)
        {
            // Convert the index to a zero-based index
            int index = id - 1;

            // Check if the index is valid
            if (index >= 0 && index < _config.AvailableConsumables.Count)
            {
                return _config.AvailableConsumables[index];
            }

            return null;
        }

        /// <summary>
        /// Gets all consumables.
        /// </summary>
        /// <returns>A list of all consumables.</returns>
        public List<Consumable> GetAllConsumables()
        {
            return _config.AvailableConsumables;
        }

        /// <summary>
        /// Gets the next available inventory item ID.
        /// </summary>
        /// <returns>The next available ID.</returns>
        public int GetNextInventoryItemId()
        {
            // Find the highest ID currently in use
            int highestId = 0;
            foreach (var userData in _userData.Values)
            {
                foreach (var item in userData.Inventory)
                {
                    if (item.InventoryId > highestId)
                    {
                        highestId = item.InventoryId;
                    }
                }
            }

            // Return the next available ID
            return highestId + 1;
        }

        /// <summary>
        /// Adds a new consumable to the system.
        /// </summary>
        /// <param name="consumable">The consumable to add.</param>
        /// <returns>True if the consumable was added, false if a consumable with the same ID already exists.</returns>
        public bool AddConsumable(Consumable consumable)
        {
            if (_config.AvailableConsumables.Any(c => c.Id == consumable.Id))
            {
                return false;
            }

            _config.AvailableConsumables.Add(consumable);
            SaveConfig();
            return true;
        }

        /// <summary>
        /// Updates an existing consumable.
        /// </summary>
        /// <param name="consumable">The consumable to update.</param>
        /// <returns>True if the consumable was updated, false if it doesn't exist.</returns>
        public bool UpdateConsumable(Consumable consumable)
        {
            var existingConsumable = _config.AvailableConsumables.FirstOrDefault(c => c.Id == consumable.Id);
            if (existingConsumable == null)
            {
                return false;
            }

            // Update the consumable properties
            existingConsumable.Name = consumable.Name;
            existingConsumable.Description = consumable.Description;
            existingConsumable.Multiplier = consumable.Multiplier;
            existingConsumable.BotReward = consumable.BotReward;
            existingConsumable.Cost = consumable.Cost;
            existingConsumable.DefaultUses = consumable.DefaultUses;
            existingConsumable.IsVisibleInShop = consumable.IsVisibleInShop;
            existingConsumable.Emoji = consumable.Emoji;
            existingConsumable.Category = consumable.Category;
            // Update bomb properties
            existingConsumable.BombSuccessRate = consumable.BombSuccessRate;
            existingConsumable.BombWinPercentage = consumable.BombWinPercentage;
            existingConsumable.BombLosePercentage = consumable.BombLosePercentage;
            // Update legacy bomb properties for backward compatibility
            existingConsumable.BombMinDamage = consumable.BombMinDamage;
            existingConsumable.BombMaxDamage = consumable.BombMaxDamage;
            existingConsumable.BombFailurePenalty = consumable.BombFailurePenalty;

            SaveConfig();
            return true;
        }

        /// <summary>
        /// Removes a consumable from the system.
        /// </summary>
        /// <param name="consumableId">The ID of the consumable to remove.</param>
        /// <returns>True if the consumable was removed, false if it doesn't exist.</returns>
        public bool RemoveConsumable(string consumableId)
        {
            var consumable = _config.AvailableConsumables.FirstOrDefault(c => c.Id == consumableId);
            if (consumable == null)
            {
                return false;
            }

            _config.AvailableConsumables.Remove(consumable);
            SaveConfig();
            return true;
        }

        /// <summary>
        /// Purchases a consumable for a user.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="consumableId">The ID of the consumable to purchase.</param>
        /// <returns>True if the purchase was successful, false otherwise.</returns>
        public bool PurchaseConsumable(ulong userId, string consumableId)
        {
            var userData = GetUserData(userId);
            var consumable = GetConsumable(consumableId);

            if (consumable == null)
            {
                return false;
            }

            if (userData.BotCount < consumable.Cost)
            {
                return false;
            }

            userData.RemoveBots(consumable.Cost);
            userData.AddConsumable(consumableId, consumable.DefaultUses);
            SaveUserData();
            return true;
        }

        /// <summary>
        /// Uses a consumable for a user by its consumable ID.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="consumableId">The ID of the consumable to use.</param>
        /// <returns>The multiplier if the consumable was used, 1.0 otherwise.</returns>
        public double UseConsumable(ulong userId, string consumableId)
        {
            var userData = GetUserData(userId);
            var consumable = GetConsumable(consumableId);

            if (consumable == null || !userData.UseConsumable(consumableId))
            {
                return 1.0;
            }

            SaveUserData();
            return consumable.Multiplier;
        }

        /// <summary>
        /// Uses a consumable for a user by its inventory ID.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="inventoryId">The inventory ID of the consumable to use.</param>
        /// <returns>A tuple containing the multiplier and bot reward if the consumable was used, (1.0, 0) otherwise.</returns>
        public (double Multiplier, int BotReward) UseConsumableByInventoryId(ulong userId, int inventoryId)
        {
            var userData = GetUserData(userId);
            var userConsumable = userData.GetConsumableByInventoryId(inventoryId);

            if (userConsumable == null)
            {
                return (1.0, 0);
            }

            var consumable = GetConsumable(userConsumable.ConsumableId);

            if (consumable == null || !userData.UseConsumableByInventoryId(inventoryId))
            {
                return (1.0, 0);
            }

            SaveUserData();
            return (consumable.Multiplier, consumable.BotReward);
        }

        /// <summary>
        /// Adds bots to a user's count, up to the maximum limit of 1,000,000.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="amount">The amount of bots to add.</param>
        /// <returns>True if the full amount was added, false if the limit was reached.</returns>
        public bool AddBots(ulong userId, long amount)
        {
            var userData = GetUserData(userId);
            bool fullAmountAdded = userData.AddBots(amount);
            SaveUserData();

            // Log if the user hit the bot limit
            if (!fullAmountAdded)
            {
                Console.WriteLine($"User {userId} reached the maximum bot limit of 1,000,000");
            }

            return fullAmountAdded;
        }

        /// <summary>
        /// Removes bots from a user's count.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="amount">The amount of bots to remove.</param>
        /// <returns>True if the user had enough bots, false otherwise.</returns>
        public bool RemoveBots(ulong userId, long amount)
        {
            var userData = GetUserData(userId);
            bool success = userData.RemoveBots(amount);
            if (success)
            {
                SaveUserData();
            }
            return success;
        }

        /// <summary>
        /// Loads user data from the JSON file.
        /// </summary>
        private void LoadUserData()
        {
            if (!File.Exists(_userDataFilePath))
            {
                _userData = new Dictionary<ulong, UserData>();
                return;
            }

            try
            {
                string json = File.ReadAllText(_userDataFilePath);
                var userDataList = JsonSerializer.Deserialize<List<UserData>>(json);

                if (userDataList != null)
                {
                    _userData = userDataList.ToDictionary(u => u.UserId);
                }
                else
                {
                    _userData = new Dictionary<ulong, UserData>();
                }
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error loading user data: {ex.Message}");
                _userData = new Dictionary<ulong, UserData>();
            }
        }

        /// <summary>
        /// Marks the data as modified and schedules a save operation.
        /// This is the primary method that should be called when data changes.
        /// </summary>
        public void SaveUserData()
        {
            lock (_saveLock)
            {
                _dataModified = true;
            }

            // Reset the timer to give a full 5 minutes before saving
            // This prevents frequent saves when multiple commands are used in succession
            // Use try-catch to prevent timer operations from blocking slash commands
            _saveTimer?.Stop();
            _saveTimer?.Start();
        }

        /// <summary>
        /// Saves user data to the JSON file if it has been modified.
        /// This is called by the timer.
        /// </summary>
        private void SaveUserDataIfModified()
        {
            // Log that the timer has triggered
            Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] Auto-save timer triggered - checking if data needs to be saved...");

            bool shouldSave = false;

            lock (_saveLock)
            {
                shouldSave = _dataModified;
                if (shouldSave)
                {
                    _dataModified = false;
                }
            }

            if (shouldSave)
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] Data has been modified - performing auto-save...");
                ForceSaveUserData();
            }
            else
            {
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] No changes detected - skipping auto-save");
            }
        }

        /// <summary>
        /// Forces an immediate save of user data regardless of the modified flag.
        /// This is used when the application is exiting or when an immediate save is required.
        /// </summary>
        public void ForceSaveUserData()
        {
            try
            {
                lock (_saveLock)
                {
                    var options = new JsonSerializerOptions { WriteIndented = true };

                    // Convert to list first to ensure we're working with a clean copy
                    var userDataList = _userData.Values.ToList();

                    // Log the save operation with timestamp
                    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] Saving user data. User count: {userDataList.Count}");

                    // Serialize and save
                    string json = JsonSerializer.Serialize(userDataList, options);
                    File.WriteAllText(_userDataFilePath, json);

                    // Reset the modified flag
                    _dataModified = false;

                    // Log success with timestamp
                    Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] User data saved successfully.");
                }
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ERROR saving user data: {ex.Message}");
            }
        }

        /// <summary>
        /// Loads the bot system configuration from the JSON file.
        /// </summary>
        private void LoadConfig()
        {
            if (!File.Exists(_configFilePath))
            {
                _config = new BotSystemConfig();
                return;
            }

            try
            {
                string json = File.ReadAllText(_configFilePath);
                var config = JsonSerializer.Deserialize<BotSystemConfig>(json);
                _config = config ?? new BotSystemConfig();
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error loading bot system config: {ex.Message}");
                _config = new BotSystemConfig();
            }
        }

        /// <summary>
        /// Saves the bot system configuration to the JSON file.
        /// </summary>
        private void SaveConfig()
        {
            try
            {
                var options = new JsonSerializerOptions { WriteIndented = true };
                string json = JsonSerializer.Serialize(_config, options);
                File.WriteAllText(_configFilePath, json);
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error saving bot system config: {ex.Message}");
            }
        }

        #region Redemption Code Methods

        /// <summary>
        /// Gets a redemption code by its ID.
        /// </summary>
        /// <param name="codeId">The ID of the redemption code.</param>
        /// <returns>The redemption code, or null if it doesn't exist.</returns>
        public RedemptionCode GetRedemptionCode(string codeId)
        {
            return _config.RedemptionCodes.FirstOrDefault(c => c.Id == codeId);
        }

        /// <summary>
        /// Gets a redemption code by its code value.
        /// </summary>
        /// <param name="code">The code value to look up.</param>
        /// <returns>The redemption code, or null if it doesn't exist.</returns>
        public RedemptionCode GetRedemptionCodeByValue(string code)
        {
            return _config.RedemptionCodes.FirstOrDefault(c => c.Code.Equals(code, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Gets all redemption codes.
        /// </summary>
        /// <returns>A list of all redemption codes.</returns>
        public List<RedemptionCode> GetAllRedemptionCodes()
        {
            return _config.RedemptionCodes;
        }

        /// <summary>
        /// Adds a new redemption code.
        /// </summary>
        /// <param name="code">The redemption code to add.</param>
        /// <returns>True if the code was added, false if a code with the same ID already exists.</returns>
        public bool AddRedemptionCode(RedemptionCode code)
        {
            if (_config.RedemptionCodes.Any(c => c.Id == code.Id))
            {
                return false;
            }

            _config.RedemptionCodes.Add(code);
            SaveConfig();
            return true;
        }

        /// <summary>
        /// Updates an existing redemption code.
        /// </summary>
        /// <param name="code">The redemption code to update.</param>
        /// <returns>True if the code was updated, false if it doesn't exist.</returns>
        public bool UpdateRedemptionCode(RedemptionCode code)
        {
            var existingCode = _config.RedemptionCodes.FirstOrDefault(c => c.Id == code.Id);
            if (existingCode == null)
            {
                return false;
            }

            // Update the code properties
            existingCode.Code = code.Code;
            existingCode.ConsumableId = code.ConsumableId;
            existingCode.Quantity = code.Quantity;
            existingCode.ExpiryDate = code.ExpiryDate;

            SaveConfig();
            return true;
        }

        /// <summary>
        /// Removes a redemption code.
        /// </summary>
        /// <param name="codeId">The ID of the redemption code to remove.</param>
        /// <returns>True if the code was removed, false if it doesn't exist.</returns>
        public bool RemoveRedemptionCode(string codeId)
        {
            var code = _config.RedemptionCodes.FirstOrDefault(c => c.Id == codeId);
            if (code == null)
            {
                return false;
            }

            _config.RedemptionCodes.Remove(code);
            SaveConfig();
            return true;
        }

        /// <summary>
        /// Redeems a code for a user.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="codeValue">The code value to redeem.</param>
        /// <returns>A tuple containing success status, the consumable, and quantity if successful.</returns>
        public (bool Success, Consumable? Consumable, int Quantity, string ErrorMessage) RedeemCode(ulong userId, string codeValue)
        {
            // Find the code
            var code = GetRedemptionCodeByValue(codeValue);
            if (code == null)
            {
                return (false, null, 0, "Invalid code. Please check the code and try again.");
            }

            // Check if the code has expired
            if (code.HasExpired())
            {
                return (false, null, 0, "This code has expired and is no longer valid.");
            }

            // Check if the user has already redeemed this code
            if (code.HasBeenRedeemedBy(userId))
            {
                return (false, null, 0, "You have already redeemed this code.");
            }

            // Get the consumable
            var consumable = GetConsumable(code.ConsumableId);
            if (consumable == null)
            {
                return (false, null, 0, "Error: The item associated with this code no longer exists.");
            }

            // Add the consumable to the user's inventory
            var userData = GetUserData(userId);
            for (int i = 0; i < code.Quantity; i++)
            {
                userData.AddConsumable(code.ConsumableId, consumable.DefaultUses);
            }

            // Mark the code as redeemed by this user
            code.MarkAsRedeemed(userId);

            // Save the changes
            SaveUserData();
            SaveConfig();

            return (true, consumable, code.Quantity, string.Empty);
        }

        #endregion
    }
}
