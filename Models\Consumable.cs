namespace BotChan.Models
{
    /// <summary>
    /// Represents a consumable item that can be used to multiply bot rewards or provide direct bot rewards.
    /// </summary>
    public class Consumable
    {
        /// <summary>
        /// The unique identifier for this consumable.
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// The name of the consumable.
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// The description of the consumable.
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// The emoji used to represent this consumable in the inventory.
        /// </summary>
        public string Emoji { get; set; } = "🔮";

        /// <summary>
        /// The multiplier applied to bot rewards when this consumable is used.
        /// </summary>
        public double Multiplier { get; set; } = 1.0;

        /// <summary>
        /// The cost of this consumable in bots.
        /// </summary>
        public int Cost { get; set; }

        /// <summary>
        /// The number of uses this consumable has when purchased.
        /// </summary>
        public int DefaultUses { get; set; } = 1;

        /// <summary>
        /// The number of bots directly awarded when this item is used.
        /// If greater than 0, this item gives bots directly instead of applying a multiplier.
        /// </summary>
        public int BotReward { get; set; } = 0;

        /// <summary>
        /// Whether this item is visible in the shop. Set to false for event-only or code-only items.
        /// </summary>
        public bool IsVisibleInShop { get; set; } = true;

        /// <summary>
        /// The category of this consumable item.
        /// 1 = Bot recruitment consumables
        /// 2 = Bot packs (instant bot rewards)
        /// 3 = Bombs (attack items)
        /// </summary>
        public int Category { get; set; } = 1;

        /// <summary>
        /// The success percentage for bomb items (0-100). Only used for Category 3 (Bombs).
        /// </summary>
        public int BombSuccessRate { get; set; } = 50;

        /// <summary>
        /// The fixed percentage of enemy bots destroyed on successful bomb attack (0-100).
        /// Only used for Category 3 (Bombs).
        /// </summary>
        public int BombWinPercentage { get; set; } = 10;

        /// <summary>
        /// The fixed percentage of your own bots lost on failed bomb attack (0-100).
        /// Only used for Category 3 (Bombs).
        /// </summary>
        public int BombLosePercentage { get; set; } = 5;

        /// <summary>
        /// Legacy field - kept for backward compatibility but not used in new bomb system.
        /// </summary>
        public int BombMinDamage { get; set; } = 0;

        /// <summary>
        /// Legacy field - kept for backward compatibility but not used in new bomb system.
        /// </summary>
        public int BombMaxDamage { get; set; } = 0;

        /// <summary>
        /// Legacy field - kept for backward compatibility but not used in new bomb system.
        /// </summary>
        public int BombFailurePenalty { get; set; } = 0;
    }
}
