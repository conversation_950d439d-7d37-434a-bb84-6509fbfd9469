{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\Bot-Chan 6\\BotChan.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\Bot-Chan 6\\BotChan.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Bot-Chan 6\\BotChan.csproj", "projectName": "<PERSON><PERSON><PERSON><PERSON>", "projectPath": "C:\\Users\\<USER>\\Desktop\\Bot-Chan 6\\BotChan.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Bot-Chan 6\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Discord.Net": {"target": "Package", "version": "[3.17.2, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.408/PortableRuntimeIdentifierGraph.json"}}}}}