using Discord;
using Discord.Net;
using Discord.WebSocket;
using BotChan.Commands;
using BotChan.Models;
using System.Net;
using System.Reflection;

namespace BotChan.Services
{
    /// <summary>
    /// The main Discord bot client class that handles connections, events, and command processing.
    /// This is the core of the bot that manages the Discord connection and routes commands to their handlers.
    /// </summary>
    public class DiscordBotClient
    {
        /// <summary>
        /// The Discord.Net client that handles the connection to Discord's API.
        /// Made public to allow commands to access advanced Discord functionality.
        /// </summary>
        public readonly DiscordSocketClient _client;

        /// <summary>
        /// Dictionary of all registered commands, with command names as keys.
        /// Commands are automatically registered from all classes that inherit from Command.
        /// </summary>
        public readonly Dictionary<string, Command> _commands;

        /// <summary>
        /// Dictionary to track reaction handlers for specific messages.
        /// The key is the message ID, and the value is a tuple containing the user ID who can react,
        /// the callback to execute when a reaction is added, and the expiry time.
        /// </summary>
        private readonly Dictionary<ulong, (ulong UserId, Func<Cacheable<IUserMessage, ulong>, Cacheable<IMessageChannel, ulong>, SocketReaction, Task> Callback, DateTime Expiry)> _reactionHandlers = new Dictionary<ulong, (ulong, Func<Cacheable<IUserMessage, ulong>, Cacheable<IMessageChannel, ulong>, SocketReaction, Task>, DateTime)>();

        /// <summary>
        /// Configuration settings for the bot, including token and command prefix.
        /// </summary>
        public BotConfig Config { get; }

        /// <summary>
        /// Service for managing command permissions.
        /// </summary>
        public PermissionsService Permissions { get; }

        /// <summary>
        /// Service for managing the bot system (bots and consumables).
        /// </summary>
        public BotSystemService BotSystem { get; }



        /// <summary>
        /// Initializes a new instance of the Discord bot client.
        /// </summary>
        /// <param name="config">Configuration settings for the bot</param>
        public DiscordBotClient(BotConfig config)
        {
            Config = config;

            // ==================== DISCORD CLIENT CONFIGURATION ====================
            // The DiscordSocketConfig allows you to configure various aspects of the Discord client.
            // Here are the most important configuration options:

            // GATEWAY INTENTS
            // Intents determine what events and data the bot will receive from Discord.
            // You should only request intents that your bot actually needs.
            // Some intents are "privileged" and require enabling in the Discord Developer Portal.

            // AVAILABLE INTENTS:
            // - Guilds: Information about servers (always recommended)
            // - GuildMembers: Access to server member lists (PRIVILEGED)
            // - GuildBans: Information about server bans
            // - GuildEmojis: Access to custom emojis and stickers
            // - GuildIntegrations: Information about server integrations
            // - GuildWebhooks: Information about webhooks
            // - GuildInvites: Information about server invites
            // - GuildVoiceStates: Information about voice states
            // - GuildPresences: Information about user presence (PRIVILEGED)
            // - GuildMessages: Access to messages in servers
            // - GuildMessageReactions: Access to message reactions
            // - GuildMessageTyping: Information about typing indicators
            // - DirectMessages: Access to direct messages
            // - DirectMessageReactions: Access to direct message reactions
            // - DirectMessageTyping: Information about typing indicators in DMs
            // - MessageContent: Ability to read message content (PRIVILEGED)
            // - GuildScheduledEvents: Information about scheduled events

            // OTHER CONFIGURATION OPTIONS:
            // - AlwaysDownloadUsers: Whether to download all users when connecting (can be resource-intensive)
            // - MessageCacheSize: Number of messages to keep in cache per channel
            // - LogLevel: Level of logging detail
            // - DefaultRetryMode: How to handle failed requests
            // - HandlerTimeout: Timeout for event handlers

            var socketConfig = new DiscordSocketConfig
            {
                // Configure the intents needed for this bot
                GatewayIntents = GatewayIntents.Guilds |            // Required for basic server information
                                GatewayIntents.GuildMessages |      // Required to receive messages in servers
                                GatewayIntents.MessageContent |     // Required to read message content (PRIVILEGED)
                                GatewayIntents.GuildMembers,        // Required for user information (PRIVILEGED)

                // Additional configuration options (commented out by default)
                // MessageCacheSize = 100,                          // Cache the last 100 messages per channel
                // AlwaysDownloadUsers = true,                      // Download all users when connecting
                // LogLevel = LogSeverity.Debug,                    // Set logging level to Debug
                // DefaultRetryMode = RetryMode.AlwaysRetry,        // Always retry failed requests
                // HandlerTimeout = 5000                            // 5 second timeout for event handlers
            };
            // ===================================================================

            _client = new DiscordSocketClient(socketConfig);
            _commands = new Dictionary<string, Command>();

            // Initialize the services
            Permissions = new PermissionsService(Config.OwnerId);
            BotSystem = new BotSystemService();


            // ==================== DISCORD CLIENT EVENTS ====================
            // Discord.Net provides many events you can subscribe to for different bot functionality.
            // Here are some of the most commonly used events:

            // CORE CONNECTION EVENTS
            _client.Ready += OnClientReady;                  // Called when the bot connects to Discord and is ready
            _client.Connected += () => { Console.WriteLine("Bot connected to Discord!"); return Task.CompletedTask; }; // Called when the WebSocket connects
            // _client.Disconnected += (ex) => { Console.WriteLine($"Bot disconnected: {ex.Message}"); return Task.CompletedTask; }; // Called when the WebSocket disconnects

            // MESSAGE EVENTS
            _client.MessageReceived += OnMessageReceived;     // Called when any message is received
            _client.ReactionAdded += OnReactionAdded;        // Called when a reaction is added to a message
            // _client.MessageUpdated += (oldMsg, newMsg, channel) => { /* Handle edited messages */ return Task.CompletedTask; };
            // _client.MessageDeleted += (msg, channel) => { /* Handle deleted messages */ return Task.CompletedTask; };

            // SLASH COMMAND EVENTS
            _client.SlashCommandExecuted += OnSlashCommandExecuted; // Called when a slash command is executed

            // USER EVENTS
            // _client.UserJoined += (user) => { /* Handle new users joining a server */ return Task.CompletedTask; };
            // _client.UserLeft += (guild, user) => { /* Handle users leaving a server */ return Task.CompletedTask; };
            // _client.UserBanned += (user, guild) => { /* Handle users being banned */ return Task.CompletedTask; };
            // _client.UserUpdated += (oldUser, newUser) => { /* Handle user updates (username, avatar, etc.) */ return Task.CompletedTask; };

            // VOICE EVENTS
            // _client.UserVoiceStateUpdated += (user, oldState, newState) => { /* Handle users joining/leaving voice channels */ return Task.CompletedTask; };
            // _client.VoiceServerUpdated += (guild, endpoint) => { /* Handle voice server changes */ return Task.CompletedTask; };

            // CHANNEL EVENTS
            // _client.ChannelCreated += (channel) => { /* Handle new channels being created */ return Task.CompletedTask; };
            // _client.ChannelDestroyed += (channel) => { /* Handle channels being deleted */ return Task.CompletedTask; };
            // _client.ChannelUpdated += (oldChannel, newChannel) => { /* Handle channel updates */ return Task.CompletedTask; };

            // GUILD (SERVER) EVENTS
            // _client.JoinedGuild += (guild) => { /* Handle the bot joining a new server */ return Task.CompletedTask; };
            // _client.LeftGuild += (guild) => { /* Handle the bot leaving a server */ return Task.CompletedTask; };
            // _client.GuildUpdated += (oldGuild, newGuild) => { /* Handle server updates */ return Task.CompletedTask; };
            // _client.GuildAvailable += (guild) => { /* Handle a server becoming available */ return Task.CompletedTask; };
            // _client.GuildUnavailable += (guild) => { /* Handle a server becoming unavailable */ return Task.CompletedTask; };

            // ROLE EVENTS
            // _client.RoleCreated += (role) => { /* Handle new roles being created */ return Task.CompletedTask; };
            // _client.RoleDeleted += (role) => { /* Handle roles being deleted */ return Task.CompletedTask; };
            // _client.RoleUpdated += (oldRole, newRole) => { /* Handle role updates */ return Task.CompletedTask; };

            // INTEGRATION EVENTS
            // _client.IntegrationCreated += (integration) => { /* Handle new integrations */ return Task.CompletedTask; };
            // _client.IntegrationUpdated += (integration) => { /* Handle integration updates */ return Task.CompletedTask; };
            // _client.IntegrationDeleted += (integration) => { /* Handle integration deletions */ return Task.CompletedTask; };
            // ===============================================================
        }

        /// <summary>
        /// Event handler that is called when the bot successfully connects to Discord.
        /// </summary>
        private async Task OnClientReady()
        {
            // Log the bot's username to confirm successful login
            Console.WriteLine($"Discord bot logged in as {_client.CurrentUser.Username}");

            // Update all user information from Discord
            Console.WriteLine("Updating user information from Discord...");
            int updatedCount = BotSystem.UpdateAllUserInfo(_client);
            Console.WriteLine($"Updated information for {updatedCount} users.");

            // ==================== BOT ACTIVITY OPTIONS ====================
            // Discord bots can display different types of activities in their status:
            // 1. Playing a game (Game)
            // 2. Streaming (StreamingGame)
            // 3. Listening to something (CustomActivity with "Listening to")
            // 4. Watching something (CustomActivity with "Watching")
            // 5. Competing in something (CustomActivity with "Competing in")
            // 6. Custom status (CustomActivity)

            // OPTION 1: PLAYING STATUS (most common)
            // Shows "Playing [game name]" in the bot's status
            await _client.SetActivityAsync(new Game("Bot Mania!"));

            // OPTION 2: STREAMING STATUS
            // Shows "Streaming [stream name]" with a Twitch/YouTube link
            // await _client.SetActivityAsync(new StreamingGame("Bot-Chan Development", "https://twitch.tv/example"));

            // OPTION 3: LISTENING STATUS
            // Shows "Listening to [name]" in the bot's status
            // await _client.SetActivityAsync(new CustomActivity(ActivityType.Listening, "user commands"));

            // OPTION 4: WATCHING STATUS
            // Shows "Watching [name]" in the bot's status
            // await _client.SetActivityAsync(new CustomActivity(ActivityType.Watching, "for commands"));

            // OPTION 5: COMPETING STATUS
            // Shows "Competing in [name]" in the bot's status
            // await _client.SetActivityAsync(new CustomActivity(ActivityType.Competing, "Discord Bot Olympics"));

            // OPTION 6: CUSTOM STATUS WITH EMOJI
            // Shows a custom status with an emoji
            // await _client.SetActivityAsync(new CustomActivity("🤖 Ready to help!"));

            // OPTION 7: NO STATUS
            // Removes any activity status
            // await _client.SetActivityAsync(null);

            // ADDITIONAL NOTES:
            // - You can only set one activity at a time
            // - Activities can be changed at any time, not just at startup
            // - Consider changing the activity based on bot state or time of day
            // - You can use a timer to rotate through different activities
            // ===============================================================

            // Check bot permissions and application info
            await CheckBotPermissionsAsync();

            // Register slash commands
            Console.WriteLine("Registering slash commands...");
            await RegisterSlashCommandsAsync();

            // Note: DM-compatible commands are now included in the main registration above
            // This prevents conflicts between separate DM registration and main command registration

            Console.WriteLine("Bot is ready to respond to commands!");
        }

        /// <summary>
        /// Event handler that is called when a message is received in any channel the bot has access to.
        /// This method processes commands by checking for the command prefix and routing to the appropriate handler.
        /// </summary>
        /// <param name="message">The message that was received</param>
        private async Task OnMessageReceived(SocketMessage message)
        {
            // Ignore messages from bots (including this bot) to prevent potential loops
            if (message.Author.IsBot) return;

            // Only process messages that start with the configured command prefix
            // The prefix is defined in appsettings.json (e.g., "%")
            if (!message.Content.StartsWith(Config.Prefix)) return;

            // Split the message into command name and arguments
            // For example, "%echo hello world" becomes:
            // args[0] = "echo" (command name)
            // args[1] = "hello" (first argument)
            // args[2] = "world" (second argument)
            var args = message.Content.Substring(Config.Prefix.Length).Split(' ');
            var commandName = args[0].ToLower(); // Convert to lowercase for case-insensitive matching

            // Look up the command in the registered commands dictionary
            if (_commands.TryGetValue(commandName, out var command))
            {
                // Check if the user has hit the rate limit
                if (Permissions.IsRateLimited(message.Author.Id, commandName))
                {
                    // Send the rate limit message
                    string rateLimitMessage = $"**<:BM_BotChanDespair:1410316642180796446> | {message.Author.Username}**, Please slow down! You're going a little **too fast** for me i can't keep up :c";

                    // Send the message and store the returned message object
                    var sentMessage = await message.Channel.SendMessageAsync(rateLimitMessage);

                    // Schedule the message to be deleted after 5 seconds
                    _ = Task.Run(async () => {
                        await Task.Delay(5000); // Wait 5 seconds
                        await sentMessage.DeleteAsync();
                    });

                    return;
                }

                // Check if the user has permission to use this command, including cooldown
                if (!Permissions.HasPermission(message.Author.Id, commandName, out string cooldownMessage))
                {
                    // If there's a cooldown message, show it to the user
                    if (!string.IsNullOrEmpty(cooldownMessage))
                    {
                        // Extract just the timestamp part from the cooldown message
                        int startIndex = cooldownMessage.IndexOf("<t:");
                        int endIndex = cooldownMessage.IndexOf(">", startIndex) + 1;
                        string timestamp = cooldownMessage.Substring(startIndex, endIndex - startIndex);

                        // Format the cooldown message with the bot's style
                        string formattedCooldownMessage = $"<:BM_BotChanReaction:1143303450369736754> | **{message.Author.Username}**, this command is on cooldown! Try again {timestamp}.";

                        // Send the message and store the returned message object
                        var sentMessage = await message.Channel.SendMessageAsync(formattedCooldownMessage);

                        // Schedule the message to be deleted after 5 seconds
                        _ = Task.Run(async () => {
                            await Task.Delay(5000); // Wait 5 seconds
                            await sentMessage.DeleteAsync();
                        });
                    }
                    // Otherwise, user doesn't have permission to use this command
                    // We don't send a message to avoid revealing command existence to unauthorized users
                    return;
                }

                try
                {
                    // Check if the bot is in lockdown mode
                    if (BotSystem.IsInLockdown() && !command.IsExemptFromLockdown)
                    {
                        // Allow staff to use commands during lockdown
                        if (!Permissions.IsStaffUser(message.Author.Id))
                        {
                            // Get lockdown info
                            var lockdownInfo = BotSystem.GetLockdownInfo();

                            // Create an embed for the lockdown message
                            var embed = new EmbedBuilder()
                                .WithTitle("⚠️ Bot is in Emergency Lockdown")
                                .WithDescription("The bot is currently in emergency lockdown mode. Only staff members can use commands at this time.")
                                .WithColor(Color.Red)
                                .WithFooter($"Lockdown activated {(DateTime.UtcNow - lockdownInfo.ActivatedAt).TotalHours:0.0} hours ago");

                            // Add reason if provided
                            if (!string.IsNullOrEmpty(lockdownInfo.Reason))
                            {
                                embed.AddField("Reason", lockdownInfo.Reason);
                            }

                            await message.Channel.SendMessageAsync(embed: embed.Build());
                            return;
                        }
                    }

                    // Check if this is the user's first command of the day
                    var userData = BotSystem.GetUserData(message.Author.Id, message.Author.Username);
                    bool shouldShowSummary = userData.ShouldReceiveDailySummary();

                    // Execute the command, passing the message, arguments, and a reference to this client
                    await command.ExecuteAsync(message, args, this);

                    // Update the command usage time for cooldown tracking
                    Permissions.UpdateCommandUsage(message.Author.Id, commandName);

                    // Show daily summary if needed (but not for daily command since it handles its own summary)
                    if (shouldShowSummary && !commandName.Equals("daily", StringComparison.OrdinalIgnoreCase))
                    {
                        string summaryMessage = userData.GetDailySummary();
                        await message.Channel.SendMessageAsync(summaryMessage);
                    }
                }
                catch (Exception ex)
                {
                    // Log any errors that occur during command execution
                    Console.Error.WriteLine(ex);

                    // Inform the user that an error occurred
                    await message.Channel.SendMessageAsync("Error executing command!");
                }
            }
            // If the command is not found, we simply ignore it (no error message)
            // This allows users to use other bots with different prefixes in the same server
        }

        /// <summary>
        /// Event handler that is called when a slash command is executed.
        /// This method processes slash commands and routes them to the appropriate command handler.
        /// </summary>
        /// <param name="command">The slash command that was executed</param>
        private async Task OnSlashCommandExecuted(SocketSlashCommand command)
        {
            try
            {
                // Get the command name
                string commandName = command.Data.Name.ToLower();

                // Check if the command exists in our registered commands
                if (_commands.TryGetValue(commandName, out var botCommand))
                {
                    // Check if the user is in lockdown mode
                    if (BotSystem.IsInLockdown() && !Permissions.IsStaffUser(command.User.Id))
                    {
                        await command.RespondAsync("The bot is currently in lockdown mode. Only staff members can use commands.", ephemeral: true);
                        return;
                    }

                    // Check command permissions and cooldowns
                    if (!Permissions.HasPermission(command.User.Id, commandName, out string cooldownMessage))
                    {
                        if (!string.IsNullOrEmpty(cooldownMessage))
                        {
                            await command.RespondAsync($"Command is on cooldown. {cooldownMessage}", ephemeral: true);
                        }
                        else
                        {
                            await command.RespondAsync("You don't have permission to use this command.", ephemeral: true);
                        }
                        return;
                    }

                    // Check rate limiting
                    if (Permissions.IsRateLimited(command.User.Id, commandName))
                    {
                        await command.RespondAsync("You're using commands too quickly. Please slow down!", ephemeral: true);
                        return;
                    }

                    // Check if this is a slash-exclusive command
                    switch (commandName)
                    {
                        case "bomb":
                            await Commands.SlashCommands.BombSlashCommand.ExecuteAsync(command, this);
                            break;

                        case "bots":
                            await Commands.SlashCommands.BotsSlashCommand.ExecuteAsync(command, this);
                            break;

                        case "daily":
                            await Commands.SlashCommands.DailySlashCommand.ExecuteAsync(command, this);
                            break;

                        case "declarewar":
                            await Commands.SlashCommands.DeclareWarSlashCommand.ExecuteAsync(command, this);
                            break;

                        case "dismantle":
                            await Commands.SlashCommands.DismantleSlashCommand.ExecuteAsync(command, this);
                            break;

                        case "help":
                            await Commands.SlashCommands.HelpSlashCommand.ExecuteAsync(command, this);
                            break;

                        case "info":
                            await Commands.SlashCommands.InfoSlashCommand.ExecuteAsync(command, this);
                            break;

                        case "inventory":
                            await Commands.SlashCommands.InventorySlashCommand.ExecuteAsync(command, this);
                            break;

                        case "leaderboards":
                            await Commands.SlashCommands.LeaderboardsSlashCommand.ExecuteAsync(command, this);
                            break;

                        case "ping":
                            await Commands.SlashCommands.PingSlashCommand.ExecuteAsync(command, this);
                            break;

                        case "recruit":
                            await Commands.SlashCommands.RecruitSlashCommand.ExecuteAsync(command, this);
                            break;

                        case "sell":
                            await Commands.SlashCommands.SellSlashCommand.ExecuteAsync(command, this);
                            break;

                        case "shop":
                            await Commands.SlashCommands.ShopSlashCommand.ExecuteAsync(command, this);
                            break;

                        case "use":
                            await Commands.SlashCommands.UseSlashCommand.ExecuteAsync(command, this);
                            break;

                        case "usecode":
                            await Commands.SlashCommands.UseCodeSlashCommand.ExecuteAsync(command, this);
                            break;

                        case "supportserver":
                            await Commands.SlashCommands.SupportServerSlashCommand.ExecuteAsync(command, this);
                            break;

                        default:
                            // Check if this is an admin command that should remain % only
                            string[] adminCommands = { "changeperms", "cleardata", "echo", "editcodes", "editconsumables", "edititems", "edituser", "lock", "unlock" };

                            if (adminCommands.Contains(commandName.ToLower()))
                            {
                                // Admin commands are intentionally not available as slash commands
                                var adminEmbed = new EmbedBuilder()
                                    .WithTitle("🔒 Admin Command")
                                    .WithDescription($"The `/{commandName}` command is an admin-only command.")
                                    .AddField("Access Method", $"Please use: `{Config.Prefix}{commandName}`")
                                    .AddField("Reason", "Admin commands are restricted to prefix commands for security and control.")
                                    .WithColor(Color.Red)
                                    .WithFooter("Admin commands are not available as slash commands")
                                    .WithTimestamp(DateTimeOffset.Now);

                                await command.RespondAsync(embed: adminEmbed.Build(), ephemeral: true);
                            }
                            else
                            {
                                // For other commands, provide helpful information about slash command availability
                                var embed = new EmbedBuilder()
                                    .WithTitle("🔧 Slash Command Detected")
                                    .WithDescription($"You used the `/{commandName}` slash command!")
                                    .AddField("Current Status", "This slash command is registered but still in development.")
                                    .AddField("Alternative", $"For now, please use: `{Config.Prefix}{commandName}`")
                                    .AddField("💡 Tip", "Try `/bomb`, `/bots`, `/daily`, `/declarewar`, `/info`, `/inventory`, `/leaderboards`, `/ping`, `/recruit`, `/sell`, `/shop`, `/supportserver`, `/use`, or `/usecode` - these work fully!")
                                    .WithColor(Color.Orange)
                                    .WithFooter("More slash command functionality coming soon!")
                                    .WithTimestamp(DateTimeOffset.Now);

                                await command.RespondAsync(embed: embed.Build(), ephemeral: true);
                            }
                            break;
                    }
                }
                else
                {
                    await command.RespondAsync("Unknown command.", ephemeral: true);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing slash command '{command.Data.Name}': {ex.Message}");

                if (!command.HasResponded)
                {
                    await command.RespondAsync("An error occurred while executing the command.", ephemeral: true);
                }
            }
        }

        /// <summary>
        /// Automatically discovers and registers all command classes in the assembly.
        /// This method uses reflection to find all classes that inherit from the Command base class.
        /// </summary>
        public void RegisterCommands()
        {
            // Find all types in the current assembly that:
            // 1. Are classes (not interfaces or other types)
            // 2. Are not abstract
            // 3. Inherit from the Command base class
            foreach (var type in Assembly.GetExecutingAssembly().GetTypes()
                .Where(t => t.IsClass && !t.IsAbstract && t.IsSubclassOf(typeof(Command))))
            {
                // Create an instance of the command class
                if (Activator.CreateInstance(type) is Command command)
                {
                    // Register the command in the dictionary with its name (converted to lowercase)
                    // This allows case-insensitive command matching
                    _commands[command.Name.ToLower()] = command;

                    // If this is an AliasCommand, register all its aliases too
                    if (command is AliasCommand aliasCommand && aliasCommand.Aliases.Length > 0)
                    {
                        foreach (var alias in aliasCommand.Aliases)
                        {
                            _commands[alias.ToLower()] = command;
                            Console.WriteLine($"  Alias {alias} registered for {command.Name}");
                        }
                    }

                    // Log that the command was successfully loaded
                    Console.WriteLine($"Command {command.Name} loaded");
                }
            }
        }

        /// <summary>
        /// Event handler that is called when a reaction is added to a message.
        /// This method routes the reaction to the appropriate handler if one is registered.
        /// </summary>
        /// <param name="message">The message that was reacted to</param>
        /// <param name="channel">The channel where the reaction occurred</param>
        /// <param name="reaction">The reaction that was added</param>
        private async Task OnReactionAdded(Cacheable<IUserMessage, ulong> message, Cacheable<IMessageChannel, ulong> channel, SocketReaction reaction)
        {
            // Clean up expired reaction handlers
            CleanupExpiredReactionHandlers();

            // Check if we have a handler for this message
            if (_reactionHandlers.TryGetValue(reaction.MessageId, out var handlerInfo))
            {
                // Check if the user who reacted is the one who can use this handler
                if (handlerInfo.UserId == reaction.UserId)
                {
                    // Execute the callback
                    await handlerInfo.Callback(message, channel, reaction);
                }
            }
        }

        /// <summary>
        /// Registers a reaction handler for a specific message.
        /// </summary>
        /// <param name="messageId">The ID of the message to watch for reactions</param>
        /// <param name="userId">The ID of the user who is allowed to react</param>
        /// <param name="callback">The callback to execute when a reaction is added</param>
        /// <param name="expiryMinutes">How many minutes until this handler expires (default: 5)</param>
        public void RegisterReactionHandler(ulong messageId, ulong userId, Func<Cacheable<IUserMessage, ulong>, Cacheable<IMessageChannel, ulong>, SocketReaction, Task> callback, int expiryMinutes = 5)
        {
            _reactionHandlers[messageId] = (userId, callback, DateTime.UtcNow.AddMinutes(expiryMinutes));
        }

        /// <summary>
        /// Removes a reaction handler for a specific message.
        /// </summary>
        /// <param name="messageId">The ID of the message</param>
        public void RemoveReactionHandler(ulong messageId)
        {
            if (_reactionHandlers.ContainsKey(messageId))
            {
                _reactionHandlers.Remove(messageId);
            }
        }

        /// <summary>
        /// Cleans up expired reaction handlers to prevent memory leaks.
        /// </summary>
        private void CleanupExpiredReactionHandlers()
        {
            var now = DateTime.UtcNow;
            var expiredHandlers = _reactionHandlers.Where(kv => kv.Value.Expiry < now).Select(kv => kv.Key).ToList();

            foreach (var messageId in expiredHandlers)
            {
                _reactionHandlers.Remove(messageId);
            }
        }

        /// <summary>
        /// Checks bot permissions and application information for slash command support.
        /// </summary>
        private async Task CheckBotPermissionsAsync()
        {
            try
            {
                Console.WriteLine("🔍 Checking bot permissions and application info...");

                // Get application info
                var application = await _client.GetApplicationInfoAsync();
                Console.WriteLine($"📱 Application: {application.Name} (ID: {application.Id})");
                Console.WriteLine($"👤 Owner: {application.Owner.Username}#{application.Owner.Discriminator}");

                // Check if bot is in any guilds
                Console.WriteLine($"🏰 Bot is in {_client.Guilds.Count} guild(s):");
                foreach (var guild in _client.Guilds)
                {
                    var botUser = guild.GetUser(_client.CurrentUser.Id);
                    if (botUser != null)
                    {
                        var permissions = botUser.GuildPermissions;
                        Console.WriteLine($"  • {guild.Name} (ID: {guild.Id}) - Members: {guild.MemberCount}");
                        Console.WriteLine($"    Permissions: Administrator={permissions.Administrator}, ManageGuild={permissions.ManageGuild}");

                        // Check if bot can use slash commands in this guild
                        // Slash commands require the bot to have the applications.commands scope when invited
                        if (permissions.Administrator)
                        {
                            Console.WriteLine($"    ✅ Has Administrator permission (can use slash commands)");
                        }
                        else
                        {
                            Console.WriteLine($"    ℹ️ No Administrator permission (slash commands depend on invite scope)");
                        }
                    }
                }

                // Check current user info
                Console.WriteLine($"🤖 Bot User: {_client.CurrentUser.Username}#{_client.CurrentUser.Discriminator} (ID: {_client.CurrentUser.Id})");
                Console.WriteLine($"🔗 Bot Invite URL: https://discord.com/api/oauth2/authorize?client_id={_client.CurrentUser.Id}&permissions=2147483647&scope=bot%20applications.commands");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error checking bot permissions: {ex.Message}");
            }
        }

        // Configuration for slash command registration
        private const int BATCH_SIZE = 5; // Commands per batch
        private const int BATCH_DELAY_MS = 2000; // Delay between batches
        private const int MAX_RETRIES = 3; // Maximum retry attempts
        private const int BASE_DELAY_MS = 2000; // Base delay for exponential backoff (increased)
        private const int REGISTRATION_TIMEOUT_MS = 60000; // 60 second timeout (increased)
        private const int GLOBAL_REGISTRATION_DELAY_MS = 5000; // 5 second delay before global registration

        // Track registration status to avoid duplicate registrations
        private bool _globalCommandsRegistered = false;
        private readonly HashSet<ulong> _guildCommandsRegistered = new HashSet<ulong>();
        private readonly object _registrationLock = new object();

        // Configuration flags
        private bool _skipGlobalRegistration = false; // Set to true to skip global registration
        private bool _forceGlobalRegistration = false; // Set to true to force re-registration
        private bool _removeGlobalCommandsOnShutdown = false; // Set to true to remove global commands on shutdown
        private bool _disableDailyLimit = false; // Set to true to disable daily registration limit

        // Daily global registration tracking
        private DateTime _lastGlobalRegistration = DateTime.MinValue;
        private const string LAST_REGISTRATION_FILE = "last_global_registration.txt";

        /// <summary>
        /// Registers slash commands with Discord using batching, retry logic, and rate limiting.
        /// This method creates slash commands for all registered bot commands.
        /// </summary>
        public async Task RegisterSlashCommandsAsync()
        {
            try
            {
                Console.WriteLine("🔧 Building slash commands...");

                // First, remove any unwanted admin commands that might have been registered previously
                await RemoveUnwantedAdminCommandsAsync();

                var allSlashCommands = BuildSlashCommands();

                // Validate all commands before registration
                if (!ValidateSlashCommands(allSlashCommands))
                {
                    Console.WriteLine("❌ Command validation failed, aborting registration");
                    return;
                }

                Console.WriteLine($"📊 Built {allSlashCommands.Count} slash commands total");

                // Determine registration strategy to prevent duplicates
                bool shouldRegisterGlobal = await ShouldRegisterGlobalCommands(allSlashCommands);
                bool hasGuilds = _client.Guilds.Count > 0;

                if (shouldRegisterGlobal)
                {
                    // Register global commands for broader reach (DMs, other servers)
                    Console.WriteLine("🌍 Registering global commands for broader reach...");
                    await RegisterGlobalCommandsWithRetry(allSlashCommands);

                    if (hasGuilds)
                    {
                        // Clear guild commands to prevent duplicates when global commands exist
                        Console.WriteLine("🧹 Clearing guild commands to prevent duplicates with global commands...");
                        await ClearGuildCommands();
                    }
                }
                else
                {
                    // Register guild commands for immediate availability when global is not needed
                    Console.WriteLine("🏰 Registering guild commands for immediate availability...");
                    await RegisterGuildCommandsWithBatching(allSlashCommands);
                }
                // Note: ShouldRegisterGlobalCommands already prints specific skip reasons, no need for additional message

                // Verify registration
                await ListRegisteredCommandsAsync();

                lock (_registrationLock)
                {
                    _globalCommandsRegistered = true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Critical error in slash command registration: {ex.Message}");
                Console.WriteLine($"📋 Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Builds all slash commands and returns them as a list.
        /// </summary>
        private List<SlashCommandBuilder> BuildSlashCommands()
        {
            var slashCommands = new List<SlashCommandBuilder>();

            // Add specific slash commands with proper arguments
            Console.WriteLine("🔨 Building custom slash commands...");

            // /bomb command
            var bombCommand = new SlashCommandBuilder()
                .WithName("bomb")
                .WithDescription("Attack another player's territory using bomb items")
                .AddOption("target", ApplicationCommandOptionType.String, "Target user ID", true)
                .AddOption("item_id", ApplicationCommandOptionType.Integer, "Inventory item ID", true)
                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
            slashCommands.Add(bombCommand);

            // /bots command
            var botsCommand = new SlashCommandBuilder()
                .WithName("bots")
                .WithDescription("Check your bot count")
                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
            slashCommands.Add(botsCommand);

            // /daily command
            var dailyCommand = new SlashCommandBuilder()
                .WithName("daily")
                .WithDescription("Claim your daily bot points")
                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
            slashCommands.Add(dailyCommand);

            // /declarewar command
            var declarewarCommand = new SlashCommandBuilder()
                .WithName("declarewar")
                .WithDescription("Declare war (Discord server required)")
                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
            slashCommands.Add(declarewarCommand);

            // /dismantle command
            var dismantleCommand = new SlashCommandBuilder()
                .WithName("dismantle")
                .WithDescription("Remove your currently active recruitment multiplier")
                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
            slashCommands.Add(dismantleCommand);

            // /help command
            var helpCommand = new SlashCommandBuilder()
                .WithName("help")
                .WithDescription("Displays information about available commands")
                .AddOption("command", ApplicationCommandOptionType.String, "Specific command to get help for", false)
                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
            slashCommands.Add(helpCommand);

            // /info command
            var infoCommand = new SlashCommandBuilder()
                .WithName("info")
                .WithDescription("Shows information about shop items")
                .AddOption("item_id", ApplicationCommandOptionType.Integer, "Item ID to get details about", false)
                .AddOption("page", ApplicationCommandOptionType.Integer, "Page number for item list", false)
                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
            slashCommands.Add(infoCommand);

            // /inventory command
            var inventoryCommand = new SlashCommandBuilder()
                .WithName("inventory")
                .WithDescription("Check your inventory of consumables")
                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
            slashCommands.Add(inventoryCommand);

            // /leaderboards command
            var leaderboardsCommand = new SlashCommandBuilder()
                .WithName("leaderboards")
                .WithDescription("View the global bot leaderboards")
                .AddOption("page", ApplicationCommandOptionType.Integer, "Page number", false)
                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
            slashCommands.Add(leaderboardsCommand);

            // /ping command
            var pingCommand = new SlashCommandBuilder()
                .WithName("ping")
                .WithDescription("Check bot latency and status")
                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
            slashCommands.Add(pingCommand);

            // /recruit command
            var recruitCommand = new SlashCommandBuilder()
                .WithName("recruit")
                .WithDescription("Recruit bots to add to your collection")
                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
            slashCommands.Add(recruitCommand);

            // /sell command
            var sellCommand = new SlashCommandBuilder()
                .WithName("sell")
                .WithDescription("Sell items from your inventory for 50% of their original price")
                .AddOption("item_id", ApplicationCommandOptionType.Integer, "Inventory item ID to sell", true)
                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
            slashCommands.Add(sellCommand);

            // /shop command
            var shopCommand = new SlashCommandBuilder()
                .WithName("shop")
                .WithDescription("Browse and purchase consumables with your bots")
                .AddOption("category", ApplicationCommandOptionType.String, "Shop category", false, choices: new ApplicationCommandOptionChoiceProperties[]
                {
                    new ApplicationCommandOptionChoiceProperties { Name = "Consumables", Value = "consumables" },
                    new ApplicationCommandOptionChoiceProperties { Name = "Vanity", Value = "packs" },
                    new ApplicationCommandOptionChoiceProperties { Name = "Bombs", Value = "bombs" }
                })
                .AddOption("page", ApplicationCommandOptionType.Integer, "Page number", false)
                .AddOption("action", ApplicationCommandOptionType.String, "Action to perform", false, choices: new ApplicationCommandOptionChoiceProperties[]
                {
                    new ApplicationCommandOptionChoiceProperties { Name = "Buy", Value = "buy" }
                })
                .AddOption("item_id", ApplicationCommandOptionType.Integer, "Item ID to buy", false)
                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
            slashCommands.Add(shopCommand);

            // /use command
            var useCommand = new SlashCommandBuilder()
                .WithName("use")
                .WithDescription("Use a consumable from your inventory")
                .AddOption("item_id", ApplicationCommandOptionType.Integer, "Inventory item ID to use", true)
                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
            slashCommands.Add(useCommand);

            // /usecode command
            var usecodeCommand = new SlashCommandBuilder()
                .WithName("usecode")
                .WithDescription("Redeem a code for items")
                .AddOption("code", ApplicationCommandOptionType.String, "Code to redeem", true)
                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
            slashCommands.Add(usecodeCommand);

            // /supportserver command
            var supportserverCommand = new SlashCommandBuilder()
                .WithName("supportserver")
                .WithDescription("Get the link to our support server")
                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
            slashCommands.Add(supportserverCommand);

            // Add generic commands from the command registry
            Console.WriteLine("🔨 Building generic slash commands...");
            foreach (var command in _commands.Values)
            {
                // Skip alias commands to avoid duplicates
                if (command is AliasCommand) continue;

                string commandNameLower = command.Name.ToLower();

                // Skip commands that have custom slash command implementations
                if (commandNameLower == "bomb" || commandNameLower == "daily" || commandNameLower == "declarewar" ||
                    commandNameLower == "dismantle" || commandNameLower == "help" || commandNameLower == "info" ||
                    commandNameLower == "inventory" || commandNameLower == "leaderboards" || commandNameLower == "recruit" ||
                    commandNameLower == "sell" || commandNameLower == "shop" || commandNameLower == "supportserver" ||
                    commandNameLower == "use" || commandNameLower == "usecode" || commandNameLower == "bots" ||
                    commandNameLower == "ping")
                {
                    continue; // Skip these as they have custom implementations
                }

                // Skip admin/developer commands that should remain % commands only
                if (commandNameLower == "changeperms" || commandNameLower == "cleardata" || commandNameLower == "echo" ||
                    commandNameLower == "editcodes" || commandNameLower == "editconsumables" || commandNameLower == "edititems" ||
                    commandNameLower == "edituser" || commandNameLower == "lock" || commandNameLower == "unlock")
                {
                    Console.WriteLine($"  ⏭️ Skipping admin command: {command.Name} (% command only)");
                    continue; // Skip these admin commands from slash command registration
                }

                // Ensure description is not empty and within Discord limits
                string description = command.Description;
                if (string.IsNullOrWhiteSpace(description))
                {
                    description = $"Execute the {command.Name} command";
                }

                // Discord requires descriptions to be 1-100 characters
                if (description.Length > 100)
                {
                    description = description.Substring(0, 97) + "...";
                }

                Console.WriteLine($"  📝 Building command: {command.Name} - {description}");

                var slashCommandBuilder = new SlashCommandBuilder()
                    .WithName(command.Name.ToLower())
                    .WithDescription(description);

                // Commands that don't need arguments parameter
                string[] commandsWithoutArguments = { "help", "declarewar", "supportserver" };

                if (!commandsWithoutArguments.Contains(commandNameLower))
                {
                    // Add generic arguments option for other commands
                    slashCommandBuilder.AddOption("arguments", ApplicationCommandOptionType.String, "Command arguments (optional)", false);
                }

                slashCommands.Add(slashCommandBuilder);
            }

            return slashCommands;
        }

        /// <summary>
        /// Validates all slash commands for Discord API compliance.
        /// </summary>
        private bool ValidateSlashCommands(List<SlashCommandBuilder> commands)
        {
            Console.WriteLine("🔍 Validating slash commands...");
            bool allValid = true;

            foreach (var command in commands)
            {
                try
                {
                    // Check if command is null
                    if (command == null)
                    {
                        Console.WriteLine($"❌ Null command found in validation");
                        allValid = false;
                        continue;
                    }

                    // Validate command name
                    if (string.IsNullOrWhiteSpace(command.Name) || command.Name.Length > 32)
                    {
                        Console.WriteLine($"❌ Invalid command name: '{command.Name ?? "NULL"}' (must be 1-32 characters)");
                        allValid = false;
                        continue;
                    }

                    // Validate command description
                    if (string.IsNullOrWhiteSpace(command.Description) || command.Description.Length > 100)
                    {
                        Console.WriteLine($"❌ Invalid description for '{command.Name}': '{command.Description ?? "NULL"}' ({command.Description?.Length ?? 0} characters, must be 1-100)");
                        allValid = false;
                        continue;
                    }

                    // Validate options count (Discord limit is 25)
                    int optionCount = command.Options?.Count ?? 0;
                    if (optionCount > 25)
                    {
                        Console.WriteLine($"❌ Too many options for '{command.Name}': {optionCount} (max 25)");
                        allValid = false;
                        continue;
                    }

                    // Try to build the command to catch any other issues
                    try
                    {
                        var builtCommand = command.Build();
                        Console.WriteLine($"✅ Command '{command.Name}' validated successfully ({optionCount} options)");
                    }
                    catch (Exception buildEx)
                    {
                        Console.WriteLine($"❌ Build failed for command '{command.Name}': {buildEx.Message}");
                        allValid = false;
                        continue;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Validation exception for command '{command?.Name ?? "UNKNOWN"}': {ex.Message}");
                    Console.WriteLine($"📋 Stack trace: {ex.StackTrace}");
                    allValid = false;
                }
            }

            Console.WriteLine($"🔍 Validation complete: {(allValid ? "All commands valid" : "Some commands failed validation")}");
            return allValid;
        }

        /// <summary>
        /// Determines if global commands should be registered based on current state and configuration.
        /// Implements daily registration to avoid timeouts while ensuring commands stay updated.
        /// </summary>
        private async Task<bool> ShouldRegisterGlobalCommands(List<SlashCommandBuilder> newCommands)
        {
            if (_skipGlobalRegistration)
            {
                Console.WriteLine("ℹ️ Global registration disabled by configuration");
                return false;
            }

            // Check daily registration limit (unless disabled)
            if (!_disableDailyLimit && !await ShouldRegisterToday())
            {
                Console.WriteLine("ℹ️ Global registration already completed today, skipping");
                return false;
            }

            lock (_registrationLock)
            {
                if (_globalCommandsRegistered && !_forceGlobalRegistration)
                {
                    Console.WriteLine("ℹ️ Global commands already registered in this session");
                    return false;
                }
            }

            try
            {
                Console.WriteLine("🔍 Checking existing global commands...");
                var existingCommands = await _client.GetGlobalApplicationCommandsAsync();

                Console.WriteLine($"📊 Found {existingCommands.Count} existing global commands");

                if (_forceGlobalRegistration)
                {
                    Console.WriteLine("🔄 Force registration enabled, will update global commands");
                    await UpdateLastRegistrationDate();
                    return true;
                }

                // If no existing commands, we need to register
                if (existingCommands.Count == 0)
                {
                    Console.WriteLine("📝 No existing global commands found, registration needed");
                    await UpdateLastRegistrationDate();
                    return true;
                }

                // Check if commands have changed
                bool commandsChanged = await CompareCommands(existingCommands, newCommands);

                if (commandsChanged)
                {
                    Console.WriteLine("🔄 Commands have changed, registration needed");
                    await UpdateLastRegistrationDate();
                    return true;
                }
                else
                {
                    Console.WriteLine("✅ Commands are up to date, skipping global registration");
                    await UpdateLastRegistrationDate(); // Update date even if skipped to prevent daily attempts
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Error checking existing commands: {ex.Message}");
                Console.WriteLine("🔄 Will proceed with registration to be safe");
                await UpdateLastRegistrationDate();
                return true;
            }
        }

        /// <summary>
        /// Checks if global registration should be performed today based on the last registration date.
        /// </summary>
        private async Task<bool> ShouldRegisterToday()
        {
            try
            {
                // Load last registration date from file
                if (File.Exists(LAST_REGISTRATION_FILE))
                {
                    var lastRegistrationText = await File.ReadAllTextAsync(LAST_REGISTRATION_FILE);
                    if (DateTime.TryParse(lastRegistrationText, out var lastRegistration))
                    {
                        _lastGlobalRegistration = lastRegistration;

                        // Check if it's been at least 24 hours since last registration
                        var timeSinceLastRegistration = DateTime.UtcNow - lastRegistration;
                        if (timeSinceLastRegistration.TotalHours < 24)
                        {
                            Console.WriteLine($"⏰ Last global registration was {timeSinceLastRegistration.TotalHours:F1} hours ago");
                            return false;
                        }
                    }
                }

                Console.WriteLine("📅 Daily global registration check passed");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Error checking last registration date: {ex.Message}");
                return true; // Default to allowing registration if we can't check
            }
        }

        /// <summary>
        /// Updates the last registration date to prevent multiple registrations per day.
        /// </summary>
        private async Task UpdateLastRegistrationDate()
        {
            try
            {
                _lastGlobalRegistration = DateTime.UtcNow;
                await File.WriteAllTextAsync(LAST_REGISTRATION_FILE, _lastGlobalRegistration.ToString("O"));
                Console.WriteLine($"📅 Updated last registration date: {_lastGlobalRegistration:yyyy-MM-dd HH:mm:ss} UTC");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Error updating last registration date: {ex.Message}");
            }
        }

        /// <summary>
        /// Registers guild commands with batching and rate limiting for immediate availability.
        /// </summary>
        private async Task RegisterGuildCommandsWithBatching(List<SlashCommandBuilder> slashCommands)
        {
            Console.WriteLine("🏰 Registering guild commands for immediate availability...");

            var commandArray = slashCommands.Select(x => x.Build()).ToArray();
            int successfulGuilds = 0;
            int totalGuilds = _client.Guilds.Count;

            foreach (var guild in _client.Guilds)
            {
                // Skip if already registered for this guild
                if (_guildCommandsRegistered.Contains(guild.Id))
                {
                    Console.WriteLine($"  ℹ️ Commands already registered for guild: {guild.Name}");
                    continue;
                }

                try
                {
                    Console.WriteLine($"  🔄 Registering commands for guild: {guild.Name} (ID: {guild.Id})");

                    using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(REGISTRATION_TIMEOUT_MS));
                    await guild.BulkOverwriteApplicationCommandAsync(commandArray);

                    _guildCommandsRegistered.Add(guild.Id);
                    successfulGuilds++;
                    Console.WriteLine($"  ✅ Successfully registered {commandArray.Length} commands for guild: {guild.Name}");

                    // Add delay between guild registrations to respect rate limits
                    if (successfulGuilds < totalGuilds)
                    {
                        await Task.Delay(1000); // 1 second delay between guilds
                    }
                }
                catch (Exception guildEx)
                {
                    Console.WriteLine($"  ❌ Failed to register commands for guild {guild.Name}: {guildEx.Message}");
                }
            }

            Console.WriteLine($"🏰 Guild registration complete: {successfulGuilds}/{totalGuilds} guilds successful");
        }

        /// <summary>
        /// Compares existing commands with new commands to determine if registration is needed.
        /// </summary>
        private async Task<bool> CompareCommands(IReadOnlyCollection<IApplicationCommand> existingCommands, List<SlashCommandBuilder> newCommands)
        {
            try
            {
                Console.WriteLine("🔍 Comparing existing commands with new commands...");

                // Quick count check
                if (existingCommands.Count != newCommands.Count)
                {
                    Console.WriteLine($"📊 Command count differs: existing={existingCommands.Count}, new={newCommands.Count}");
                    return true;
                }

                // Create lookup for existing commands
                var existingLookup = existingCommands.ToDictionary(cmd => cmd.Name.ToLower(), cmd => cmd);

                foreach (var newCommand in newCommands)
                {
                    string commandName = newCommand.Name.ToLower();

                    if (!existingLookup.TryGetValue(commandName, out var existingCommand))
                    {
                        Console.WriteLine($"📝 New command found: {commandName}");
                        return true;
                    }

                    // Compare descriptions
                    if (existingCommand.Description != newCommand.Description)
                    {
                        Console.WriteLine($"📝 Description changed for {commandName}");
                        return true;
                    }

                    // Compare option counts (simplified comparison)
                    if (existingCommand.Options.Count != newCommand.Options.Count)
                    {
                        Console.WriteLine($"📝 Options changed for {commandName}");
                        return true;
                    }
                }

                Console.WriteLine("✅ Commands are identical, no changes needed");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Error comparing commands: {ex.Message}");
                return true; // Assume changes if we can't compare
            }
        }

        /// <summary>
        /// Registers global commands with retry logic and exponential backoff.
        /// </summary>
        private async Task RegisterGlobalCommandsWithRetry(List<SlashCommandBuilder> slashCommands)
        {
            Console.WriteLine("🌍 Registering global commands with retry logic...");

            var commandArray = slashCommands.Select(x => x.Build()).ToArray();

            // Log each command being registered for diagnostics
            Console.WriteLine("📋 Commands to register:");
            for (int i = 0; i < commandArray.Length; i++)
            {
                var cmd = commandArray[i];
                Console.WriteLine($"  {i + 1}. /{cmd.Name} - {cmd.Description} ({(cmd.Options.IsSpecified ? cmd.Options.Value.Count : 0)} options)");
            }

            for (int attempt = 1; attempt <= MAX_RETRIES; attempt++)
            {
                try
                {
                    Console.WriteLine($"🔄 Global registration attempt {attempt}/{MAX_RETRIES} ({commandArray.Length} commands)");
                    Console.WriteLine($"⏱️ Using timeout: {REGISTRATION_TIMEOUT_MS}ms");

                    // Create cancellation token and use it properly
                    using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(REGISTRATION_TIMEOUT_MS));

                    // Add progress indicator
                    var registrationTask = _client.BulkOverwriteGlobalApplicationCommandsAsync(commandArray);
                    var delayTask = Task.Delay(5000, cts.Token); // 5 second progress indicator

                    var completedTask = await Task.WhenAny(registrationTask, delayTask);

                    if (completedTask == delayTask && !registrationTask.IsCompleted)
                    {
                        Console.WriteLine("⏳ Registration taking longer than expected, still waiting...");
                        await registrationTask; // Continue waiting for the actual registration
                    }
                    else if (completedTask == registrationTask)
                    {
                        await registrationTask; // Ensure we get the result/exception
                    }

                    Console.WriteLine($"✅ Successfully registered {commandArray.Length} global slash commands!");
                    Console.WriteLine("⏰ Note: Global commands may take up to 1 hour to appear in Discord.");
                    return; // Success, exit retry loop
                }
                catch (TaskCanceledException ex) when (ex.CancellationToken.IsCancellationRequested)
                {
                    Console.WriteLine($"⏱️ Registration timed out after {REGISTRATION_TIMEOUT_MS}ms on attempt {attempt}");
                    if (attempt < MAX_RETRIES)
                    {
                        int delay = CalculateExponentialBackoff(attempt);
                        Console.WriteLine($"⏳ Waiting {delay}ms before retry...");
                        await Task.Delay(delay);
                    }
                }
                catch (HttpException ex) when (ex.HttpCode == HttpStatusCode.TooManyRequests)
                {
                    Console.WriteLine($"🚫 Rate limited on attempt {attempt}: {ex.Message}");
                    if (attempt < MAX_RETRIES)
                    {
                        // Use exponential backoff for rate limits
                        int delay = CalculateExponentialBackoff(attempt) * 3; // Triple delay for rate limits
                        Console.WriteLine($"⏳ Rate limit delay: {delay}ms");
                        await Task.Delay(delay);
                    }
                }
                catch (HttpException ex)
                {
                    Console.WriteLine($"🌐 HTTP error on attempt {attempt}: {ex.HttpCode} - {ex.Message}");
                    if (ex.HttpCode == HttpStatusCode.BadRequest)
                    {
                        Console.WriteLine("❌ Bad request - likely invalid command data, aborting retries");
                        break;
                    }
                    if (attempt < MAX_RETRIES)
                    {
                        int delay = CalculateExponentialBackoff(attempt);
                        Console.WriteLine($"⏳ Waiting {delay}ms before retry...");
                        await Task.Delay(delay);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Unexpected error on attempt {attempt}: {ex.GetType().Name} - {ex.Message}");
                    if (attempt < MAX_RETRIES)
                    {
                        int delay = CalculateExponentialBackoff(attempt);
                        Console.WriteLine($"⏳ Waiting {delay}ms before retry...");
                        await Task.Delay(delay);
                    }
                }
            }

            Console.WriteLine($"❌ Failed to register global commands after {MAX_RETRIES} attempts");
            Console.WriteLine("ℹ️ Guild commands are still available for immediate use");
        }

        /// <summary>
        /// Calculates exponential backoff delay.
        /// </summary>
        private int CalculateExponentialBackoff(int attempt)
        {
            return BASE_DELAY_MS * (int)Math.Pow(2, attempt - 1);
        }

        /// <summary>
        /// Configures slash command registration behavior.
        /// </summary>
        /// <param name="skipGlobalRegistration">If true, skips global command registration entirely</param>
        /// <param name="forceGlobalRegistration">If true, forces global command re-registration even if commands exist</param>
        /// <param name="removeGlobalOnShutdown">If true, removes global commands when bot shuts down</param>
        /// <param name="disableDailyLimit">If true, disables the daily registration limit for development</param>
        public void ConfigureSlashCommandBehavior(bool skipGlobalRegistration = false, bool forceGlobalRegistration = false, bool removeGlobalOnShutdown = false, bool disableDailyLimit = false)
        {
            _skipGlobalRegistration = skipGlobalRegistration;
            _forceGlobalRegistration = forceGlobalRegistration;
            _removeGlobalCommandsOnShutdown = removeGlobalOnShutdown;
            _disableDailyLimit = disableDailyLimit;

            Console.WriteLine("⚙️ Slash command behavior configured:");
            Console.WriteLine($"  • Skip global registration: {skipGlobalRegistration}");
            Console.WriteLine($"  • Force global registration: {forceGlobalRegistration}");
            Console.WriteLine($"  • Remove global on shutdown: {removeGlobalOnShutdown}");
            Console.WriteLine($"   Disable Daily Limit: {disableDailyLimit}");
        }

        /// <summary>
        /// Forces global command registration immediately, bypassing daily limits.
        /// Use this when you need commands to be available globally right away.
        /// </summary>
        public async Task ForceGlobalCommandRegistration()
        {
            Console.WriteLine("🚀 Forcing global command registration...");

            // Temporarily enable force registration
            bool originalForceValue = _forceGlobalRegistration;
            _forceGlobalRegistration = true;

            try
            {
                await RegisterSlashCommandsAsync();
                Console.WriteLine("✅ Global commands force-registered successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to force register global commands: {ex.Message}");
            }
            finally
            {
                // Restore original setting
                _forceGlobalRegistration = originalForceValue;
            }
        }

        /// <summary>
        /// Clears guild commands from all guilds to prevent duplicates with global commands.
        /// </summary>
        private async Task ClearGuildCommands()
        {
            Console.WriteLine("🧹 Clearing guild commands to prevent duplicates...");

            foreach (var guild in _client.Guilds)
            {
                try
                {
                    // Clear all guild commands by setting an empty array
                    await guild.BulkOverwriteApplicationCommandAsync(Array.Empty<ApplicationCommandProperties>());
                    Console.WriteLine($"  ✅ Cleared commands for guild: {guild.Name}");

                    // Remove from tracking
                    _guildCommandsRegistered.Remove(guild.Id);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  ⚠️ Failed to clear commands for guild {guild.Name}: {ex.Message}");
                }

                // Small delay to avoid rate limits
                await Task.Delay(100);
            }
        }

        /// <summary>
        /// Forces registration of DM-compatible commands for use in direct messages and group chats.
        /// This is separate from guild commands and needed for DM functionality.
        /// </summary>
        public async Task ForceGlobalDMCommandRegistration()
        {
            Console.WriteLine("🚀 Forcing global DM command registration...");

            try
            {
                var dmCommands = BuildDMCompatibleSlashCommands();
                Console.WriteLine($"📊 Built {dmCommands.Count} DM-compatible slash commands");

                // Register as global commands with DM permission
                var commandArray = dmCommands.Select(cmd => cmd.Build()).ToArray();

                Console.WriteLine("🔄 Registering DM-compatible global commands...");
                await _client.BulkOverwriteGlobalApplicationCommandsAsync(commandArray);

                Console.WriteLine("✅ DM commands force-registered successfully!");
                Console.WriteLine("⏰ DM commands will be available in 1-2 hours globally");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to force register DM commands: {ex.Message}");
            }
        }

        /// <summary>
        /// Builds slash commands that are compatible with DMs and group chats.
        /// These commands work in both servers and DMs.
        /// </summary>
        private List<SlashCommandBuilder> BuildDMCompatibleSlashCommands()
        {
            var dmCommands = new List<SlashCommandBuilder>();

            // Commands that work well in DMs
            var dmCompatibleCommands = new[]
            {
                "bomb", "bots", "daily", "declarewar", "dismantle", "help", "info",
                "inventory", "ping", "recruit", "sell", "shop", "supportserver",
                "use", "usecode", "leaderboards"
            };

            Console.WriteLine("🔨 Building DM-compatible slash commands...");

            foreach (var commandName in dmCompatibleCommands)
            {
                try
                {
                    SlashCommandBuilder? builder = null;

                    switch (commandName)
                    {
                        case "bomb":
                            builder = new SlashCommandBuilder()
                                .WithName("bomb")
                                .WithDescription("Use a bomb item on a target")
                                .AddOption("target", ApplicationCommandOptionType.User, "User to bomb", true)
                                .AddOption("item_id", ApplicationCommandOptionType.Integer, "Inventory item ID to use", true)
                                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
                            break;

                        case "bots":
                            builder = new SlashCommandBuilder()
                                .WithName("bots")
                                .WithDescription("Check your bot count")
                                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
                            break;

                        case "daily":
                            builder = new SlashCommandBuilder()
                                .WithName("daily")
                                .WithDescription("Claim your daily bot points")
                                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
                            break;

                        case "declarewar":
                            builder = new SlashCommandBuilder()
                                .WithName("declarewar")
                                .WithDescription("Declare war (Discord server required)")
                                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
                            break;

                        case "dismantle":
                            builder = new SlashCommandBuilder()
                                .WithName("dismantle")
                                .WithDescription("Remove your currently active recruitment multiplier")
                                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
                            break;

                        case "help":
                            builder = new SlashCommandBuilder()
                                .WithName("help")
                                .WithDescription("Displays information about available commands")
                                .AddOption("command", ApplicationCommandOptionType.String, "Specific command to get help for", false)
                                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
                            break;

                        case "info":
                            builder = new SlashCommandBuilder()
                                .WithName("info")
                                .WithDescription("Shows information about shop items")
                                .AddOption("item_id", ApplicationCommandOptionType.Integer, "Item ID to get details about", false)
                                .AddOption("page", ApplicationCommandOptionType.Integer, "Page number for item list", false)
                                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
                            break;

                        case "inventory":
                            builder = new SlashCommandBuilder()
                                .WithName("inventory")
                                .WithDescription("Check your inventory of consumables")
                                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
                            break;

                        case "ping":
                            builder = new SlashCommandBuilder()
                                .WithName("ping")
                                .WithDescription("Check bot latency and status")
                                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
                            break;

                        case "recruit":
                            builder = new SlashCommandBuilder()
                                .WithName("recruit")
                                .WithDescription("Recruit bots to your army")
                                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
                            break;

                        case "sell":
                            builder = new SlashCommandBuilder()
                                .WithName("sell")
                                .WithDescription("Sell items from your inventory for 50% of their original price")
                                .AddOption("item_id", ApplicationCommandOptionType.Integer, "Inventory item ID to sell", true)
                                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
                            break;

                        case "shop":
                            builder = new SlashCommandBuilder()
                                .WithName("shop")
                                .WithDescription("Browse and purchase items from the bot shop")
                                .AddOption("category", ApplicationCommandOptionType.String, "Item category to browse", false,
                                    choices: new[]
                                    {
                                        new ApplicationCommandOptionChoiceProperties { Name = "All Items", Value = "all" },
                                        new ApplicationCommandOptionChoiceProperties { Name = "Multipliers", Value = "multipliers" },
                                        new ApplicationCommandOptionChoiceProperties { Name = "Packs", Value = "packs" },
                                        new ApplicationCommandOptionChoiceProperties { Name = "Bombs", Value = "bombs" }
                                    })
                                .AddOption("page", ApplicationCommandOptionType.Integer, "Page number", false)
                                .AddOption("action", ApplicationCommandOptionType.String, "Action to perform", false,
                                    choices: new[]
                                    {
                                        new ApplicationCommandOptionChoiceProperties { Name = "Buy Item", Value = "buy" }
                                    })
                                .AddOption("item_id", ApplicationCommandOptionType.Integer, "Item ID to buy (use with action:buy)", false)
                                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
                            break;

                        case "supportserver":
                            builder = new SlashCommandBuilder()
                                .WithName("supportserver")
                                .WithDescription("Get the link to our support server")
                                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
                            break;

                        case "use":
                            builder = new SlashCommandBuilder()
                                .WithName("use")
                                .WithDescription("Use an item from your inventory")
                                .AddOption("item_id", ApplicationCommandOptionType.Integer, "Inventory item ID to use", true)
                                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
                            break;

                        case "usecode":
                            builder = new SlashCommandBuilder()
                                .WithName("usecode")
                                .WithDescription("Redeem a code for items")
                                .AddOption("code", ApplicationCommandOptionType.String, "Code to redeem", true)
                                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
                            break;

                        case "leaderboards":
                            builder = new SlashCommandBuilder()
                                .WithName("leaderboards")
                                .WithDescription("View the global bot leaderboards")
                                .AddOption("page", ApplicationCommandOptionType.Integer, "Page number", false)
                                .WithContextTypes(InteractionContextType.Guild, InteractionContextType.BotDm, InteractionContextType.PrivateChannel);
                            break;
                    }

                    if (builder != null)
                    {
                        dmCommands.Add(builder);
                        Console.WriteLine($"  📝 Built DM command: {commandName}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  ❌ Failed to build DM command {commandName}: {ex.Message}");
                }
            }

            return dmCommands;
        }

        /// <summary>
        /// Removes specific unwanted admin commands from both global and guild slash command registrations.
        /// This method should be called to clean up any previously registered admin commands.
        /// </summary>
        public async Task RemoveUnwantedAdminCommandsAsync()
        {
            string[] unwantedCommands = { "changeperms", "cleardata", "echo", "editcodes", "editconsumables", "edititems", "edituser", "lock", "unlock" };

            Console.WriteLine("🧹 Removing unwanted admin slash commands...");

            try
            {
                // Remove from global commands
                await RemoveSpecificGlobalCommandsAsync(unwantedCommands);

                // Remove from guild commands
                await RemoveSpecificGuildCommandsAsync(unwantedCommands);

                Console.WriteLine("✅ Successfully removed unwanted admin commands from slash command registrations");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error removing unwanted admin commands: {ex.Message}");
            }
        }

        /// <summary>
        /// Removes specific commands from global registration.
        /// </summary>
        private async Task RemoveSpecificGlobalCommandsAsync(string[] commandsToRemove)
        {
            try
            {
                Console.WriteLine("🌍 Checking global commands for unwanted admin commands...");

                var existingGlobalCommands = await _client.GetGlobalApplicationCommandsAsync();
                var commandsToDelete = existingGlobalCommands.Where(cmd => commandsToRemove.Contains(cmd.Name.ToLower())).ToList();

                if (commandsToDelete.Count == 0)
                {
                    Console.WriteLine("✅ No unwanted admin commands found in global registration");
                    return;
                }

                Console.WriteLine($"🗑️ Found {commandsToDelete.Count} unwanted global commands to remove:");
                foreach (var cmd in commandsToDelete)
                {
                    Console.WriteLine($"  • /{cmd.Name}");
                }

                // Remove each command individually
                foreach (var cmd in commandsToDelete)
                {
                    try
                    {
                        await cmd.DeleteAsync();
                        Console.WriteLine($"  ✅ Removed global command: /{cmd.Name}");
                        await Task.Delay(1000); // Rate limit protection
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"  ❌ Failed to remove global command /{cmd.Name}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error checking global commands: {ex.Message}");
            }
        }

        /// <summary>
        /// Removes specific commands from guild registrations.
        /// </summary>
        private async Task RemoveSpecificGuildCommandsAsync(string[] commandsToRemove)
        {
            try
            {
                Console.WriteLine("🏰 Checking guild commands for unwanted admin commands...");

                foreach (var guild in _client.Guilds)
                {
                    try
                    {
                        var existingGuildCommands = await guild.GetApplicationCommandsAsync();
                        var commandsToDelete = existingGuildCommands.Where(cmd => commandsToRemove.Contains(cmd.Name.ToLower())).ToList();

                        if (commandsToDelete.Count == 0)
                        {
                            Console.WriteLine($"  ✅ No unwanted commands in guild: {guild.Name}");
                            continue;
                        }

                        Console.WriteLine($"  🗑️ Found {commandsToDelete.Count} unwanted commands in guild: {guild.Name}");

                        // Remove each command individually
                        foreach (var cmd in commandsToDelete)
                        {
                            try
                            {
                                await cmd.DeleteAsync();
                                Console.WriteLine($"    ✅ Removed guild command: /{cmd.Name}");
                                await Task.Delay(500); // Rate limit protection
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"    ❌ Failed to remove guild command /{cmd.Name}: {ex.Message}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"  ❌ Error checking guild {guild.Name}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error checking guild commands: {ex.Message}");
            }
        }

        /// <summary>
        /// Lists currently registered slash commands for verification.
        /// </summary>
        private async Task ListRegisteredCommandsAsync()
        {
            try
            {
                Console.WriteLine("📋 Checking currently registered commands...");

                // Check global commands
                var globalCommands = await _client.GetGlobalApplicationCommandsAsync();
                Console.WriteLine($"🌍 Global commands registered: {globalCommands.Count}");
                foreach (var cmd in globalCommands)
                {
                    Console.WriteLine($"  • /{cmd.Name} - {cmd.Description}");
                }

                // Note: Guild command logging removed to avoid confusion with new registration strategy
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error listing registered commands: {ex.Message}");
            }
        }

        // Track cleanup status to prevent duplicate cleanup
        private bool _commandsCleanedUp = false;
        private readonly object _cleanupLock = new object();

        /// <summary>
        /// Removes slash commands from Discord.
        /// This method is called when the bot shuts down.
        /// By default, only guild commands are removed to avoid unnecessary global command cleanup.
        /// </summary>
        public async Task RemoveSlashCommandsAsync()
        {
            lock (_cleanupLock)
            {
                if (_commandsCleanedUp)
                {
                    Console.WriteLine("ℹ️ Slash commands already cleaned up, skipping...");
                    return;
                }
                _commandsCleanedUp = true;
            }

            try
            {
                Console.WriteLine("🧹 Starting slash command cleanup...");

                if (_removeGlobalCommandsOnShutdown)
                {
                    Console.WriteLine("🌍 Removing global commands (enabled by configuration)...");
                    await RemoveGlobalCommandsWithRetry();
                }
                else
                {
                    Console.WriteLine("ℹ️ Skipping global command removal (disabled by default to avoid unnecessary API calls)");
                    Console.WriteLine("💡 Global commands will remain active and available across all servers");
                }

                // Always remove guild-specific commands for clean shutdown
                Console.WriteLine("🏰 Removing guild-specific commands...");
                await RemoveGuildCommandsWithBatching();

                Console.WriteLine("✅ Slash command cleanup completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error during slash command cleanup: {ex.Message}");
                Console.WriteLine($"📋 Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Removes global commands with retry logic.
        /// </summary>
        private async Task RemoveGlobalCommandsWithRetry()
        {
            for (int attempt = 1; attempt <= MAX_RETRIES; attempt++)
            {
                try
                {
                    Console.WriteLine($"🔄 Removing global commands (attempt {attempt}/{MAX_RETRIES})");

                    using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(REGISTRATION_TIMEOUT_MS));
                    await _client.BulkOverwriteGlobalApplicationCommandsAsync(Array.Empty<ApplicationCommandProperties>());

                    Console.WriteLine("✅ Successfully removed all global slash commands");
                    return; // Success, exit retry loop
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Failed to remove global commands (attempt {attempt}): {ex.Message}");
                    if (attempt < MAX_RETRIES)
                    {
                        int delay = CalculateExponentialBackoff(attempt);
                        Console.WriteLine($"⏳ Waiting {delay}ms before retry...");
                        await Task.Delay(delay);
                    }
                }
            }

            Console.WriteLine($"❌ Failed to remove global commands after {MAX_RETRIES} attempts");
        }

        /// <summary>
        /// Removes guild commands with batching and rate limiting.
        /// </summary>
        private async Task RemoveGuildCommandsWithBatching()
        {
            Console.WriteLine("🏰 Removing guild commands...");

            int successfulGuilds = 0;
            int totalGuilds = _client.Guilds.Count;

            foreach (var guild in _client.Guilds)
            {
                try
                {
                    Console.WriteLine($"  🔄 Removing commands from guild: {guild.Name}");

                    using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(REGISTRATION_TIMEOUT_MS));
                    await guild.BulkOverwriteApplicationCommandAsync(Array.Empty<ApplicationCommandProperties>());

                    _guildCommandsRegistered.Remove(guild.Id); // Update tracking
                    successfulGuilds++;
                    Console.WriteLine($"  ✅ Removed commands from guild: {guild.Name}");

                    // Add delay between guild operations to respect rate limits
                    if (successfulGuilds < totalGuilds)
                    {
                        await Task.Delay(500); // 500ms delay between guilds for cleanup
                    }
                }
                catch (Exception guildEx)
                {
                    Console.WriteLine($"  ❌ Failed to remove commands from guild {guild.Name}: {guildEx.Message}");
                }
            }

            Console.WriteLine($"🏰 Guild cleanup complete: {successfulGuilds}/{totalGuilds} guilds successful");
        }

        /// <summary>
        /// Starts the bot by registering commands and connecting to Discord.
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task StartAsync()
        {
            // ==================== DISCORD CLIENT FUNCTIONALITY ====================
            // The Discord client provides many methods for interacting with Discord's API.
            // Here are some important functionalities you might want to use:

            // PERMISSIONS SYSTEM
            // Discord uses a permission system to control what users and bots can do.
            // When adding your bot to a server, you need to request the right permissions.
            // Common permissions include:
            // - SendMessages: Ability to send messages in text channels
            // - ReadMessageHistory: Ability to read message history
            // - ManageMessages: Ability to delete/pin messages
            // - AttachFiles: Ability to upload files
            // - EmbedLinks: Ability to send embedded links
            // - AddReactions: Ability to add reactions to messages
            // - UseExternalEmojis: Ability to use emojis from other servers
            // - ManageChannels: Ability to create/edit/delete channels
            // - ManageRoles: Ability to manage roles
            // - KickMembers/BanMembers: Ability to kick/ban members

            // RATE LIMITS
            // Discord has rate limits to prevent abuse. If you hit these limits,
            // your bot may be temporarily blocked from performing actions.
            // Discord.Net handles most rate limits automatically, but you should
            // still be mindful of how many requests your bot makes.

            // SLASH COMMANDS
            // Discord supports slash commands (/) which are registered with Discord's API.
            // This bot uses text commands with a prefix, but you could extend it to use
            // slash commands for a more integrated experience.
            // To implement slash commands, you would use Discord.Net's InteractionService.

            // EMBEDS AND RICH MESSAGES
            // Discord supports rich embeds for more visually appealing messages.
            // The bot already uses these in the help command, but you can use them
            // for other commands as well.

            // REACTIONS
            // Bots can add reactions to messages and listen for reactions from users.
            // This can be used for creating interactive menus, polls, etc.

            // FILE UPLOADS
            // Bots can upload files to Discord channels using:
            // await channel.SendFileAsync("path/to/file.txt", "Optional comment");

            // DIRECT MESSAGES
            // Bots can send direct messages to users using:
            // await user.SendMessageAsync("Hello!");

            // VOICE SUPPORT
            // Discord.Net supports voice connections for music bots, etc.
            // This requires additional setup and libraries like Discord.Net.Audio.
            // ===================================================================

            // Register all commands before connecting
            RegisterCommands();

            // Log in to Discord with the bot token from configuration
            await _client.LoginAsync(TokenType.Bot, Config.Token);

            // Start the WebSocket connection to Discord
            await _client.StartAsync();
        }

        /// <summary>
        /// Stops the bot and removes slash commands from Discord.
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task StopAsync()
        {
            try
            {
                Console.WriteLine("🛑 Initiating bot shutdown...");

                // Remove slash commands first (RemoveSlashCommandsAsync handles its own logging)
                await RemoveSlashCommandsAsync();

                // Stop the Discord client
                Console.WriteLine("🔌 Stopping Discord client...");
                await _client.StopAsync();
                await _client.LogoutAsync();

                Console.WriteLine("✅ Bot shutdown completed successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error during bot shutdown: {ex.Message}");
                Console.WriteLine($"📋 Stack trace: {ex.StackTrace}");
            }
        }
    }
}
