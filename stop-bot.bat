@echo off
setlocal EnableDelayedExpansion
title <PERSON><PERSON><PERSON><PERSON> Shutdown
echo Stopping Bot-<PERSON>...

:: Create a flag to track if we found the process
set "FOUND=0"

:: Method 1: Find by window title
echo Searching for <PERSON><PERSON><PERSON><PERSON> by window title...
for /f "tokens=2" %%a in ('tasklist /fi "WINDOWTITLE eq <PERSON><PERSON><PERSON><PERSON> Discord <PERSON>" /fo list ^| find "PID:"') do (
    set BOT_PID=%%a
    set "FOUND=1"
    echo Found Bot-Chan process with PID !BOT_PID! (by window title)
    goto :PROCESS_FOUND
)

:: Method 2: Find by command line containing <PERSON><PERSON><PERSON><PERSON>
if "!FOUND!"=="0" (
    echo Searching for <PERSON><PERSON><PERSON><PERSON> by process name...
    for /f "tokens=2" %%a in ('wmic process where "commandline like '%%BotChan%%'" get processid /format:list ^| find "ProcessId"') do (
        set BOT_PID=%%a
        set "FOUND=1"
        echo Found Bot-Chan process with PID !BOT_PID! (by process name)
        goto :PROCESS_FOUND
    )
)

:: Method 3: Last resort - find any dotnet process in the Bot-<PERSON> directory
if "!FOUND!"=="0" (
    echo Searching for any dotnet process in Bot-<PERSON> directory...
    for /f "tokens=2" %%a in ('wmic process where "commandline like '%%dotnet%%' AND commandline like '%%Bot-Chan%%'" get processid /format:list ^| find "ProcessId"') do (
        set BOT_PID=%%a
        set "FOUND=1"
        echo Found potential Bot-Chan process with PID !BOT_PID! (by directory)
        goto :PROCESS_FOUND
    )
)

:: If all methods failed
if "!FOUND!"=="0" (
    echo Could not find Bot-Chan process. It may not be running.
    echo If the bot is still running, you may need to close it manually.
    goto :EXIT
)

:PROCESS_FOUND
:: Create a shutdown signal file to trigger graceful shutdown
echo Attempting graceful shutdown to save data...

:: Create the shutdown signal file
echo %DATE% %TIME% > shutdown.signal

:: Wait a moment for the bot to detect the signal and save data
echo Waiting for Bot-Chan to save data and shut down (10 seconds)...
timeout /t 10 /nobreak > nul

:: Check if the process is still running
tasklist /fi "PID eq !BOT_PID!" /fo list 2>nul | find "PID:" > nul
if not errorlevel 1 (
    echo Process is still running. Trying to send Ctrl+C signal...
    taskkill /pid !BOT_PID! /im dotnet.exe 2>nul

    :: Wait a bit more for the process to exit
    echo Waiting 5 more seconds...
    timeout /t 5 /nobreak > nul

    :: Check again if the process is still running
    tasklist /fi "PID eq !BOT_PID!" /fo list 2>nul | find "PID:" > nul
    if not errorlevel 1 (
        echo Process is still running. Forcing termination...
        taskkill /f /pid !BOT_PID!
        if errorlevel 1 (
            echo Failed to terminate process. You may need to close it manually.
        ) else (
            echo Bot-Chan has been forcefully stopped.
        )
    ) else (
        echo Bot-Chan has been gracefully stopped with data saved.
    )
) else (
    echo Bot-Chan has been gracefully stopped with data saved.
)

:EXIT
echo Done.
echo Press any key to exit...
pause > nul
endlocal