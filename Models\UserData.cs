using System.Text.Json.Serialization;
using System.Text;

namespace BotChan.Models
{
    /// <summary>
    /// Stores data for a user in the bot system.
    /// </summary>
    public class UserData
    {
        /// <summary>
        /// The Discord user ID.
        /// </summary>
        public ulong UserId { get; set; }

        /// <summary>
        /// The Discord username of the user.
        /// </summary>
        public string Username { get; set; } = "Unknown User";

        /// <summary>
        /// The user's avatar URL.
        /// </summary>
        public string AvatarUrl { get; set; } = "";

        /// <summary>
        /// The user's discriminator (the #1234 part).
        /// </summary>
        public string Discriminator { get; set; } = "0000";

        /// <summary>
        /// Whether the user is a bot.
        /// </summary>
        public bool IsBot { get; set; } = false;

        /// <summary>
        /// The timestamp when the user was last seen (used a command).
        /// </summary>
        public DateTime LastSeen { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// The timestamp when the user last received a daily summary.
        /// </summary>
        public DateTime LastDailySummary { get; set; } = DateTime.MinValue;

        /// <summary>
        /// The number of wars the user has participated in.
        /// </summary>
        public int TotalWars { get; set; } = 0;

        /// <summary>
        /// The number of AI wars the user has participated in.
        /// </summary>
        public int AiWars { get; set; } = 0;

        /// <summary>
        /// The number of player wars the user has participated in.
        /// </summary>
        public int PlayerWars { get; set; } = 0;

        /// <summary>
        /// The number of wars the user has won.
        /// </summary>
        public int WarsWon { get; set; } = 0;

        /// <summary>
        /// The number of bots gained from wars.
        /// </summary>
        public long BotsGainedFromWars { get; set; } = 0;

        /// <summary>
        /// The number of bots lost in wars.
        /// </summary>
        public long BotsLostInWars { get; set; } = 0;

        /// <summary>
        /// The timestamp when the war statistics were last reset (for daily tracking).
        /// </summary>
        public DateTime LastWarStatsReset { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Dictionary tracking the last time this user attacked specific targets.
        /// The key is the target user ID, and the value is when they were last attacked.
        /// </summary>
        public Dictionary<ulong, DateTime> LastAttackedTargets { get; set; } = new Dictionary<ulong, DateTime>();

        /// <summary>
        /// The number of bots the user has collected.
        /// </summary>
        public long BotCount { get; set; } = 0;

        /// <summary>
        /// The user's inventory of consumable items.
        /// </summary>
        public List<UserConsumable> Inventory { get; set; } = new List<UserConsumable>();

        /// <summary>
        /// The ID of the currently active consumable, if any.
        /// </summary>
        public string ActiveConsumableId { get; set; } = string.Empty;

        /// <summary>
        /// The number of uses remaining for the active consumable.
        /// </summary>
        public int ActiveConsumableUsesRemaining { get; set; } = 0;

        /// <summary>
        /// The multiplier of the active consumable.
        /// </summary>
        public double ActiveConsumableMultiplier { get; set; } = 1.0;

        /// <summary>
        /// Adds bots to the user's count, up to the maximum limit of 1,000,000.
        /// </summary>
        /// <param name="amount">The amount of bots to add.</param>
        /// <returns>True if the full amount was added, false if the limit was reached.</returns>
        public bool AddBots(long amount)
        {
            const long MAX_BOT_LIMIT = 1000000; // 1 million bot limit

            // Check if already at or above the limit
            if (BotCount >= MAX_BOT_LIMIT)
            {
                return false;
            }

            // Calculate how many bots can be added without exceeding the limit
            long botsToAdd = Math.Min(amount, MAX_BOT_LIMIT - BotCount);

            // Add the bots (either the full amount or up to the limit)
            BotCount += botsToAdd;

            // If we hit the limit exactly, cap it to ensure we never exceed it
            if (BotCount > MAX_BOT_LIMIT)
            {
                BotCount = MAX_BOT_LIMIT;
            }

            // Return true if all bots were added, false if limited
            return botsToAdd == amount;
        }

        /// <summary>
        /// Removes bots from the user's count.
        /// </summary>
        /// <param name="amount">The amount of bots to remove.</param>
        /// <returns>True if the user had enough bots, false otherwise.</returns>
        public bool RemoveBots(long amount)
        {
            if (BotCount < amount)
            {
                return false;
            }

            BotCount -= amount;
            return true;
        }

        /// <summary>
        /// Adds a consumable to the user's inventory.
        /// </summary>
        /// <param name="consumableId">The ID of the consumable.</param>
        /// <param name="usesRemaining">The number of uses remaining.</param>
        public void AddConsumable(string consumableId, int usesRemaining)
        {
            // Get the inventory ID for this consumable type
            int inventoryId = GetInventoryIdForConsumable(consumableId);

            // Always add as a new item (no stacking)
            Inventory.Add(new UserConsumable
            {
                ConsumableId = consumableId,
                UsesRemaining = usesRemaining,
                InventoryId = inventoryId
            });
        }

        /// <summary>
        /// Gets the inventory ID for a specific consumable type.
        /// If the consumable already exists in the inventory, returns its ID.
        /// Otherwise, assigns a new unique ID.
        /// </summary>
        /// <param name="consumableId">The consumable ID to find or assign an inventory ID for.</param>
        /// <returns>The inventory ID for this consumable type.</returns>
        private int GetInventoryIdForConsumable(string consumableId)
        {
            // Check if this consumable type already exists in the inventory
            var existingConsumable = Inventory.FirstOrDefault(c => c.ConsumableId == consumableId);
            if (existingConsumable != null)
            {
                // Return the existing inventory ID for this consumable type
                return existingConsumable.InventoryId;
            }

            // If this is a new consumable type, assign a new unique ID
            return GetNextAvailableInventoryId();
        }

        /// <summary>
        /// Gets the next available inventory ID that isn't already in use.
        /// </summary>
        /// <returns>The next available inventory ID.</returns>
        private int GetNextAvailableInventoryId()
        {
            if (Inventory.Count == 0)
            {
                return 1;
            }

            // Find the smallest positive integer that isn't already used as an inventory ID
            var usedIds = Inventory.Select(c => c.InventoryId).ToHashSet();
            int nextId = 1;

            while (usedIds.Contains(nextId))
            {
                nextId++;
            }

            return nextId;
        }

        /// <summary>
        /// Uses a consumable from the user's inventory by its consumable ID.
        /// </summary>
        /// <param name="consumableId">The ID of the consumable to use.</param>
        /// <returns>True if the consumable was used, false if the user doesn't have it.</returns>
        public bool UseConsumable(string consumableId)
        {
            var consumable = Inventory.FirstOrDefault(c => c.ConsumableId == consumableId);

            if (consumable == null || consumable.UsesRemaining <= 0)
            {
                return false;
            }

            consumable.UsesRemaining--;

            // Remove the consumable from inventory if no uses remain
            if (consumable.UsesRemaining <= 0)
            {
                Inventory.Remove(consumable);
            }

            return true;
        }

        /// <summary>
        /// Uses a consumable from the user's inventory by its inventory ID.
        /// </summary>
        /// <param name="inventoryId">The inventory ID of the consumable to use.</param>
        /// <returns>True if the consumable was used, false if the user doesn't have it.</returns>
        public bool UseConsumableByInventoryId(int inventoryId)
        {
            var consumable = Inventory.FirstOrDefault(c => c.InventoryId == inventoryId);

            if (consumable == null || consumable.UsesRemaining <= 0)
            {
                return false;
            }

            consumable.UsesRemaining--;

            // Remove the consumable from inventory if no uses remain
            if (consumable.UsesRemaining <= 0)
            {
                Inventory.Remove(consumable);
            }

            return true;
        }

        /// <summary>
        /// Gets a consumable from the user's inventory by its consumable ID.
        /// </summary>
        /// <param name="consumableId">The ID of the consumable.</param>
        /// <returns>The consumable, or null if the user doesn't have it.</returns>
        public UserConsumable GetConsumable(string consumableId)
        {
            return Inventory.FirstOrDefault(c => c.ConsumableId == consumableId);
        }

        /// <summary>
        /// Gets a consumable from the user's inventory by its inventory ID.
        /// </summary>
        /// <param name="inventoryId">The inventory ID of the consumable.</param>
        /// <returns>The consumable, or null if the user doesn't have it.</returns>
        public UserConsumable GetConsumableByInventoryId(int inventoryId)
        {
            return Inventory.FirstOrDefault(c => c.InventoryId == inventoryId);
        }

        /// <summary>
        /// Updates the user's information from a Discord user object.
        /// </summary>
        /// <param name="user">The Discord user object.</param>
        public void UpdateUserInfo(Discord.IUser user)
        {
            if (user == null) return;

            Username = user.Username;
            AvatarUrl = user.GetAvatarUrl() ?? user.GetDefaultAvatarUrl();
            Discriminator = user.Discriminator;
            IsBot = user.IsBot;
            LastSeen = DateTime.UtcNow;
        }

        /// <summary>
        /// Gets the user's display name, which is the username.
        /// </summary>
        /// <returns>The user's display name.</returns>
        public string GetDisplayName()
        {
            return Username;
        }

        /// <summary>
        /// Gets the user's avatar URL, or a default URL if none is available.
        /// </summary>
        /// <returns>The user's avatar URL.</returns>
        public string GetAvatarUrl()
        {
            if (string.IsNullOrEmpty(AvatarUrl))
            {
                // Return a default avatar URL
                return "https://cdn.discordapp.com/embed/avatars/0.png";
            }
            return AvatarUrl;
        }

        /// <summary>
        /// Records a war result for the user.
        /// </summary>
        /// <param name="won">Whether the user won the war.</param>
        /// <param name="botsGained">The number of bots gained (if won).</param>
        /// <param name="botsLost">The number of bots lost (if lost).</param>
        /// <param name="isAiWar">Whether this was a war against AI (true) or another player (false).</param>
        public void RecordWarResult(bool won, long botsGained, long botsLost, bool isAiWar = false)
        {
            // Increment total wars
            TotalWars++;

            // Track AI vs Player wars
            if (isAiWar)
            {
                AiWars++;
            }
            else
            {
                PlayerWars++;
            }

            // Record win/loss
            if (won)
            {
                WarsWon++;
                BotsGainedFromWars += botsGained;
            }
            else
            {
                BotsLostInWars += botsLost;
            }
        }

        /// <summary>
        /// Checks if the user should receive a daily summary and updates the last summary timestamp.
        /// </summary>
        /// <returns>True if this is the first summary of the day, false otherwise.</returns>
        public bool ShouldReceiveDailySummary()
        {
            // Get the current date (UTC)
            DateTime now = DateTime.UtcNow;
            DateTime today = now.Date; // Just the date part, no time

            // Check if the last summary was on a different day
            bool isFirstOfDay = LastDailySummary.Date < today;

            // Also check if the last war stats reset was on a different day
            // This handles cases where the bot was restarted and stats weren't reset
            if (LastWarStatsReset.Date < today)
            {
                isFirstOfDay = true;
            }

            // Always update the last summary timestamp
            LastDailySummary = now;

            return isFirstOfDay;
        }

        /// <summary>
        /// Gets a daily summary message for the user.
        /// </summary>
        /// <param name="resetStats">Whether to reset the war statistics after generating the summary. Default is true.</param>
        /// <returns>A formatted message with the user's war statistics.</returns>
        public string GetDailySummary(bool resetStats = true)
        {
            // Calculate time since last activity
            TimeSpan timeSinceLastReset = DateTime.UtcNow - LastWarStatsReset;
            string timeDescription;

            if (timeSinceLastReset.TotalDays >= 1)
            {
                int days = (int)timeSinceLastReset.TotalDays;
                timeDescription = $"the last {days} {(days == 1 ? "day" : "days")}";
            }
            else
            {
                timeDescription = "today";
            }

            // Create the summary message
            StringBuilder summary = new StringBuilder();
            summary.AppendLine($"**<:BM_BotChanOhISee:1410316825681727661> | Daily Bot War Summary**");
            summary.AppendLine();

            if (TotalWars > 0)
            {
                summary.AppendLine($"During {timeDescription}, you have:");
                summary.AppendLine($"• Participated in **{TotalWars}** {(TotalWars == 1 ? "war" : "wars")}")
                       .AppendLine($"• **{AiWars}** vs AI {(AiWars == 1 ? "war" : "wars")}")
                       .AppendLine($"• **{PlayerWars}** vs Player {(PlayerWars == 1 ? "war" : "wars")}")
                       .AppendLine($"• Won **{WarsWon}** {(WarsWon == 1 ? "battle" : "battles")}")
                       .AppendLine($"• Lost **{TotalWars - WarsWon}** {(TotalWars - WarsWon == 1 ? "battle" : "battles")}");

                if (BotsGainedFromWars > 0)
                {
                    summary.AppendLine($"• Captured **{BotsGainedFromWars}** enemy bots <:Bot:1363239519914164264>");
                }

                if (BotsLostInWars > 0)
                {
                    summary.AppendLine($"• Lost **{BotsLostInWars}** of your bots <:Bot:1363239519914164264>");
                }

                long netChange = BotsGainedFromWars - BotsLostInWars;
                if (netChange > 0)
                {
                    summary.AppendLine($"• Net gain of **{netChange}** bots <:Bot:1363239519914164264>");
                }
                else if (netChange < 0)
                {
                    summary.AppendLine($"• Net loss of **{Math.Abs(netChange)}** bots <:Bot:1363239519914164264>");
                }
                else
                {
                    summary.AppendLine($"• No net change in bot count");
                }
            }
            else
            {
                summary.AppendLine($"You haven't participated in any wars {timeDescription}.");
                summary.AppendLine($"Use `%declarewar AI` or `%declarewar [user ID]` to start a battle!");
            }

            // Reset the war statistics for the new day if requested
            if (resetStats)
            {
                ResetWarStats();
            }

            return summary.ToString();
        }

        /// <summary>
        /// Resets the war statistics for a new day.
        /// </summary>
        public void ResetWarStats()
        {
            TotalWars = 0;
            AiWars = 0;
            PlayerWars = 0;
            WarsWon = 0;
            BotsGainedFromWars = 0;
            BotsLostInWars = 0;
            LastWarStatsReset = DateTime.UtcNow;
        }

        /// <summary>
        /// Records that this user has attacked a specific target.
        /// </summary>
        /// <param name="targetUserId">The ID of the target user.</param>
        public void RecordAttack(ulong targetUserId)
        {
            LastAttackedTargets[targetUserId] = DateTime.UtcNow;
        }

        /// <summary>
        /// Checks if this user can attack a specific target based on cooldown.
        /// </summary>
        /// <param name="targetUserId">The ID of the target user.</param>
        /// <param name="cooldownHours">The cooldown period in hours.</param>
        /// <param name="remainingTime">If on cooldown, contains the remaining time until the cooldown expires.</param>
        /// <returns>True if the user can attack, false if on cooldown.</returns>
        public bool CanAttackTarget(ulong targetUserId, int cooldownHours, out TimeSpan remainingTime)
        {
            remainingTime = TimeSpan.Zero;

            // Check if the user has attacked this target before
            if (LastAttackedTargets.TryGetValue(targetUserId, out DateTime lastAttackTime))
            {
                // Calculate the time since the last attack
                TimeSpan timeSinceLastAttack = DateTime.UtcNow - lastAttackTime;

                // Check if the cooldown has expired
                TimeSpan cooldownPeriod = TimeSpan.FromHours(cooldownHours);
                if (timeSinceLastAttack < cooldownPeriod)
                {
                    // Still on cooldown
                    remainingTime = cooldownPeriod - timeSinceLastAttack;
                    return false;
                }
            }

            // No cooldown or cooldown has expired
            return true;
        }
    }

    /// <summary>
    /// Represents a consumable item in a user's inventory.
    /// </summary>
    public class UserConsumable
    {
        /// <summary>
        /// The ID of the consumable.
        /// </summary>
        public string ConsumableId { get; set; } = string.Empty;

        /// <summary>
        /// The number of uses remaining for this consumable.
        /// </summary>
        public int UsesRemaining { get; set; }

        /// <summary>
        /// The unique inventory ID for this item instance.
        /// </summary>
        public int InventoryId { get; set; }
    }
}
