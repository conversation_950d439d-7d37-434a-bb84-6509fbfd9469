using Discord;
using Discord.WebSocket;
using BotChan.Services;
using BotChan.Models;
using System.Text;
using System.Linq;

namespace BotChan.Commands.SlashCommands
{
    /// <summary>
    /// InventorySlashCommand - Slash command version of the inventory command
    /// Usage: /inventory
    /// Shows the user's inventory with silent mode support
    /// </summary>
    public class InventorySlashCommand
    {
        /// <summary>
        /// Executes the inventory slash command.
        /// </summary>
        /// <param name="command">The slash command that was executed</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task ExecuteAsync(SocketSlashCommand command, DiscordBotClient client)
        {
            try
            {
                // Check cooldown before allowing the command (same as %inventory)
                if (!client.Permissions.HasPermission(command.User.Id, "inventory", out string cooldownMessage))
                {
                    if (!string.IsNullOrEmpty(cooldownMessage))
                    {
                        await command.RespondAsync($"⏰ **Inventory command is on cooldown!** {cooldownMessage}", ephemeral: true);
                    }
                    else
                    {
                        await command.RespondAsync("❌ You don't have permission to use this command.", ephemeral: true);
                    }
                    return;
                }

                // Update last usage time for cooldown tracking
                client.Permissions.UpdateCommandUsage(command.User.Id, "inventory");

                // Get user data
                var userData = client.BotSystem.GetUserData(command.User.Id, command.User.Username);

                // Build the inventory display in a grid format (match %inventory exactly)
                var sb = new StringBuilder();

                // Add the header
                sb.AppendLine($"**<a:BotChanHappy:1410317358852997233> [====== {command.User.Username}'s Arsenal ======] <a:BotChanHappy:1410317358852997233>**");

                // Group consumables by their InventoryId to stack identical items
                var groupedInventory = userData.Inventory
                    .GroupBy(c => c.InventoryId)
                    .Select(g => new {
                        InventoryId = g.Key,
                        ConsumableId = g.First().ConsumableId,
                        Count = g.Count()
                    })
                    .OrderBy(g => g.InventoryId)
                    .ToList();

                // Display items in a grid, 4 items per row
                const int itemsPerRow = 4;
                for (int i = 0; i < groupedInventory.Count; i += itemsPerRow)
                {
                    var rowItems = new List<string>();

                    // Process up to 4 items for this row
                    for (int j = 0; j < itemsPerRow && i + j < groupedInventory.Count; j++)
                    {
                        var group = groupedInventory[i + j];
                        var consumable = client.BotSystem.GetConsumable(group.ConsumableId);

                        if (consumable != null)
                        {
                            // Format: `ID`emoji superscript_count
                            string countText = FormatUsesAsSuperscript(group.Count);
                            rowItems.Add($"`{group.InventoryId:D3}`{consumable.Emoji}{countText}");
                        }
                    }

                    // Add the row to the output
                    if (rowItems.Count > 0)
                    {
                        sb.AppendLine(string.Join("    ", rowItems));
                    }
                }

                // Check if the user has any consumables
                if (userData.Inventory.Count == 0)
                {
                    sb.AppendLine();
                    sb.AppendLine(" ");
                    sb.AppendLine("Your inventory is empty. Purchase consumables from the shop to get started!");
                    sb.AppendLine("Use `%shop` to browse available consumables");

                    await command.RespondAsync(sb.ToString());
                    return;
                }

                // Add active consumable info if applicable
                if (!string.IsNullOrEmpty(userData.ActiveConsumableId))
                {
                    var activeConsumable = client.BotSystem.GetConsumable(userData.ActiveConsumableId);
                    if (activeConsumable != null)
                    {
                        sb.AppendLine();
                        sb.AppendLine($"**Active Consumable:** {activeConsumable.Name} {activeConsumable.Emoji} ({userData.ActiveConsumableMultiplier}x)");
                        sb.AppendLine($"Uses remaining: **{userData.ActiveConsumableUsesRemaining}**");
                        sb.AppendLine("You cannot activate another consumable until this one is fully used.");
                    }
                }
                else
                {
                    sb.AppendLine();
                    sb.AppendLine("Use `%use [ID]` to activate a consumable. Example: `%use 1`");
                }

                // Send the inventory as a plain message
                await command.RespondAsync(sb.ToString());
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing inventory slash command: {ex.Message}");

                if (!command.HasResponded)
                {
                    await command.RespondAsync("❌ An error occurred while retrieving your inventory.", ephemeral: true);
                }
            }
        }

        /// <summary>
        /// Formats a number as superscript text.
        /// </summary>
        /// <param name="number">The number to format.</param>
        /// <returns>The number as superscript text.</returns>
        private static string FormatUsesAsSuperscript(int number)
        {
            var digits = number.ToString().ToCharArray();
            var result = new StringBuilder();

            foreach (char digit in digits)
            {
                switch (digit)
                {
                    case '0': result.Append("\u2070"); break; // ⁰
                    case '1': result.Append("\u00b9"); break; // ¹
                    case '2': result.Append("\u00b2"); break; // ²
                    case '3': result.Append("\u00b3"); break; // ³
                    case '4': result.Append("\u2074"); break; // ⁴
                    case '5': result.Append("\u2075"); break; // ⁵
                    case '6': result.Append("\u2076"); break; // ⁶
                    case '7': result.Append("\u2077"); break; // ⁷
                    case '8': result.Append("\u2078"); break; // ⁸
                    case '9': result.Append("\u2079"); break; // ⁹
                    default: result.Append(digit); break;
                }
            }

            return result.ToString();
        }
    }
}
