using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;

namespace BotChan.Commands
{
    /// <summary>
    /// UseCommand - A command that uses a consumable from the user's inventory
    /// Usage: %use [inventory_id]
    /// Example: %use 1
    /// </summary>
    public class UseCommand : Command
    {
        // Override the default category
        public override string Category => "Economy";

        // Override the default usage
        public override string Usage => "%use [inventory_id]";

        // Override the default examples
        public override string Examples => "%use 1";

        public UseCommand() : base("use", "Use a consumable from your inventory")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Get the user's data
            var userData = client.BotSystem.GetUserData(message.Author.Id);

            // Check if the user has any items in their inventory
            if (userData.Inventory.Count == 0)
            {
                await message.Channel.SendMessageAsync($"<:BM_BChanSmug2:1200974229059932301> | {message.Author.Username}, You do not have any items to use to begin with. Go to my shop to get some!");
                return;
            }

            // Check if the user provided an inventory ID
            if (args.Length < 2)
            {
                await message.Channel.SendMessageAsync("Please specify an inventory ID to use. Example: `%use 1`");
                return;
            }

            // Try to parse the inventory ID
            if (!int.TryParse(args[1], out int inventoryId))
            {
                await message.Channel.SendMessageAsync("Invalid inventory ID. Please provide a numeric ID. Use `%inventory` to see your consumables.");
                return;
            }

            // Check if the user already has an active consumable (only for multiplier items)
            if (!string.IsNullOrEmpty(userData.ActiveConsumableId))
            {
                var activeConsumable = client.BotSystem.GetConsumable(userData.ActiveConsumableId);
                if (activeConsumable != null)
                {
                    // Get the consumable the user is trying to use
                    var itemToUse = userData.GetConsumableByInventoryId(inventoryId);
                    if (itemToUse != null)
                    {
                        var consumableToUse = client.BotSystem.GetConsumable(itemToUse.ConsumableId);

                        // If it's a bot reward item (not a consumable), allow it to be used
                        if (consumableToUse != null && consumableToUse.BotReward > 0)
                        {
                            // This is a bot reward item, so we'll allow it to be used even with an active consumable
                        }
                        else
                        {
                            // This is a consumable item, so we can't use it while another is active
                            await message.Channel.SendMessageAsync($"<:BM_BotChanReaction:1143303450369736754> | Oops {message.Author.Username}, it seems like you already have a consumable active. You cannot activate another consumable until this one is fully used!");
                            return;
                        }
                    }
                }
                else
                {
                    // If the active consumable no longer exists, clear it
                    userData.ActiveConsumableId = string.Empty;
                    client.BotSystem.SaveUserData();
                }
            }

            // Check if the user has the consumable with the given inventory ID
            var userConsumable = userData.GetConsumableByInventoryId(inventoryId);
            if (userConsumable == null)
            {
                await message.Channel.SendMessageAsync($"You don't have a consumable with inventory ID `{inventoryId}`. Use `%inventory` to see your consumables.");
                return;
            }

            // Get the consumable details
            var consumable = client.BotSystem.GetConsumable(userConsumable.ConsumableId);
            if (consumable == null)
            {
                await message.Channel.SendMessageAsync($"Error: Consumable not found in the system.");
                return;
            }

            // Check if this is a bomb item (Category 3)
            if (consumable.Category == 3)
            {
                await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1142948885694787684> | {message.Author.Username}**, bomb items cannot be used with the `%use` command. Use `%bomb [target_user_id] [inventory_id]` instead to attack other players.");
                return;
            }

            // Check if this is a bot reward item or a consumable
            if (consumable.Category == 2) // Bot Pack
            {
                // This is a bot reward item - give the bots immediately
                long botsAwarded = consumable.BotReward;

                // Add the bots to the user's account
                client.BotSystem.AddBots(message.Author.Id, botsAwarded);

                // Create the success message for bot reward item
                string responseMessage = $"<a:BM_BotChanHappy:1158391055998259270> ✨ | {message.Author.Username}, you used **{consumable.Name}** {consumable.Emoji} and received **{botsAwarded}** <:Bot:1363239519914164264>!\n\n" +
                    $"Your new bot count: **{userData.BotCount}** <:Bot:1363239519914164264>";

                // Send the response
                await message.Channel.SendMessageAsync(responseMessage);

                // Remove the consumable from inventory immediately
                userData.Inventory.Remove(userConsumable);

                // Save the user data
                client.BotSystem.SaveUserData();
            }
            else
            {
                // This is a consumable item
                // Create the success message for consumable
                string responseMessage = $"<a:BM_BotChanHappy:1158391055998259270> ✨ | {message.Author.Username}, you activated the following item!\n" +
                    $"**{consumable.Name}** {consumable.Emoji} (x{consumable.Multiplier} multiplier for {userConsumable.UsesRemaining} uses)\n\n" +
                    $"**Note**\n" +
                    $"This consumable will remain active until all uses are exhausted. You cannot activate another consumable until this one is fully used.";

                // Send the response
                await message.Channel.SendMessageAsync(responseMessage);

                // Set this consumable as the active one and store its properties
                userData.ActiveConsumableId = userConsumable.ConsumableId;
                userData.ActiveConsumableUsesRemaining = userConsumable.UsesRemaining;
                userData.ActiveConsumableMultiplier = consumable.Multiplier;

                // Remove the consumable from inventory immediately
                userData.Inventory.Remove(userConsumable);

                // Save the user data
                client.BotSystem.SaveUserData();
            }
        }
    }
}
