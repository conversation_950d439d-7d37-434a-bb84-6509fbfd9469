using Discord;
using Discord.WebSocket;
using BotChan.Services;
using BotChan.Models;
using System.Text;

namespace BotChan.Commands.SlashCommands
{
    /// <summary>
    /// ShopSlashCommand - Slash command version of the shop command
    /// Usage: /shop [category] [page] [action] [item_id]
    /// Browse and purchase consumables with your bots
    /// </summary>
    public class ShopSlashCommand
    {
        // Number of items to display per page
        private const int ItemsPerPage = 10;

        /// <summary>
        /// Executes the shop slash command.
        /// </summary>
        /// <param name="command">The slash command that was executed</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task ExecuteAsync(SocketSlashCommand command, DiscordBotClient client)
        {
            try
            {
                // Check cooldown before allowing the command (same as %shop)
                if (!client.Permissions.HasPermission(command.User.Id, "shop", out string cooldownMessage))
                {
                    if (!string.IsNullOrEmpty(cooldownMessage))
                    {
                        await command.RespondAsync($"⏰ **Shop command is on cooldown!** {cooldownMessage}", ephemeral: true);
                    }
                    else
                    {
                        await command.RespondAsync("❌ You don't have permission to use this command.", ephemeral: true);
                    }
                    return;
                }

                // Update last usage time for cooldown tracking
                client.Permissions.UpdateCommandUsage(command.User.Id, "shop");

                // Get command options
                var categoryOption = command.Data.Options.FirstOrDefault(x => x.Name == "category");
                var pageOption = command.Data.Options.FirstOrDefault(x => x.Name == "page");
                var actionOption = command.Data.Options.FirstOrDefault(x => x.Name == "action");
                var itemIdOption = command.Data.Options.FirstOrDefault(x => x.Name == "item_id");

                // Check if the user wants to buy something
                if (actionOption != null && actionOption.Value.ToString().ToLower() == "buy")
                {
                    if (itemIdOption == null)
                    {
                        await command.RespondAsync("Please specify an item ID to buy.\n\nExample: `/shop action:buy category:bombs item_id:1`", ephemeral: true);
                        return;
                    }

                    if (categoryOption == null)
                    {
                        await command.RespondAsync("Please specify a category when buying.\n\nExample: `/shop action:buy category:bombs item_id:1`", ephemeral: true);
                        return;
                    }

                    if (!int.TryParse(itemIdOption.Value.ToString(), out int itemId))
                    {
                        await command.RespondAsync("Invalid item ID. Please provide a numeric ID.\n\nExample: `/shop action:buy category:bombs item_id:1`", ephemeral: true);
                        return;
                    }

                    string categoryName = categoryOption.Value.ToString().ToLower();
                    await BuyConsumable(command, categoryName, itemId, client);
                    return;
                }

                // Parse category
                int category = 0;
                if (categoryOption != null)
                {
                    category = GetCategoryFromString(categoryOption.Value.ToString().ToLower());
                    if (category == -1)
                    {
                        await command.RespondAsync("Invalid category. Please use one of the following:\n• `consumables`\n• `vanity`\n• `bombs`", ephemeral: true);
                        return;
                    }
                }

                // Parse page
                int page = 1;
                if (pageOption != null)
                {
                    if (!int.TryParse(pageOption.Value.ToString(), out page))
                    {
                        await command.RespondAsync("Please specify a valid page number.", ephemeral: true);
                        return;
                    }
                }

                // Show appropriate shop view
                if (category == 0)
                {
                    await ShowMainShopMenu(command, client);
                }
                else
                {
                    await ShowCategoryShop(command, category, page, client);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing shop slash command: {ex.Message}");
                
                if (!command.HasResponded)
                {
                    await command.RespondAsync("❌ An error occurred while processing the shop command.", ephemeral: true);
                }
            }
        }

        private static int GetCategoryFromString(string categoryArg)
        {
            return categoryArg switch
            {
                "consumables" or "consumable" or "multipliers" or "multiplier" or "mult" or "1" => 1,
                "vanity" or "van" or "botvanity" or "botvan" or "packs" or "pack" or "2" => 2,
                "bombs" or "bomb" or "3" => 3,
                _ => -1
            };
        }

        private static string GetCategoryName(int category)
        {
            return category switch
            {
                1 => "Consumables",
                2 => "Vanity",
                3 => "Bombs",
                _ => "Unknown"
            };
        }

        private static async Task ShowMainShopMenu(SocketSlashCommand command, DiscordBotClient client)
        {
            // Get the user's data
            var userData = client.BotSystem.GetUserData(command.User.Id);

            // Get all visible consumables
            var allConsumables = client.BotSystem.GetAllConsumables()
                .Where(c => c.IsVisibleInShop)
                .ToList();

            // Count items by category
            int multipliersCount = allConsumables.Count(c => c.Category == 1);
            int packsCount = allConsumables.Count(c => c.Category == 2);
            int bombsCount = allConsumables.Count(c => c.Category == 3);

            // Build the description for the main shop (exact same as %shop)
            var description = new StringBuilder();
            description.AppendLine("**Available Commands:**");
            description.AppendLine("`/shop category:consumables`");
            description.AppendLine("`/shop category:vanity`");
            description.AppendLine("`/shop category:bombs`");
            description.AppendLine("");
            description.AppendLine("`/shop action:buy category:{Category} item_id:{id}` to buy an item");
            description.AppendLine("`/sell item_id:{inventory id}` to sell an item for 50% of its original price");
            description.AppendLine("`/shop category:{Category} page:[1-2]` to navigate pages");
            description.AppendLine("════════════════════════════════");

            // Add categories in the same format as items
            string[] categories = {
                $"1 ⭐ Consumables",
                $"2 📦 Vanity",
                $"3 💣 Bombs"
            };

            int[] counts = { multipliersCount, packsCount, bombsCount };
            for (int i = 0; i < categories.Length; i++)
            {
                string categoryLine = categories[i];

                // Calculate dashes for alignment (same as %shop)
                int targetWidth = 40; // Same target width as items
                int currentLength = GetDisplayLength(categoryLine);
                int dashCount = Math.Max(1, targetWidth - currentLength);
                string dashes = new string('-', dashCount);

                description.AppendLine($"{categoryLine}{dashes} {counts[i]} items");
            }

            // Create the embed (exact same as %shop)
            var embed = new EmbedBuilder()
                .WithAuthor("Bot-Chan's Secret Armory", command.User.GetAvatarUrl() ?? command.User.GetDefaultAvatarUrl())
                .WithTitle("Shop Categories")
                .WithDescription(description.ToString())
                .WithColor(Color.Blue)
                .WithFooter($"You have {userData.BotCount:N0} bots")
                .WithTimestamp(DateTimeOffset.Now);

            await command.RespondAsync(embed: embed.Build());
        }

        private static async Task ShowCategoryShop(SocketSlashCommand command, int category, int page, DiscordBotClient client)
        {
            // Get the user's data
            var userData = client.BotSystem.GetUserData(command.User.Id);

            // Get all visible consumables for this category
            var consumables = client.BotSystem.GetAllConsumables()
                .Where(c => c.IsVisibleInShop && c.Category == category)
                .OrderBy(c => c.Cost) // Order by cost like in the original
                .ToList();

            if (consumables.Count == 0)
            {
                await command.RespondAsync($"No items available in the {GetCategoryName(category)} category.", ephemeral: true);
                return;
            }

            // Calculate total pages
            int totalPages = (int)Math.Ceiling(consumables.Count / (double)ItemsPerPage);
            if (totalPages == 0) totalPages = 1;

            // Ensure page is within valid range
            page = Math.Max(1, Math.Min(page, totalPages));

            // Get consumables for the current page
            var pageConsumables = consumables
                .Skip((page - 1) * ItemsPerPage)
                .Take(ItemsPerPage)
                .ToList();

            // Build the description like the original format (exact same as %shop)
            var description = new StringBuilder();

            // Category-specific descriptions
            if (category == 1) // Consumables/Multipliers
            {
                description.AppendLine("Purchase a consumable for more bots!");
                description.AppendLine("The better the consumable the more bots you acquire!");
            }
            else if (category == 2) // vanity
            {
                description.AppendLine("Purchase vanity items to customize your inventory!");
                description.AppendLine("Vanity can be turned back into bots when you don't want them anymore!");
            }
            else if (category == 3) // Bombs
            {
                description.AppendLine("Purchase bombs and gift bombs for strategic gameplay!");
                description.AppendLine("Attack enemies or gift bots to allies!");
            }
            description.AppendLine($"`/shop action:buy category:{{category}} item_id:{{id}}` to buy an item");
            description.AppendLine($"`/sell item_id:{{id}}` to sell an item for 50% of its original price");
            description.AppendLine($"`/shop category:{{category}} page:[1-{totalPages}]` to navigate pages");
            description.AppendLine("════════════════════════════════");

            // Add items in the original format with dashes (same as %shop)
            int displayIndex = (page - 1) * ItemsPerPage + 1;
            foreach (var consumable in pageConsumables)
            {
                string costDisplay = FormatNumber(consumable.Cost);
                string itemLine = $"{displayIndex} {consumable.Emoji} {consumable.Name}";

                // Calculate dashes for alignment (same as %shop)
                int targetWidth = 40; // Target width for alignment
                int currentLength = GetDisplayLength(itemLine);
                int dashCount = Math.Max(1, targetWidth - currentLength);
                string dashes = new string('-', dashCount);

                description.AppendLine($"{itemLine}{dashes} {costDisplay} {Constants.Emojis.BotDown}");
                displayIndex++;
            }

            var embed = new EmbedBuilder()
                .WithAuthor("Bot-Chan's Secret Armory", command.User.GetAvatarUrl() ?? command.User.GetDefaultAvatarUrl())
                .WithTitle(GetCategoryName(category))
                .WithDescription(description.ToString())
                .WithColor(Color.Blue)
                .WithFooter($"Page {page}/{totalPages} | You have {userData.BotCount:N0} bots")
                .WithTimestamp(DateTimeOffset.Now);

            await command.RespondAsync(embed: embed.Build());
        }

        private static string FormatNumber(int number)
        {
            if (number >= 1000000)
                return $"{number / 1000000}M";
            else if (number >= 1000)
                return $"{number / 1000}K";
            else
                return number.ToString();
        }

        /// <summary>
        /// Calculates the display length of a string, treating Discord custom emojis as single characters.
        /// </summary>
        private static int GetDisplayLength(string text)
        {
            if (string.IsNullOrEmpty(text))
                return 0;

            // Replace Discord custom emojis with single characters for length calculation
            // Discord custom emojis have the format <:name:id> or <a:name:id>
            var customEmojiPattern = @"<a?:[^:]+:\d+>";
            var withoutCustomEmojis = System.Text.RegularExpressions.Regex.Replace(text, customEmojiPattern, "E");

            return withoutCustomEmojis.Length;
        }

        private static async Task BuyConsumable(SocketSlashCommand command, string categoryName, int displayIndex, DiscordBotClient client)
        {
            // Get category from string
            int category = GetCategoryFromString(categoryName);
            if (category == -1)
            {
                await command.RespondAsync("Invalid category. Please specify: consumables, vanity, or bombs", ephemeral: true);
                return;
            }

            // Get all visible consumables for this category
            var consumables = client.BotSystem.GetAllConsumables()
                .Where(c => c.IsVisibleInShop && c.Category == category)
                .OrderBy(c => c.Cost)
                .ToList();

            // Check if the display index is valid
            if (displayIndex < 1 || displayIndex > consumables.Count)
            {
                await command.RespondAsync($"Invalid item number. Please choose a number between 1 and {consumables.Count} for {GetCategoryName(category)}.", ephemeral: true);
                return;
            }

            // Get the consumable by display index (1-based)
            var consumable = consumables[displayIndex - 1];

            // Get the user's data
            var userData = client.BotSystem.GetUserData(command.User.Id);

            // Check if the user has enough bots
            if (userData.BotCount < consumable.Cost)
            {
                await command.RespondAsync($"You don't have enough bots to purchase **{consumable.Name}**. You need **{consumable.Cost}** bots, but you only have **{userData.BotCount}**.", ephemeral: true);
                return;
            }

            // Purchase the consumable
            bool success = client.BotSystem.PurchaseConsumable(command.User.Id, consumable.Id);

            if (success)
            {
                // Create an embed for the response (exact same as %shop)
                var description = new StringBuilder();

                description.AppendLine($"You've purchased **{consumable.Name}** {consumable.Emoji} for **{consumable.Cost}** {Constants.Emojis.BotDown}!");
                description.AppendLine();

                if (consumable.Category == 1) // Multiplier
                {
                    description.AppendLine($"• This consumable gives you a **{consumable.Multiplier}x** multiplier for **{consumable.DefaultUses}** uses.");
                    description.AppendLine($"• Use it with the `/use [inventory_id]` command.");
                }
                else if (consumable.Category == 2) // Vanity
                {
                    description.AppendLine($"• This vanity item can be turned back into **{consumable.BotReward:N0}** bots.");
                    description.AppendLine($"• Use it with the `/use [inventory_id]` command to claim back bots");
                }
                else if (consumable.Category == 3) // Bomb
                {
                    description.AppendLine($"• This bomb has a **100%** success rate.");
                    description.AppendLine($"• Use it with the `/bomb [target_user_id] [inventory_id]` command to attack other players.");
                }

                description.AppendLine();
                description.AppendLine($"**Remaining Bots:** {userData.BotCount:N0} {Constants.Emojis.BotDown}");

                var embed = new EmbedBuilder()
                    .WithAuthor("Bot-Chan's Secret Armory", command.User.GetAvatarUrl() ?? command.User.GetDefaultAvatarUrl())
                    .WithTitle("✅ Purchase Successful!")
                    .WithDescription(description.ToString())
                    .WithColor(Color.Green)
                    .WithThumbnailUrl("https://cdn.discordapp.com/attachments/1363245551184908468/1363389520245952672/botchan_icon.png")
                    .WithTimestamp(DateTimeOffset.Now);

                await command.RespondAsync(embed: embed.Build());
            }
            else
            {
                await command.RespondAsync("❌ Purchase failed. Please try again.", ephemeral: true);
            }
        }
    }
}
