using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;
using System.Text;

namespace BotChan.Commands
{
    /// <summary>
    /// RecruitCommand - A command that gives the user bots
    /// Usage: %recruit
    /// Example: %recruit
    /// </summary>
    public class RecruitCommand : Command
    {
        // Override the default category
        public override string Category => "Economy";

        // Override the default usage
        public override string Usage => "%recruit";

        // Override the default examples
        public override string Examples => "%recruit";

        public RecruitCommand() : base("recruit", "Recruit bots to add to your collection")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Get the user's data
            var userData = client.BotSystem.GetUserData(message.Author.Id);

            // Check if the user has an active consumable
            double multiplier = 1.0;
            string activeConsumableName = string.Empty;

            // If the user has an active consumable, use it
            if (!string.IsNullOrEmpty(userData.ActiveConsumableId))
            {
                var consumable = client.BotSystem.GetConsumable(userData.ActiveConsumableId);

                if (consumable != null && userData.ActiveConsumableUsesRemaining > 0)
                {
                    // Use the active consumable
                    multiplier = userData.ActiveConsumableMultiplier;
                    activeConsumableName = consumable.Name;

                    // Reduce the uses remaining
                    userData.ActiveConsumableUsesRemaining--;

                    // If the consumable has been fully used, clear the active consumable
                    if (userData.ActiveConsumableUsesRemaining <= 0)
                    {
                        userData.ActiveConsumableId = string.Empty;
                        userData.ActiveConsumableMultiplier = 1.0;
                        userData.ActiveConsumableUsesRemaining = 0;
                    }

                    // Save the user data
                    client.BotSystem.SaveUserData();
                }
                else
                {
                    // If the consumable no longer exists, clear the active consumable
                    userData.ActiveConsumableId = string.Empty;
                    userData.ActiveConsumableMultiplier = 1.0;
                    userData.ActiveConsumableUsesRemaining = 0;
                    client.BotSystem.SaveUserData();
                }
            }

            // Calculate the reward
            long baseReward = client.BotSystem.Config.BaseRecruitReward;
            long totalReward = (long)Math.Ceiling(baseReward * multiplier);

            // Add the bots to the user's count
            bool fullAmountAdded = client.BotSystem.AddBots(message.Author.Id, totalReward);

            // Get the updated user data
            userData = client.BotSystem.GetUserData(message.Author.Id);

            // Build the bot emoji string with directly embedded emoji (max 20 emojis)
            string botEmojis = string.Empty;
            long emojiCount = Math.Min(totalReward, 20); // Limit to maximum 20 emojis
            for (long i = 0; i < emojiCount; i++)
            {
                // add randomized bot emojis
                int RandomBot = new Random().Next(1, 5);
                
                if (RandomBot == 1)
                {
                    botEmojis += "<:BotDown:1410320403469176913> ";
                }
                else if (RandomBot == 2)
                {
                    botEmojis += "<:RBotDown:1411710381755207701> ";
                }
                else if (RandomBot == 3)
                {
                    botEmojis += "<:GBotDown:1411710401464369153> ";
                }
                else if (RandomBot == 4)
                {
                    botEmojis += "<:BBotDown:1411710393277087744> ";
                }
            }

            // Create the message with the specified format and directly embedded emojis
            string responseMessage;

            // Check if a consumable is being used
            if (!string.IsNullOrEmpty(activeConsumableName))
            {
                // Get the consumable to access its emoji
                var consumable = client.BotSystem.GetConsumable(userData.ActiveConsumableId);

                // Check if this was the last use of the consumable
                bool isLastUse = userData.ActiveConsumableUsesRemaining == 0;

                if (isLastUse)
                {
                    // This was the last use of the consumable
                    responseMessage = $"**<a:BM_BotChanDance2:1410331782607540245> | {message.Author.Username} found the following bots!** **This consumable has run out!**\n**<:Blank:1410316480905609256> |** {botEmojis}";
                }
                else if (consumable != null && !string.IsNullOrEmpty(consumable.Emoji))
                {
                    // Format with the consumable info and emoji
                    responseMessage = $"**<a:BM_BotChanDance2:1410331782607540245> | {message.Author.Username} found the following bots!** **Empowered by {consumable.Emoji}!**\n**<:Blank:1410316480905609256> |** {botEmojis}";
                }
                else
                {
                    // Format with just the consumable name if emoji is not available
                    responseMessage = $"**<a:BM_BotChanDance2:1410331782607540245> | {message.Author.Username} found the following bots!** **Empowered by {activeConsumableName}!**\n**<:Blank:1410316480905609256> |** {botEmojis}";
                }
            }
            else
            {
                // Format without consumable info
                responseMessage = $"**<a:BM_BotChanDance2:1410331782607540245> | {message.Author.Username} found the following bots!**\n**<:Blank:1410316480905609256> |** {botEmojis}";
            }

            // Add tip about consumables if the user has any but no active consumable
            if (userData.Inventory.Count > 0 && string.IsNullOrEmpty(userData.ActiveConsumableId))
            {
                responseMessage += "\n\n*Tip: Use `%use [consumable_id]` to activate a consumable for more bots!*";
            }

            // Add a message if the user reached the bot limit
            if (!fullAmountAdded)
            {
                responseMessage += "\n\n**🏆 Congratulations! You've reached the maximum limit of 1,000,000 bots! Any other bots found will be discarded. Use boxes to store them! 🏆**";
            }

            // Send the response
            await message.Channel.SendMessageAsync(responseMessage);
        }
    }
}
