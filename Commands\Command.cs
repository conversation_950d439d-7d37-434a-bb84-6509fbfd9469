using Discord;
using Discord.WebSocket;
using BotChan.Services;
using System.Text;

namespace BotChan.Commands
{
    /// <summary>
    /// Base class for all bot commands
    /// </summary>
    public abstract class Command
    {
        /// <summary>
        /// The name of the command (used to invoke it)
        /// </summary>
        public string Name { get; }

        /// <summary>
        /// A short description of what the command does
        /// </summary>
        public string Description { get; }

        /// <summary>
        /// The category this command belongs to (used for organizing help)
        /// </summary>
        public virtual string Category => "General";

        /// <summary>
        /// Usage instructions for the command
        /// </summary>
        public virtual string Usage => $"%{Name}";

        /// <summary>
        /// Examples of how to use the command
        /// </summary>
        public virtual string Examples => $"%{Name}";

        /// <summary>
        /// Whether this command is exempt from lockdown restrictions.
        /// Only staff-only commands should be exempt.
        /// </summary>
        public virtual bool IsExemptFromLockdown => false;

        /// <summary>
        /// Creates a new command with the specified name and description
        /// </summary>
        /// <param name="name">The name of the command (used to invoke it)</param>
        /// <param name="description">A short description of what the command does</param>
        protected Command(string name, string description)
        {
            Name = name;
            Description = description;
        }

        /// <summary>
        /// Executes the command logic
        /// </summary>
        /// <param name="message">The message that triggered the command</param>
        /// <param name="args">Command arguments (args[0] is the command name)</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public virtual async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // For backward compatibility, check if this is a derived class that overrides ExecuteAsync
            // but doesn't override ExecuteCommandAsync
            var method = GetType().GetMethod("ExecuteAsync", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Public);
            if (method != null)
            {
                var declaringType = method.DeclaringType;

                if (declaringType != null && declaringType != typeof(Command))
                {
                    // This is a derived class that overrides ExecuteAsync directly
                    // Let the derived implementation handle it
                    return;
                }
            }

            // Otherwise, call the ExecuteCommandAsync method
            await ExecuteCommandAsync(message, args, client);
        }

        /// <summary>
        /// Implements the command logic (to be overridden by derived classes)
        /// </summary>
        /// <param name="message">The message that triggered the command</param>
        /// <param name="args">Command arguments (args[0] is the command name)</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        protected virtual Task ExecuteCommandAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // This is a fallback implementation for backward compatibility
            // It should never be called directly
            return Task.CompletedTask;
        }
    }
}
