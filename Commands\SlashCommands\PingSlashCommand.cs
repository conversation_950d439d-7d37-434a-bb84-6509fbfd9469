using Discord;
using Discord.WebSocket;
using BotChan.Services;

namespace BotChan.Commands.SlashCommands
{
    /// <summary>
    /// PingSlashCommand - Slash command version of the ping command
    /// Usage: /ping
    /// Shows bot latency and status with silent mode support
    /// </summary>
    public class PingSlashCommand
    {
        /// <summary>
        /// Executes the ping slash command.
        /// </summary>
        /// <param name="command">The slash command that was executed</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task ExecuteAsync(SocketSlashCommand command, DiscordBotClient client)
        {
            try
            {
                // Calculate latency
                var latency = client._client.Latency;

                // Determine latency status and color
                Color embedColor;
                string latencyStatus;
                string latencyEmoji;

                if (latency < 100)
                {
                    embedColor = Color.Green;
                    latencyStatus = "Excellent";
                    latencyEmoji = "🟢";
                }
                else if (latency < 200)
                {
                    embedColor = Color.Orange;
                    latencyStatus = "Good";
                    latencyEmoji = "🟡";
                }
                else
                {
                    embedColor = Color.Red;
                    latencyStatus = "Poor";
                    latencyEmoji = "🔴";
                }

                // Create the embed response
                var embed = new EmbedBuilder()
                    .WithTitle("🏓 Pong!")
                    .WithDescription($"Bot is online and responding!")
                    .WithColor(embedColor)
                    .AddField("📡 Latency", $"{latencyEmoji} {latency}ms ({latencyStatus})", true)
                    .AddField("⏰ Response Time", $"< 1 second", true)
                    .AddField("🤖 Status", "✅ Operational", true)
                    .WithTimestamp(DateTimeOffset.Now);

                embed.WithFooter("💬 Everyone can see this");

                // Respond with the embed
                await command.RespondAsync(embed: embed.Build());
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing ping slash command: {ex.Message}");
                
                if (!command.HasResponded)
                {
                    await command.RespondAsync("❌ An error occurred while processing the ping command.", ephemeral: true);
                }
            }
        }
    }
}
