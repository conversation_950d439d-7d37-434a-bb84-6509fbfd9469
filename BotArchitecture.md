# Bot-Chan Architecture Guide

This document explains how the Bot-Chan Discord bot is structured and how to create new commands.

## Core Components

### Program.cs
The entry point of the application. It:
- Loads configuration from appsettings.json
- Creates and initializes the DiscordBotClient
- Keeps the application running

### Models/BotConfig.cs
Contains configuration settings for the bot:
- Discord Bot Token
- Command Prefix

### Services/DiscordBotClient.cs
The main bot client that:
- Connects to <PERSON>rd
- Handles incoming messages
- Registers and executes commands
- Manages the bot's lifecycle

### Commands/Command.cs
The base abstract class for all commands:
- Defines the command name and description
- Provides the abstract ExecuteAsync method that all commands must implement

## How Commands Work

1. **Command Registration**:
   - When the bot starts, the `RegisterCommands` method in `DiscordBotClient.cs` uses reflection to find all classes that inherit from `Command`
   - Each command is instantiated and added to the `_commands` dictionary with its name as the key

2. **Message Processing**:
   - When a message is received, the bot checks if it starts with the configured prefix
   - If it does, the bot extracts the command name and arguments
   - The bot looks up the command in the `_commands` dictionary
   - If found, the command's `ExecuteAsync` method is called with the message, arguments, and client

3. **Command Execution**:
   - Each command implements its own logic in the `ExecuteAsync` method
   - Commands can send messages, create embeds, and interact with Discord in various ways
   - Commands can access the bot client to use bot-wide functionality

## Creating a New Command

To create a new command:

1. Create a new class in the Commands folder that inherits from `Command`
2. Implement the constructor, calling the base constructor with the command name and description
3. Override the `ExecuteAsync` method to implement the command's functionality
4. The command will be automatically registered when the bot starts

Example:
```csharp
public class MyCommand : Command
{
    public MyCommand() : base("mycommand", "Description of my command")
    {
    }

    public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
    {
        // Command implementation
        await message.Channel.SendMessageAsync("Hello from my command!");
    }
}
```

## Special Features

### Special Command Handling
The bot has special handling for certain commands:
- The "%ping" command is handled directly in the `OnMessageReceived` method
- This demonstrates how to implement commands that don't use the standard prefix

### Discord.Net Features
The bot uses Discord.Net library which provides:
- Message handling
- Embed creation
- User and server information
- And many more Discord API features

## Example Commands

The bot includes several example commands that demonstrate different features:

1. **EchoCommand**: A simple command that echoes back the user's message
2. **RandomCommand**: Generates random numbers with optional min/max parameters
3. **UserInfoCommand**: Displays information about a user, demonstrating how to work with Discord users and servers
4. **WeatherCommand**: A simulated command showing how you might integrate with external APIs
5. **RollCommand**: A dice rolling command that demonstrates parsing complex input with regular expressions

## Best Practices

1. **Command Structure**:
   - Keep commands focused on a single responsibility
   - Use clear, descriptive names and descriptions
   - Validate user input before processing

2. **Error Handling**:
   - Use try/catch blocks to handle exceptions
   - Provide helpful error messages to users
   - Log errors for debugging

3. **Performance**:
   - Initialize resources in the constructor when possible
   - Use async/await for all Discord API calls
   - Be mindful of rate limits when making external API calls

4. **User Experience**:
   - Use embeds for complex responses
   - Provide clear usage instructions
   - Consider using emojis to make responses more visually appealing
