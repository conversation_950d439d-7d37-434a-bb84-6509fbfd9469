using System.Text.Json.Serialization;
using System.Collections.Concurrent;

namespace BotChan.Models
{
    /// <summary>
    /// Stores permission settings for the bot.
    /// </summary>
    public class Permissions
    {
        /// <summary>
        /// Dictionary mapping command names to their permission levels.
        /// </summary>
        public Dictionary<string, PermissionLevel> CommandPermissions { get; set; } = new Dictionary<string, PermissionLevel>();

        /// <summary>
        /// List of user IDs that are considered staff members.
        /// </summary>
        public List<ulong> StaffUsers { get; set; } = new List<ulong>();

        /// <summary>
        /// Dictionary mapping command names to lists of whitelisted user IDs.
        /// </summary>
        public Dictionary<string, List<ulong>> WhitelistedUsers { get; set; } = new Dictionary<string, List<ulong>>();

        /// <summary>
        /// Dictionary mapping command names to lists of blacklisted user IDs.
        /// </summary>
        public Dictionary<string, List<ulong>> BlacklistedUsers { get; set; } = new Dictionary<string, List<ulong>>();

        /// <summary>
        /// Dictionary mapping command names to their cooldown times in seconds.
        /// </summary>
        public Dictionary<string, int> CommandCooldowns { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// Dictionary tracking the last usage time of commands by users.
        /// The key is a combination of user ID and command name.
        /// </summary>
        public Dictionary<string, DateTime> LastCommandUsage { get; set; } = new Dictionary<string, DateTime>();

        /// <summary>
        /// Dictionary tracking recent command usage for rate limiting.
        /// The key is the user ID, and the value is a list of command usage timestamps.
        /// This is not serialized to JSON as it's only for runtime use.
        /// </summary>
        [JsonIgnore]
        private readonly ConcurrentDictionary<ulong, List<(string CommandName, DateTime Timestamp)>> _recentCommandUsage = new ConcurrentDictionary<ulong, List<(string, DateTime)>>();

        /// <summary>
        /// Dictionary tracking when users were last rate limited.
        /// The key is the user ID, and the value is when they were rate limited.
        /// </summary>
        [JsonIgnore]
        private readonly ConcurrentDictionary<ulong, DateTime> _rateLimitedUsers = new ConcurrentDictionary<ulong, DateTime>();

        /// <summary>
        /// The maximum number of different commands a user can execute within the rate limit window.
        /// </summary>
        public int MaxDifferentCommandsPerWindow { get; set; } = 4;

        /// <summary>
        /// The maximum number of times a user can execute the same command within the rate limit window.
        /// </summary>
        public int MaxSameCommandPerWindow { get; set; } = 5;

        /// <summary>
        /// The rate limit window in seconds.
        /// </summary>
        public int RateLimitWindowSeconds { get; set; } = 5;

        /// <summary>
        /// The user ID of the bot owner who has full permissions.
        /// </summary>
        [JsonIgnore]
        public ulong OwnerId { get; set; }

        /// <summary>
        /// Checks if a user has permission to use a command.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="commandName">The name of the command.</param>
        /// <param name="cooldownMessage">If cooldown is active, contains a message with remaining time.</param>
        /// <param name="checkCooldown">Whether to check command cooldown.</param>
        /// <returns>True if the user has permission, false otherwise.</returns>
        public bool HasPermission(ulong userId, string commandName, out string cooldownMessage, bool checkCooldown = true)
        {
            cooldownMessage = string.Empty;

            // Creator always has permission and bypasses cooldowns
            if (userId == 513793435807907841)
            {
                return true;
            }

            // Bot owner always has permission and bypasses cooldowns (except for Creator-only commands)
            if (userId == OwnerId)
            {
                // Owner can use all commands except Creator-only ones
                if (!CommandPermissions.TryGetValue(commandName, out var permLevel) || permLevel != PermissionLevel.Creator)
                {
                    return true;
                }
            }

            // Check cooldown if enabled
            if (checkCooldown && CommandCooldowns.TryGetValue(commandName, out int cooldownSeconds) && cooldownSeconds > 0)
            {
                string key = $"{userId}:{commandName}";
                if (LastCommandUsage.TryGetValue(key, out DateTime lastUsage))
                {
                    TimeSpan elapsed = DateTime.UtcNow - lastUsage;
                    if (elapsed.TotalSeconds < cooldownSeconds)
                    {
                        // Calculate when the cooldown expires
                        DateTime expiresAt = lastUsage.AddSeconds(cooldownSeconds);

                        // Convert to Unix timestamp (seconds since epoch)
                        long expiresAtUnix = ((DateTimeOffset)expiresAt).ToUnixTimeSeconds();

                        // Format using Discord's timestamp format with relative time style (R)
                        cooldownMessage = $"This command is on cooldown. You can use it again <t:{expiresAtUnix}:R>.";
                        return false;
                    }
                }
            }

            // Check if the command exists in permissions
            if (!CommandPermissions.TryGetValue(commandName, out var permissionLevel))
            {
                // Default to Everyone if not specified
                permissionLevel = PermissionLevel.Everyone;
            }

            // Check if the command is disabled
            if (permissionLevel == PermissionLevel.Disabled)
            {
                return false;
            }

            // Check if the user is blacklisted for this command
            if (BlacklistedUsers.TryGetValue(commandName, out var blacklist) && blacklist.Contains(userId))
            {
                return false;
            }

            // Check permission level
            switch (permissionLevel)
            {
                case PermissionLevel.Everyone:
                    return true;

                case PermissionLevel.Whitelist:
                    return WhitelistedUsers.TryGetValue(commandName, out var whitelist) && whitelist.Contains(userId);

                case PermissionLevel.Staff:
                    return StaffUsers.Contains(userId);

                case PermissionLevel.Creator:
                    return userId == 513793435807907841;

                default:
                    return false;
            }
        }

        /// <summary>
        /// Checks if a user has permission to use a command.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="commandName">The name of the command.</param>
        /// <returns>True if the user has permission, false otherwise.</returns>
        public bool HasPermission(ulong userId, string commandName)
        {
            return HasPermission(userId, commandName, out _, true);
        }

        /// <summary>
        /// Updates the last usage time for a command by a user.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="commandName">The name of the command.</param>
        public void UpdateCommandUsage(ulong userId, string commandName)
        {
            string key = $"{userId}:{commandName}";
            LastCommandUsage[key] = DateTime.UtcNow;

            // Also update the recent command usage for rate limiting
            UpdateRecentCommandUsage(userId, commandName);
        }

        /// <summary>
        /// Updates the recent command usage for a user for rate limiting purposes.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="commandName">The name of the command.</param>
        private void UpdateRecentCommandUsage(ulong userId, string commandName)
        {
            // Get or create the list of recent commands for this user
            var userCommands = _recentCommandUsage.GetOrAdd(userId, _ => new List<(string, DateTime)>());

            // Add the current command usage
            lock (userCommands)
            {
                // Remove expired entries first
                DateTime cutoff = DateTime.UtcNow.AddSeconds(-RateLimitWindowSeconds);
                userCommands.RemoveAll(cmd => cmd.Timestamp < cutoff);

                // Add the new command usage
                userCommands.Add((commandName, DateTime.UtcNow));
            }
        }

        /// <summary>
        /// Cleans up inactive users from the rate limiting dictionaries to prevent memory leaks.
        /// This is called periodically when checking rate limits.
        /// </summary>
        private void CleanupInactiveUsers()
        {
            // Only run cleanup occasionally (1 in 100 calls) to avoid performance impact
            if (new Random().Next(100) != 0) return;

            DateTime cutoff = DateTime.UtcNow.AddMinutes(-30); // Remove users inactive for 30+ minutes

            // Clean up rate limited users
            foreach (var userId in _rateLimitedUsers.Keys)
            {
                if (_rateLimitedUsers.TryGetValue(userId, out var timestamp) && timestamp < cutoff)
                {
                    _rateLimitedUsers.TryRemove(userId, out _);
                }
            }

            // Clean up recent command usage
            foreach (var userId in _recentCommandUsage.Keys.ToList())
            {
                if (_recentCommandUsage.TryGetValue(userId, out var commands))
                {
                    // Check if all commands are older than the cutoff
                    bool allOld = true;
                    lock (commands)
                    {
                        allOld = !commands.Any() || commands.All(cmd => cmd.Timestamp < cutoff);
                    }

                    if (allOld)
                    {
                        _recentCommandUsage.TryRemove(userId, out _);
                    }
                }
            }
        }

        /// <summary>
        /// Checks if a user has hit the rate limit.
        /// </summary>
        /// <param name="userId">The ID of the user.</param>
        /// <param name="commandName">The name of the command being executed.</param>
        /// <returns>True if the user has hit the rate limit, false otherwise.</returns>
        public bool IsRateLimited(ulong userId, string commandName)
        {
            // Creator and owner bypass rate limits
            if (userId == 513793435807907841 || userId == OwnerId)
            {
                return false;
            }

            // Periodically clean up inactive users to prevent memory leaks
            CleanupInactiveUsers();

            // Check if the user is already rate limited
            if (_rateLimitedUsers.TryGetValue(userId, out DateTime rateLimitTime))
            {
                // If the rate limit was within the window, they're still rate limited
                if (DateTime.UtcNow.Subtract(rateLimitTime).TotalSeconds < RateLimitWindowSeconds)
                {
                    return true;
                }
                else
                {
                    // Rate limit expired, remove them from the dictionary
                    _rateLimitedUsers.TryRemove(userId, out _);
                }
            }

            // Get the list of recent commands for this user
            if (!_recentCommandUsage.TryGetValue(userId, out var userCommands))
            {
                return false; // No recent commands, not rate limited
            }

            lock (userCommands)
            {
                // Remove expired entries
                DateTime cutoff = DateTime.UtcNow.AddSeconds(-RateLimitWindowSeconds);
                userCommands.RemoveAll(cmd => cmd.Timestamp < cutoff);

                // Check for same command spam (same command used too many times)
                int sameCommandCount = userCommands.Count(cmd => cmd.CommandName == commandName);
                if (sameCommandCount >= MaxSameCommandPerWindow)
                {
                    // Mark the user as rate limited
                    _rateLimitedUsers[userId] = DateTime.UtcNow;
                    return true; // Rate limited due to same command spam
                }

                // Check if this would be a new unique command
                bool commandAlreadyUsed = userCommands.Any(cmd => cmd.CommandName == commandName);
                if (commandAlreadyUsed)
                {
                    return false; // Not adding a new unique command, so not rate limited for different commands
                }

                // Check if the user has used too many different commands in the time window
                int uniqueCommandCount = userCommands.Select(cmd => cmd.CommandName).Distinct().Count();
                if (uniqueCommandCount >= MaxDifferentCommandsPerWindow)
                {
                    // Mark the user as rate limited
                    _rateLimitedUsers[userId] = DateTime.UtcNow;
                    return true;
                }

                return false;
            }
        }
    }
}
