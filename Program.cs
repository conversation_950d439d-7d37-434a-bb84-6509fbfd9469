﻿using Microsoft.Extensions.Configuration;
using BotChan.Models;
using BotChan.Services;

// This is the entry point of the Bot-Chan Discord bot application.
// The Program class is responsible for:
// 1. Loading configuration from appsettings.json
// 2. Creating and initializing the Discord bot client
// 3. Keeping the application running indefinitely

namespace Bot<PERSON>han
{
    /// <summary>
    /// The main program class that serves as the entry point for the application.
    /// </summary>
    public class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// This method initializes the bot and keeps it running.
        /// </summary>
        /// <param name="args">Command line arguments (not used)</param>
        /// <returns>A task representing the asynchronous operation</returns>
        // Flag to track if the bot is shutting down
        private static bool _isShuttingDown = false;

        // Reference to the bot client for shutdown handling
        private static DiscordBotClient? _botClient = null;

        // Path to the shutdown signal file
        private const string ShutdownSignalFile = "shutdown.signal";

        public static async Task Main(string[] args)
        {
            // Clear the console and display startup message
            Console.Clear();
            Console.WriteLine("Starting Bot-Chan...");

            // Delete any existing shutdown signal file
            if (File.Exists(ShutdownSignalFile))
            {
                try
                {
                    File.Delete(ShutdownSignalFile);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Warning: Could not delete existing shutdown signal file: {ex.Message}");
                }
            }

            // STEP 1: Load configuration from appsettings.json
            // The configuration contains the bot token and command prefix
            var config = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory()) // Use current directory as base path
                .AddJsonFile("appsettings.json") // Load settings from appsettings.json
                .Build();

            // STEP 2: Create the bot client with configuration
            _botClient = new DiscordBotClient(new BotConfig
            {
                // Get the Discord bot token from configuration
                // This is required to authenticate with Discord's API
                Token = config["BotConfiguration:Token"] ?? string.Empty,

                // Get the command prefix from configuration (default to "%" if not specified)
                // This is the character that users will type before commands (e.g., %help)
                Prefix = config["BotConfiguration:Prefix"] ?? "%",

                // Get the owner ID from configuration
                // The owner has full permissions for all commands
                // Replace this with your own Discord user ID
                OwnerId = ulong.TryParse(config["BotConfiguration:OwnerId"], out ulong ownerId) ? ownerId : 0
            });

            // Register console control handler for Ctrl+C and other shutdown events
            Console.CancelKeyPress += OnConsoleCancel;

            // Register process exit handler
            AppDomain.CurrentDomain.ProcessExit += OnProcessExit;

            // STEP 3: Configure slash command behavior for development
            // Disable daily limit to allow frequent global registration during development
            _botClient.ConfigureSlashCommandBehavior(
                skipGlobalRegistration: false,
                forceGlobalRegistration: false,
                removeGlobalOnShutdown: false,
                disableDailyLimit: true // Enable for development - allows multiple global registrations per day
            );

            // STEP 4: Start the bot
            // This connects to Discord and begins listening for commands
            await _botClient.StartAsync();
            Console.WriteLine("Bot initialization completed!");

            // STEP 5: Start a background task to check for shutdown signal
            _ = Task.Run(CheckForShutdownSignal);

            // STEP 6: Start console command handler
            Console.WriteLine("💡 Console Commands Available:");
            Console.WriteLine("   'global' - Force global slash command registration");
            Console.WriteLine("   'dm' - Force DM-compatible command registration");
            Console.WriteLine("   'exit' - Graceful shutdown");
            Console.WriteLine("   'help' - Show available console commands");

            _ = Task.Run(HandleConsoleCommands);

            // STEP 7: Keep the application running indefinitely
            // This prevents the application from exiting after initialization
            try
            {
                await Task.Delay(Timeout.Infinite);
            }
            catch (TaskCanceledException)
            {
                // This exception is thrown when the delay is canceled during shutdown
                // We can safely ignore it
            }
        }

        /// <summary>
        /// Handles console commands for bot management
        /// </summary>
        private static async Task HandleConsoleCommands()
        {
            while (!_isShuttingDown)
            {
                try
                {
                    string? input = Console.ReadLine();
                    if (string.IsNullOrWhiteSpace(input)) continue;

                    string command = input.Trim().ToLower();

                    switch (command)
                    {
                        case "global":
                            Console.WriteLine("🚀 Forcing global slash command registration...");
                            if (_botClient != null)
                            {
                                await _botClient.ForceGlobalCommandRegistration();
                            }
                            else
                            {
                                Console.WriteLine("❌ Bot client not available");
                            }
                            break;

                        case "dm":
                            Console.WriteLine("🚀 Forcing DM-compatible command registration...");
                            if (_botClient != null)
                            {
                                await _botClient.ForceGlobalDMCommandRegistration();
                            }
                            else
                            {
                                Console.WriteLine("❌ Bot client not available");
                            }
                            break;

                        case "exit":
                            Console.WriteLine("🛑 Initiating graceful shutdown...");
                            await GracefulShutdown();
                            return;

                        case "help":
                            Console.WriteLine("💡 Available Console Commands:");
                            Console.WriteLine("   'global' - Force global slash command registration");
                            Console.WriteLine("   'dm' - Force DM-compatible command registration");
                            Console.WriteLine("   'exit' - Graceful shutdown");
                            Console.WriteLine("   'help' - Show this help message");
                            break;

                        default:
                            Console.WriteLine($"❓ Unknown command: {input}");
                            Console.WriteLine("💡 Type 'help' for available commands");
                            break;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Error processing console command: {ex.Message}");
                }

                await Task.Delay(100); // Small delay to prevent tight loop
            }
        }

        /// <summary>
        /// Checks for the presence of a shutdown signal file and initiates graceful shutdown if found
        /// </summary>
        private static async Task CheckForShutdownSignal()
        {
            while (!_isShuttingDown)
            {
                if (File.Exists(ShutdownSignalFile))
                {
                    Console.WriteLine("Shutdown signal file detected. Initiating graceful shutdown...");
                    await GracefulShutdown();
                    break;
                }

                // Check every second
                await Task.Delay(1000);
            }
        }

        /// <summary>
        /// Handles Ctrl+C and other console cancellation events
        /// </summary>
        private static void OnConsoleCancel(object? sender, ConsoleCancelEventArgs e)
        {
            Console.WriteLine("Ctrl+C detected. Initiating graceful shutdown...");
            e.Cancel = true; // Prevent the process from terminating immediately
            _ = GracefulShutdown();
        }

        /// <summary>
        /// Handles process exit events
        /// </summary>
        private static void OnProcessExit(object? sender, EventArgs e)
        {
            if (!_isShuttingDown)
            {
                Console.WriteLine("Process exit detected. Saving data...");
                _botClient?.BotSystem.ForceSaveUserData();
            }
        }

        /// <summary>
        /// Performs a graceful shutdown of the bot
        /// </summary>
        private static async Task GracefulShutdown()
        {
            if (_isShuttingDown) return; // Prevent multiple shutdown attempts
            _isShuttingDown = true;

            Console.WriteLine("Performing graceful shutdown...");

            try
            {
                // Save all user data
                Console.WriteLine("Saving all user data...");
                _botClient?.BotSystem.ForceSaveUserData();

                // Stop the bot (this will remove slash commands and disconnect)
                if (_botClient != null)
                {
                    await _botClient.StopAsync();
                }

                // Delete the shutdown signal file if it exists
                if (File.Exists(ShutdownSignalFile))
                {
                    try
                    {
                        File.Delete(ShutdownSignalFile);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Warning: Could not delete shutdown signal file: {ex.Message}");
                    }
                }

                Console.WriteLine("Graceful shutdown completed. Exiting...");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during graceful shutdown: {ex.Message}");
            }

            // Exit the application
            Environment.Exit(0);
        }
    }
}
