using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;
using System.Text;
using System.Text.RegularExpressions;

namespace BotChan.Commands
{
    /// <summary>
    /// EditConsumablesCommand - A command that allows staff to manage consumables
    /// Usage: %editconsumables
    /// Example: %editconsumables
    /// </summary>
    public class EditConsumablesCommand : Command
    {
        // Number of items to display per page
        private const int ItemsPerPage = 7;

        // Override the default category
        public override string Category => "Admin";

        // Override the default usage
        public override string Usage => "%editconsumables\n%editconsumables add [id] [name] [description] [multiplier] [cost] [uses] [emoji]\n%editconsumables edit [id] [name] [description] [multiplier] [cost] [uses] [emoji]\n%editconsumables remove [id]\n%editconsumables page [number]";

        // Override the default examples
        public override string Examples => "%editconsumables\n%editconsumables add super_boost \"Super Boost\" \"Gives a huge boost to bot recruitment\" 20 200 1 💥\n%editconsumables edit basic_multiplier \"Basic Booster\" \"A basic bot multiplier\" 2 15 3 🌟\n%editconsumables remove advanced_multiplier\n%editconsumables page 2";

        public EditConsumablesCommand() : base("editconsumables", "Manage consumables (staff only)")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Check if the user is a staff member
            if (!client.Permissions.IsStaffUser(message.Author.Id))
            {
                await message.Channel.SendMessageAsync("You don't have permission to use this command.");
                return;
            }

            // If no arguments, show the list of consumables
            if (args.Length == 1)
            {
                await ShowConsumables(message, 1, client);
                return;
            }

            // Handle subcommands
            string subCommand = args[1].ToLower();

            switch (subCommand)
            {
                case "add":
                    await AddConsumable(message, args, client);
                    break;

                case "edit":
                    await EditConsumable(message, args, client);
                    break;

                case "remove":
                    await RemoveConsumable(message, args, client);
                    break;

                case "page":
                    // Check if a page number was provided
                    if (args.Length < 3 || !int.TryParse(args[2], out int pageNumber))
                    {
                        await message.Channel.SendMessageAsync("Please specify a valid page number. Example: `%editconsumables page 2`");
                        return;
                    }

                    // Show the specified page
                    await ShowConsumables(message, pageNumber, client);
                    break;

                default:
                    await message.Channel.SendMessageAsync("Unknown subcommand. Available subcommands: `add`, `edit`, `remove`, `page`");
                    break;
            }
        }

        private async Task ShowConsumables(SocketMessage message, int page, DiscordBotClient client)
        {
            // Get all consumables
            var allConsumables = client.BotSystem.Config.AvailableConsumables;

            // Calculate total pages
            int totalPages = (int)Math.Ceiling(allConsumables.Count / (double)ItemsPerPage);
            if (totalPages == 0) totalPages = 1; // At least one page even if empty

            // Ensure page is within valid range
            page = Math.Max(1, Math.Min(page, totalPages));

            // Get consumables for the current page
            var pageConsumables = allConsumables
                .Skip((page - 1) * ItemsPerPage)
                .Take(ItemsPerPage)
                .ToList();

            // Build the consumables list
            var sb = new StringBuilder();

            // Add the header
            sb.AppendLine("%editconsumables add [id] [name] [description] [multiplier] [cost] [uses] [emoji]");
            sb.AppendLine("%editconsumables edit [id] [name] [description] [multiplier] [cost] [uses] [emoji]");
            sb.AppendLine("%editconsumables remove [id]");

            // Add navigation instructions if there are multiple pages
            if (totalPages > 1)
            {
                sb.AppendLine($"%editconsumables page [1-{totalPages}] to navigate pages");
            }

            sb.AppendLine("Here are all the available consumables:");
            sb.AppendLine("════════════════════════════════");

            // Add the consumables
            for (int i = 0; i < pageConsumables.Count; i++)
            {
                var consumable = pageConsumables[i];
                int displayId = (page - 1) * ItemsPerPage + i + 1; // Calculate the display ID
                string formattedPrice = FormatNumber(consumable.Cost);

                // Format: ID emoji name----------------------- price :BotDown:
                // Calculate dashes needed for alignment
                int dashCount = Math.Max(1, 40 - consumable.Name.Length - formattedPrice.Length);
                string dashes = new string('-', dashCount);

                sb.AppendLine($"{displayId} {consumable.Emoji} {consumable.Name}{dashes} {formattedPrice} {Constants.Emojis.BotDown}");
            }

            // Add a blank line at the end for spacing
            sb.AppendLine();

            // Create an embed for the consumables list
            var embed = new EmbedBuilder()
                .WithAuthor("Bot-Chan's Secret Research Facility", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                .WithDescription(sb.ToString())
                .WithColor(Color.Purple)
                .WithFooter($"Page {page}/{totalPages} |");

            // Send the embed message
            var consumablesMessage = await message.Channel.SendMessageAsync(embed: embed.Build());

            // No need to add reactions or handlers - using command-based pagination
        }

        private async Task AddConsumable(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Get the full command text
            string fullCommand = message.Content;

            // Parse the command using regex to handle quoted strings properly
            var matches = Regex.Matches(fullCommand, @"[\""].+?[\""]|[^ ]+");
            var parsedArgs = new List<string>();

            foreach (Match match in matches)
            {
                string arg = match.Value;
                // Remove quotes from quoted arguments
                if (arg.StartsWith("\"") && arg.EndsWith("\""))
                {
                    arg = arg.Substring(1, arg.Length - 2);
                }
                parsedArgs.Add(arg);
            }

            // Check if we have enough arguments
            if (parsedArgs.Count < 8)
            {
                await message.Channel.SendMessageAsync("Not enough arguments. Usage: `%editconsumables add [id] [name] [description] [multiplier] [cost] [uses] [emoji]`");
                return;
            }

            // Parse the arguments
            string id = parsedArgs[2];
            string name = parsedArgs[3];
            string description = parsedArgs[4];

            // Parse the numeric values
            if (!double.TryParse(parsedArgs[5], out double multiplier))
            {
                await message.Channel.SendMessageAsync($"Invalid multiplier value: '{parsedArgs[5]}'. Must be a number.");
                return;
            }

            if (!int.TryParse(parsedArgs[6], out int cost))
            {
                await message.Channel.SendMessageAsync($"Invalid cost value: '{parsedArgs[6]}'. Must be a number.");
                return;
            }

            if (!int.TryParse(parsedArgs[7], out int uses))
            {
                await message.Channel.SendMessageAsync($"Invalid uses value: '{parsedArgs[7]}'. Must be a number.");
                return;
            }

            // Get the emoji
            string emoji = parsedArgs.Count > 8 ? parsedArgs[8] : "🔮";

            // Create the consumable
            var consumable = new Consumable
            {
                Id = id,
                Name = name,
                Description = description,
                Multiplier = multiplier,
                Cost = cost,
                DefaultUses = uses,
                Emoji = emoji
            };

            // Add the consumable
            bool success = client.BotSystem.AddConsumable(consumable);

            if (success)
            {
                // Create an embed for the response
                var embed = new EmbedBuilder()
                    .WithAuthor("Bot-Chan's Secret Research Facility", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                    .WithTitle("Consumable Added")
                    .WithDescription($"**{name}** {emoji} has been added to the shop!\n\n" +
                                    $"• ID: `{id}`\n" +
                                    $"• Description: {description}\n" +
                                    $"• Multiplier: **{multiplier}x**\n" +
                                    $"• Uses: **{uses}**\n" +
                                    $"• Price: **{cost}** {Constants.Emojis.BotDown}")
                    .WithColor(Color.Green)
                    .WithFooter("Use %shop to see all available consumables");

                await message.Channel.SendMessageAsync(embed: embed.Build());
            }
            else
            {
                await message.Channel.SendMessageAsync($"Error: A consumable with ID `{id}` already exists.");
            }
        }

        private async Task EditConsumable(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Get the full command text
            string fullCommand = message.Content;

            // Parse the command using regex to handle quoted strings properly
            var matches = Regex.Matches(fullCommand, @"[\""].+?[\""]|[^ ]+");
            var parsedArgs = new List<string>();

            foreach (Match match in matches)
            {
                string arg = match.Value;
                // Remove quotes from quoted arguments
                if (arg.StartsWith("\"") && arg.EndsWith("\""))
                {
                    arg = arg.Substring(1, arg.Length - 2);
                }
                parsedArgs.Add(arg);
            }

            // Check if we have enough arguments
            if (parsedArgs.Count < 8)
            {
                await message.Channel.SendMessageAsync("Not enough arguments. Usage: `%editconsumables edit [id] [name] [description] [multiplier] [cost] [uses] [emoji]`");
                return;
            }

            // Parse the arguments
            string id = parsedArgs[2];
            string name = parsedArgs[3];
            string description = parsedArgs[4];

            // Parse the numeric values
            if (!double.TryParse(parsedArgs[5], out double multiplier))
            {
                await message.Channel.SendMessageAsync($"Invalid multiplier value: '{parsedArgs[5]}'. Must be a number.");
                return;
            }

            if (!int.TryParse(parsedArgs[6], out int cost))
            {
                await message.Channel.SendMessageAsync($"Invalid cost value: '{parsedArgs[6]}'. Must be a number.");
                return;
            }

            if (!int.TryParse(parsedArgs[7], out int uses))
            {
                await message.Channel.SendMessageAsync($"Invalid uses value: '{parsedArgs[7]}'. Must be a number.");
                return;
            }

            // Get the emoji
            string emoji = parsedArgs.Count > 8 ? parsedArgs[8] : "🔮";

            // Create the consumable
            var consumable = new Consumable
            {
                Id = id,
                Name = name,
                Description = description,
                Multiplier = multiplier,
                Cost = cost,
                DefaultUses = uses,
                Emoji = emoji
            };

            // Update the consumable
            bool success = client.BotSystem.UpdateConsumable(consumable);

            if (success)
            {
                // Create an embed for the response
                var embed = new EmbedBuilder()
                    .WithAuthor("Bot-Chan's Secret Research Facility", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                    .WithTitle("Consumable Updated")
                    .WithDescription($"**{name}** {emoji} has been updated!\n\n" +
                                    $"• ID: `{id}`\n" +
                                    $"• Description: {description}\n" +
                                    $"• Multiplier: **{multiplier}x**\n" +
                                    $"• Uses: **{uses}**\n" +
                                    $"• Price: **{cost}** {Constants.Emojis.BotDown}")
                    .WithColor(Color.Blue)
                    .WithFooter("Use %shop to see all available consumables");

                await message.Channel.SendMessageAsync(embed: embed.Build());
            }
            else
            {
                await message.Channel.SendMessageAsync($"Error: No consumable with ID `{id}` found.");
            }
        }

        private async Task RemoveConsumable(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Check if we have enough arguments
            if (args.Length < 3)
            {
                await message.Channel.SendMessageAsync("Not enough arguments. Usage: `%editconsumables remove [id]`");
                return;
            }

            // Parse the arguments
            string id = args[2];

            // Get the consumable before removing it
            var consumable = client.BotSystem.GetConsumable(id);
            if (consumable == null)
            {
                await message.Channel.SendMessageAsync($"Error: No consumable with ID `{id}` found.");
                return;
            }

            // Remove the consumable
            bool success = client.BotSystem.RemoveConsumable(id);

            if (success)
            {
                // Create an embed for the response
                var embed = new EmbedBuilder()
                    .WithAuthor("Bot-Chan's Secret Research Facility", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                    .WithTitle("Consumable Removed")
                    .WithDescription($"**{consumable.Name}** {consumable.Emoji} has been removed from the shop.")
                    .WithColor(Color.Red)
                    .WithFooter("Use %shop to see all available consumables");

                await message.Channel.SendMessageAsync(embed: embed.Build());
            }
            else
            {
                await message.Channel.SendMessageAsync($"Error: Failed to remove consumable with ID `{id}`.");
            }
        }

        /// <summary>
        /// Formats a number with K, M, B suffixes for thousands, millions, billions
        /// </summary>
        private string FormatNumber(int number)
        {
            if (number >= 1_000_000_000)
                return $"{number / 1_000_000_000}B";
            if (number >= 1_000_000)
                return $"{number / 1_000_000}M";
            if (number >= 1_000)
                return $"{number / 1_000}K";
            return number.ToString();
        }
    }
}
