using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;
using System.Text;

namespace BotChan.Commands
{
    /// <summary>
    /// ChangePermsCommand - Allows the Creator and bot owner to manage command permissions
    /// Usage: %changeperms
    /// Example: %changeperms
    /// </summary>
    public class ChangePermsCommand : Command
    {
        // Override the default category
        public override string Category => "Admin";

        // Override the default usage
        public override string Usage => "%changeperms";

        // Override the default examples
        public override string Examples => "%changeperms";

        // Dictionary to track active permission menus
        private static readonly Dictionary<ulong, PermissionMenuState> _activeMenus = new();

        // Dictionary to track message handlers for each user
        private static readonly Dictionary<ulong, Func<SocketMessage, Task>> _messageHandlers = new();

        // List of available slash commands for permission management
        private static readonly List<string> _slashCommands = new()
        {
            "bomb", "bots", "daily", "declarewar", "dismantle", "help", "info",
            "inventory", "leaderboards", "ping", "recruit", "sell", "shop",
            "use", "usecode", "supportserver"
        };

        public ChangePermsCommand() : base("changeperms", "Manage command permissions (Creator and owner only)")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Only the creator and bot owner can use this command
            bool isCreator = message.Author.Id == 513793435807907841;
            bool isOwner = message.Author.Id == client.Config.OwnerId;

            if (!isCreator && !isOwner)
            {
                await message.Channel.SendMessageAsync("You don't have permission to use this command.");
                return;
            }

            // If the user already has an active menu, close it first
            if (_activeMenus.ContainsKey(message.Author.Id))
            {
                await CloseMenu(message.Author.Id, client);
                await message.Channel.SendMessageAsync("Previous menu closed.");
            }

            // Create a new menu state for this user
            var menuState = new PermissionMenuState();
            _activeMenus[message.Author.Id] = menuState;

            // Show the main menu
            await ShowMainMenu(message, client);

            // Create a message handler for this menu
            Func<SocketMessage, Task> handler = async (msg) =>
            {
                // Only process messages from the original user
                if (msg.Author.Id != message.Author.Id || msg.Author.IsBot)
                    return;

                // Check if the user has an active menu
                if (!_activeMenus.TryGetValue(msg.Author.Id, out var state))
                    return;

                // Check for menu timeout (2 minutes)
                if (DateTime.UtcNow - state.LastActivity > TimeSpan.FromMinutes(2))
                {
                    await CloseMenu(msg.Author.Id, client);
                    await msg.Channel.SendMessageAsync("Menu closed due to inactivity.");
                    return;
                }

                // Process the menu input
                await ProcessMenuInput(msg, state, client);
            };

            // Store the handler so we can remove it later
            _messageHandlers[message.Author.Id] = handler;

            // Register the handler
            client._client.MessageReceived += handler;
        }

        /// <summary>
        /// Shows the main permissions menu.
        /// </summary>
        private async Task ShowMainMenu(SocketMessage message, DiscordBotClient client)
        {
            var embed = new EmbedBuilder()
                .WithTitle("🔒 Permission Management")
                .WithDescription("Select an option by typing the corresponding number:")
                .WithColor(Color.Blue)
                .AddField("1️⃣ Command Permissions", "Set permission levels for % commands")
                .AddField("2️⃣ Staff Management", "Add or remove staff members")
                .AddField("3️⃣ Whitelist Management", "Manage whitelisted users for % commands")
                .AddField("4️⃣ Blacklist Management", "Manage blacklisted users for % commands")
                .AddField("5️⃣ Slash Command Whitelist", "Manage whitelisted users for / commands")
                .AddField("6️⃣ Slash Command Blacklist", "Manage blacklisted users for / commands")
                .AddField("7️⃣ Cooldown Management", "Set cooldown times for commands")
                .AddField("8️⃣ Rate Limit Management", "Configure rate limiting settings")
                .AddField("❌ Exit", "Type 'exit' to close this menu")
                .WithFooter("This menu will time out after 2 minutes of inactivity")
                .Build();

            await message.Channel.SendMessageAsync(embed: embed);

            // Update the menu state
            var state = _activeMenus[message.Author.Id];
            state.CurrentMenu = MenuType.Main;
            state.LastActivity = DateTime.UtcNow;
        }

        /// <summary>
        /// Shows the command permissions menu.
        /// </summary>
        private async Task ShowCommandPermissionsMenu(SocketMessage message, DiscordBotClient client)
        {
            var state = _activeMenus[message.Author.Id];
            var allCommands = client._commands.Values.OrderBy(c => c.Name).ToList();

            // Calculate pagination
            int totalPages = (int)Math.Ceiling((double)allCommands.Count / state.ItemsPerPage);
            int startIndex = state.CurrentPage * state.ItemsPerPage;
            int endIndex = Math.Min(startIndex + state.ItemsPerPage, allCommands.Count);

            var embedBuilder = new EmbedBuilder()
                .WithTitle("🔒 Command Permissions")
                .WithDescription($"Select a command to modify its permission level:\n**Page {state.CurrentPage + 1} of {totalPages}**")
                .WithColor(Color.Blue);

            // Add commands for current page
            for (int i = startIndex; i < endIndex; i++)
            {
                var command = allCommands[i];
                var permLevel = client.Permissions.GetCommandPermissionLevel(command.Name);
                int displayIndex = i + 1;
                embedBuilder.AddField($"{displayIndex}. {command.Name}", $"Current level: {permLevel}");
            }

            // Add navigation options
            var navigationText = new StringBuilder();
            if (state.CurrentPage > 0)
                navigationText.AppendLine("⬅️ Type 'prev' for previous page");
            if (state.CurrentPage < totalPages - 1)
                navigationText.AppendLine("➡️ Type 'next' for next page");

            if (navigationText.Length > 0)
                embedBuilder.AddField("📄 Navigation", navigationText.ToString().Trim());

            embedBuilder.AddField("� Back", "Type 'back' to return to the main menu")
                .AddField("❌ Exit", "Type 'exit' to close this menu")
                .WithFooter($"This menu will time out after 2 minutes of inactivity • Page {state.CurrentPage + 1}/{totalPages}");

            await message.Channel.SendMessageAsync(embed: embedBuilder.Build());

            // Update the menu state
            state.CurrentMenu = MenuType.CommandPermissions;
            state.CommandList = allCommands;
            state.LastActivity = DateTime.UtcNow;
        }

        /// <summary>
        /// Shows the permission level selection menu for a command.
        /// </summary>
        private async Task ShowPermissionLevelMenu(SocketMessage message, Command command, DiscordBotClient client)
        {
            var currentLevel = client.Permissions.GetCommandPermissionLevel(command.Name);

            var embed = new EmbedBuilder()
                .WithTitle($"🔒 Permission Level for {command.Name}")
                .WithDescription($"Current level: {currentLevel}\n\nSelect a new permission level:")
                .WithColor(Color.Blue)
                .AddField("1️⃣ Everyone", "Anyone can use this command")
                .AddField("2️⃣ Whitelist", "Only whitelisted users can use this command")
                .AddField("3️⃣ Staff", "Only staff members can use this command")
                .AddField("4️⃣ Disabled", "No one can use this command (except the owner)")
                .AddField("5️⃣ Creator", "Only the creator (513793435807907841) can use this command")
                .AddField("🔙 Back", "Type 'back' to return to the command list")
                .AddField("❌ Exit", "Type 'exit' to close this menu")
                .WithFooter("This menu will time out after 2 minutes of inactivity")
                .Build();

            await message.Channel.SendMessageAsync(embed: embed);

            // Update the menu state
            var state = _activeMenus[message.Author.Id];
            state.CurrentMenu = MenuType.PermissionLevel;
            state.SelectedCommand = command;
            state.LastActivity = DateTime.UtcNow;
        }

        /// <summary>
        /// Shows the staff management menu.
        /// </summary>
        private async Task ShowStaffMenu(SocketMessage message, DiscordBotClient client)
        {
            var staffUsers = client.Permissions.CurrentPermissions.StaffUsers;

            var embedBuilder = new EmbedBuilder()
                .WithTitle("👑 Staff Management")
                .WithDescription("Manage staff members who have elevated permissions.")
                .WithColor(Color.Gold);

            // Add current staff members
            var staffList = new StringBuilder();
            if (staffUsers.Count > 0)
            {
                foreach (var userId in staffUsers)
                {
                    staffList.AppendLine($"• <@{userId}> (ID: {userId})");
                }
            }
            else
            {
                staffList.AppendLine("No staff members added yet.");
            }

            embedBuilder.AddField("Current Staff Members", staffList.ToString())
                .AddField("Commands",
                    "**add @user** - Add a user to staff\n" +
                    "**remove @user** - Remove a user from staff\n" +
                    "**back** - Return to main menu\n" +
                    "**exit** - Close this menu")
                .WithFooter("This menu will time out after 2 minutes of inactivity");

            await message.Channel.SendMessageAsync(embed: embedBuilder.Build());

            // Update the menu state
            var state = _activeMenus[message.Author.Id];
            state.CurrentMenu = MenuType.StaffManagement;
            state.LastActivity = DateTime.UtcNow;
        }

        /// <summary>
        /// Shows the whitelist management menu.
        /// </summary>
        private async Task ShowWhitelistMenu(SocketMessage message, DiscordBotClient client)
        {
            var embedBuilder = new EmbedBuilder()
                .WithTitle("📋 Whitelist Management")
                .WithDescription("Select a command to manage its whitelist:")
                .WithColor(Color.Green);

            // Add all commands to the embed
            int index = 1;
            foreach (var command in client._commands.Values.OrderBy(c => c.Name))
            {
                var whitelistCount = client.Permissions.CurrentPermissions.WhitelistedUsers.TryGetValue(command.Name, out var users)
                    ? users.Count
                    : 0;

                embedBuilder.AddField($"{index}. {command.Name}", $"Whitelisted users: {whitelistCount}");
                index++;
            }

            embedBuilder.AddField("🔙 Back", "Type 'back' to return to the main menu")
                .AddField("❌ Exit", "Type 'exit' to close this menu")
                .WithFooter("This menu will time out after 2 minutes of inactivity");

            await message.Channel.SendMessageAsync(embed: embedBuilder.Build());

            // Update the menu state
            var state = _activeMenus[message.Author.Id];
            state.CurrentMenu = MenuType.WhitelistManagement;
            state.CommandList = client._commands.Values.OrderBy(c => c.Name).ToList();
            state.LastActivity = DateTime.UtcNow;
        }

        /// <summary>
        /// Shows the whitelist for a specific command.
        /// </summary>
        private async Task ShowCommandWhitelist(SocketMessage message, Command command, DiscordBotClient client)
        {
            var whitelistedUsers = client.Permissions.CurrentPermissions.WhitelistedUsers.TryGetValue(command.Name, out var users)
                ? users
                : new List<ulong>();

            var embedBuilder = new EmbedBuilder()
                .WithTitle($"📋 Whitelist for {command.Name}")
                .WithDescription("Manage whitelisted users for this command.")
                .WithColor(Color.Green);

            // Add current whitelisted users
            var userList = new StringBuilder();
            if (whitelistedUsers.Count > 0)
            {
                foreach (var userId in whitelistedUsers)
                {
                    userList.AppendLine($"• <@{userId}> (ID: {userId})");
                }
            }
            else
            {
                userList.AppendLine("No users whitelisted for this command.");
            }

            embedBuilder.AddField("Whitelisted Users", userList.ToString())
                .AddField("Commands",
                    "**add @user** - Add a user to the whitelist\n" +
                    "**remove @user** - Remove a user from the whitelist\n" +
                    "**back** - Return to whitelist menu\n" +
                    "**exit** - Close this menu")
                .WithFooter("This menu will time out after 2 minutes of inactivity");

            await message.Channel.SendMessageAsync(embed: embedBuilder.Build());

            // Update the menu state
            var state = _activeMenus[message.Author.Id];
            state.CurrentMenu = MenuType.CommandWhitelist;
            state.SelectedCommand = command;
            state.LastActivity = DateTime.UtcNow;
        }

        /// <summary>
        /// Shows the blacklist management menu.
        /// </summary>
        private async Task ShowBlacklistMenu(SocketMessage message, DiscordBotClient client)
        {
            var embedBuilder = new EmbedBuilder()
                .WithTitle("⛔ Blacklist Management")
                .WithDescription("Select a command to manage its blacklist:")
                .WithColor(Color.Red);

            // Add all commands to the embed
            int index = 1;
            foreach (var command in client._commands.Values.OrderBy(c => c.Name))
            {
                var blacklistCount = client.Permissions.CurrentPermissions.BlacklistedUsers.TryGetValue(command.Name, out var users)
                    ? users.Count
                    : 0;

                embedBuilder.AddField($"{index}. {command.Name}", $"Blacklisted users: {blacklistCount}");
                index++;
            }

            embedBuilder.AddField("🔙 Back", "Type 'back' to return to the main menu")
                .AddField("❌ Exit", "Type 'exit' to close this menu")
                .WithFooter("This menu will time out after 2 minutes of inactivity");

            await message.Channel.SendMessageAsync(embed: embedBuilder.Build());

            // Update the menu state
            var state = _activeMenus[message.Author.Id];
            state.CurrentMenu = MenuType.BlacklistManagement;
            state.CommandList = client._commands.Values.OrderBy(c => c.Name).ToList();
            state.LastActivity = DateTime.UtcNow;
        }

        /// <summary>
        /// Shows the blacklist for a specific command.
        /// </summary>
        private async Task ShowCommandBlacklist(SocketMessage message, Command command, DiscordBotClient client)
        {
            var blacklistedUsers = client.Permissions.CurrentPermissions.BlacklistedUsers.TryGetValue(command.Name, out var users)
                ? users
                : new List<ulong>();

            var embedBuilder = new EmbedBuilder()
                .WithTitle($"⛔ Blacklist for {command.Name}")
                .WithDescription("Manage blacklisted users for this command.")
                .WithColor(Color.Red);

            // Add current blacklisted users
            var userList = new StringBuilder();
            if (blacklistedUsers.Count > 0)
            {
                foreach (var userId in blacklistedUsers)
                {
                    userList.AppendLine($"• <@{userId}> (ID: {userId})");
                }
            }
            else
            {
                userList.AppendLine("No users blacklisted for this command.");
            }

            embedBuilder.AddField("Blacklisted Users", userList.ToString())
                .AddField("Commands",
                    "**add @user** - Add a user to the blacklist\n" +
                    "**remove @user** - Remove a user from the blacklist\n" +
                    "**back** - Return to blacklist menu\n" +
                    "**exit** - Close this menu")
                .WithFooter("This menu will time out after 2 minutes of inactivity");

            await message.Channel.SendMessageAsync(embed: embedBuilder.Build());

            // Update the menu state
            var state = _activeMenus[message.Author.Id];
            state.CurrentMenu = MenuType.CommandBlacklist;
            state.SelectedCommand = command;
            state.LastActivity = DateTime.UtcNow;
        }

        /// <summary>
        /// Shows the slash command whitelist management menu.
        /// </summary>
        private async Task ShowSlashWhitelistMenu(SocketMessage message, DiscordBotClient client)
        {
            var embedBuilder = new EmbedBuilder()
                .WithTitle("📋 Slash Command Whitelist Management")
                .WithDescription("Select a slash command to manage its whitelist:")
                .WithColor(Color.Green);

            // Add all slash commands to the embed
            int index = 1;
            foreach (var commandName in _slashCommands.OrderBy(c => c))
            {
                var whitelistCount = client.Permissions.CurrentPermissions.WhitelistedUsers.TryGetValue(commandName, out var users)
                    ? users.Count
                    : 0;

                embedBuilder.AddField($"{index}. /{commandName}", $"Whitelisted users: {whitelistCount}");
                index++;
            }

            embedBuilder.AddField("🔙 Back", "Type 'back' to return to the main menu")
                .AddField("❌ Exit", "Type 'exit' to close this menu")
                .WithFooter("This menu will time out after 2 minutes of inactivity");

            await message.Channel.SendMessageAsync(embed: embedBuilder.Build());

            // Update the menu state
            var state = _activeMenus[message.Author.Id];
            state.CurrentMenu = MenuType.SlashWhitelistManagement;
            state.SlashCommandList = _slashCommands.OrderBy(c => c).ToList();
            state.LastActivity = DateTime.UtcNow;
        }

        /// <summary>
        /// Shows the slash command blacklist management menu.
        /// </summary>
        private async Task ShowSlashBlacklistMenu(SocketMessage message, DiscordBotClient client)
        {
            var embedBuilder = new EmbedBuilder()
                .WithTitle("⛔ Slash Command Blacklist Management")
                .WithDescription("Select a slash command to manage its blacklist:")
                .WithColor(Color.Red);

            // Add all slash commands to the embed
            int index = 1;
            foreach (var commandName in _slashCommands.OrderBy(c => c))
            {
                var blacklistCount = client.Permissions.CurrentPermissions.BlacklistedUsers.TryGetValue(commandName, out var users)
                    ? users.Count
                    : 0;

                embedBuilder.AddField($"{index}. /{commandName}", $"Blacklisted users: {blacklistCount}");
                index++;
            }

            embedBuilder.AddField("🔙 Back", "Type 'back' to return to the main menu")
                .AddField("❌ Exit", "Type 'exit' to close this menu")
                .WithFooter("This menu will time out after 2 minutes of inactivity");

            await message.Channel.SendMessageAsync(embed: embedBuilder.Build());

            // Update the menu state
            var state = _activeMenus[message.Author.Id];
            state.CurrentMenu = MenuType.SlashBlacklistManagement;
            state.SlashCommandList = _slashCommands.OrderBy(c => c).ToList();
            state.LastActivity = DateTime.UtcNow;
        }

        /// <summary>
        /// Processes user input for the permission menus.
        /// </summary>
        private async Task ProcessMenuInput(SocketMessage message, PermissionMenuState state, DiscordBotClient client)
        {
            // Update the last activity time
            state.LastActivity = DateTime.UtcNow;

            // Check for exit command
            if (message.Content.Equals("exit", StringComparison.OrdinalIgnoreCase))
            {
                await CloseMenu(message.Author.Id, client);
                await message.Channel.SendMessageAsync("Permission management closed.");
                return;
            }

            // Check for back command
            if (message.Content.Equals("back", StringComparison.OrdinalIgnoreCase))
            {
                switch (state.CurrentMenu)
                {
                    case MenuType.CommandPermissions:
                    case MenuType.StaffManagement:
                    case MenuType.WhitelistManagement:
                    case MenuType.BlacklistManagement:
                    case MenuType.SlashWhitelistManagement:
                    case MenuType.SlashBlacklistManagement:
                    case MenuType.CooldownManagement:
                    case MenuType.RateLimitManagement:
                        await ShowMainMenu(message, client);
                        break;
                    case MenuType.PermissionLevel:
                        await ShowCommandPermissionsMenu(message, client);
                        break;
                    case MenuType.CommandWhitelist:
                        await ShowWhitelistMenu(message, client);
                        break;
                    case MenuType.CommandBlacklist:
                        await ShowBlacklistMenu(message, client);
                        break;
                    case MenuType.SlashCommandWhitelist:
                        await ShowSlashWhitelistMenu(message, client);
                        break;
                    case MenuType.SlashCommandBlacklist:
                        await ShowSlashBlacklistMenu(message, client);
                        break;
                    case MenuType.CommandCooldown:
                        await ShowCooldownMenu(message, client);
                        break;
                }
                return;
            }

            // Process menu-specific input
            switch (state.CurrentMenu)
            {
                case MenuType.Main:
                    await ProcessMainMenuInput(message, client);
                    break;
                case MenuType.CommandPermissions:
                    await ProcessCommandPermissionsInput(message, state, client);
                    break;
                case MenuType.PermissionLevel:
                    await ProcessPermissionLevelInput(message, state, client);
                    break;
                case MenuType.StaffManagement:
                    await ProcessStaffManagementInput(message, client);
                    break;
                case MenuType.WhitelistManagement:
                    await ProcessWhitelistManagementInput(message, state, client);
                    break;
                case MenuType.CommandWhitelist:
                    await ProcessCommandWhitelistInput(message, state, client);
                    break;
                case MenuType.BlacklistManagement:
                    await ProcessBlacklistManagementInput(message, state, client);
                    break;
                case MenuType.CommandBlacklist:
                    await ProcessCommandBlacklistInput(message, state, client);
                    break;
                case MenuType.SlashWhitelistManagement:
                    await ProcessSlashWhitelistManagementInput(message, state, client);
                    break;
                case MenuType.SlashCommandWhitelist:
                    await ProcessSlashCommandWhitelistInput(message, state, client);
                    break;
                case MenuType.SlashBlacklistManagement:
                    await ProcessSlashBlacklistManagementInput(message, state, client);
                    break;
                case MenuType.SlashCommandBlacklist:
                    await ProcessSlashCommandBlacklistInput(message, state, client);
                    break;
                case MenuType.CooldownManagement:
                    await ProcessCooldownManagementInput(message, state, client);
                    break;
                case MenuType.CommandCooldown:
                    await ProcessCommandCooldownInput(message, state, client);
                    break;
                case MenuType.RateLimitManagement:
                    await ProcessRateLimitManagementInput(message, state, client);
                    break;
            }
        }

        /// <summary>
        /// Processes input for the main menu.
        /// </summary>
        private async Task ProcessMainMenuInput(SocketMessage message, DiscordBotClient client)
        {
            switch (message.Content)
            {
                case "1":
                    // Reset to first page when entering command permissions menu
                    var state = _activeMenus[message.Author.Id];
                    state.CurrentPage = 0;
                    await ShowCommandPermissionsMenu(message, client);
                    break;
                case "2":
                    await ShowStaffMenu(message, client);
                    break;
                case "3":
                    await ShowWhitelistMenu(message, client);
                    break;
                case "4":
                    await ShowBlacklistMenu(message, client);
                    break;
                case "5":
                    await ShowSlashWhitelistMenu(message, client);
                    break;
                case "6":
                    await ShowSlashBlacklistMenu(message, client);
                    break;
                case "7":
                    // Reset to first page when entering cooldown management
                    var cooldownState = _activeMenus[message.Author.Id];
                    cooldownState.CurrentPage = 0;
                    await ShowCooldownMenu(message, client);
                    break;
                case "8":
                    await ShowRateLimitMenu(message, client);
                    break;
                default:
                    await message.Channel.SendMessageAsync("Invalid option. Please select a number from 1-6, or type 'exit' to close the menu.");
                    break;
            }
        }

        /// <summary>
        /// Processes input for the command permissions menu with pagination support.
        /// </summary>
        private async Task ProcessCommandPermissionsInput(SocketMessage message, PermissionMenuState state, DiscordBotClient client)
        {
            string input = message.Content.ToLower();

            // Handle navigation
            if (input == "next")
            {
                int totalPages = (int)Math.Ceiling((double)state.CommandList.Count / state.ItemsPerPage);
                if (state.CurrentPage < totalPages - 1)
                {
                    state.CurrentPage++;
                    await ShowCommandPermissionsMenu(message, client);
                }
                else
                {
                    await message.Channel.SendMessageAsync("You're already on the last page.");
                }
                return;
            }

            if (input == "prev" || input == "previous")
            {
                if (state.CurrentPage > 0)
                {
                    state.CurrentPage--;
                    await ShowCommandPermissionsMenu(message, client);
                }
                else
                {
                    await message.Channel.SendMessageAsync("You're already on the first page.");
                }
                return;
            }

            // Handle command selection
            if (int.TryParse(message.Content, out int index) && index > 0 && index <= state.CommandList.Count)
            {
                var command = state.CommandList[index - 1];
                await ShowPermissionLevelMenu(message, command, client);
            }
            else
            {
                await message.Channel.SendMessageAsync("Invalid option. Please select a valid command number, type 'next'/'prev' for navigation, or 'back' to return to the main menu.");
            }
        }

        /// <summary>
        /// Processes input for the permission level menu.
        /// </summary>
        private async Task ProcessPermissionLevelInput(SocketMessage message, PermissionMenuState state, DiscordBotClient client)
        {
            if (state.SelectedCommand == null)
            {
                await message.Channel.SendMessageAsync("Error: No command selected.");
                await ShowCommandPermissionsMenu(message, client);
                return;
            }

            PermissionLevel level;
            // Check if the user is the creator before allowing Creator permission level
            bool isCreator = message.Author.Id == 513793435807907841;

            switch (message.Content)
            {
                case "1":
                    level = PermissionLevel.Everyone;
                    break;
                case "2":
                    level = PermissionLevel.Whitelist;
                    break;
                case "3":
                    level = PermissionLevel.Staff;
                    break;
                case "4":
                    level = PermissionLevel.Disabled;
                    break;
                case "5":
                    // Only the creator can set Creator permission level
                    if (isCreator)
                    {
                        level = PermissionLevel.Creator;
                    }
                    else
                    {
                        await message.Channel.SendMessageAsync("You don't have permission to set the Creator permission level.");
                        return;
                    }
                    break;
                default:
                    await message.Channel.SendMessageAsync("Invalid option. Please select a number from 1-5, or type 'back' to return to the command list.");
                    return;
            }

            // Set the permission level
            client.Permissions.SetCommandPermission(state.SelectedCommand.Name, level);
            await message.Channel.SendMessageAsync($"Permission level for {state.SelectedCommand.Name} set to {level}.");

            // Return to the command list
            await ShowCommandPermissionsMenu(message, client);
        }

        /// <summary>
        /// Processes input for the staff management menu.
        /// </summary>
        private async Task ProcessStaffManagementInput(SocketMessage message, DiscordBotClient client)
        {
            string[] parts = message.Content.Split(' ', 2);
            string command = parts[0].ToLower();

            if (command == "add" || command == "remove")
            {
                // Check if a user was mentioned
                if (message.MentionedUsers.Count == 0)
                {
                    await message.Channel.SendMessageAsync("Please mention a user to add or remove.");
                    return;
                }

                var user = message.MentionedUsers.First();

                if (command == "add")
                {
                    if (client.Permissions.AddStaffUser(user.Id))
                    {
                        await message.Channel.SendMessageAsync($"Added {user.Username} to staff.");
                    }
                    else
                    {
                        await message.Channel.SendMessageAsync($"{user.Username} is already a staff member.");
                    }
                }
                else // remove
                {
                    if (client.Permissions.RemoveStaffUser(user.Id))
                    {
                        await message.Channel.SendMessageAsync($"Removed {user.Username} from staff.");
                    }
                    else
                    {
                        await message.Channel.SendMessageAsync($"{user.Username} is not a staff member.");
                    }
                }

                // Refresh the staff menu
                await ShowStaffMenu(message, client);
            }
            else
            {
                await message.Channel.SendMessageAsync("Invalid command. Use 'add @user', 'remove @user', 'back', or 'exit'.");
            }
        }

        /// <summary>
        /// Processes input for the whitelist management menu.
        /// </summary>
        private async Task ProcessWhitelistManagementInput(SocketMessage message, PermissionMenuState state, DiscordBotClient client)
        {
            if (int.TryParse(message.Content, out int index) && index > 0 && index <= state.CommandList.Count)
            {
                var command = state.CommandList[index - 1];
                await ShowCommandWhitelist(message, command, client);
            }
            else
            {
                await message.Channel.SendMessageAsync("Invalid option. Please select a valid command number, or type 'back' to return to the main menu.");
            }
        }

        /// <summary>
        /// Processes input for the command whitelist menu.
        /// </summary>
        private async Task ProcessCommandWhitelistInput(SocketMessage message, PermissionMenuState state, DiscordBotClient client)
        {
            if (state.SelectedCommand == null)
            {
                await message.Channel.SendMessageAsync("Error: No command selected.");
                await ShowWhitelistMenu(message, client);
                return;
            }

            string[] parts = message.Content.Split(' ', 2);
            string command = parts[0].ToLower();

            if (command == "add" || command == "remove")
            {
                // Check if a user was mentioned
                if (message.MentionedUsers.Count == 0)
                {
                    await message.Channel.SendMessageAsync("Please mention a user to add or remove.");
                    return;
                }

                var user = message.MentionedUsers.First();

                if (command == "add")
                {
                    if (client.Permissions.AddWhitelistedUser(state.SelectedCommand.Name, user.Id))
                    {
                        await message.Channel.SendMessageAsync($"Added {user.Username} to the whitelist for {state.SelectedCommand.Name}.");
                    }
                    else
                    {
                        await message.Channel.SendMessageAsync($"{user.Username} is already whitelisted for {state.SelectedCommand.Name}.");
                    }
                }
                else // remove
                {
                    if (client.Permissions.RemoveWhitelistedUser(state.SelectedCommand.Name, user.Id))
                    {
                        await message.Channel.SendMessageAsync($"Removed {user.Username} from the whitelist for {state.SelectedCommand.Name}.");
                    }
                    else
                    {
                        await message.Channel.SendMessageAsync($"{user.Username} is not whitelisted for {state.SelectedCommand.Name}.");
                    }
                }

                // Refresh the whitelist menu
                await ShowCommandWhitelist(message, state.SelectedCommand, client);
            }
            else
            {
                await message.Channel.SendMessageAsync("Invalid command. Use 'add @user', 'remove @user', 'back', or 'exit'.");
            }
        }

        /// <summary>
        /// Processes input for the blacklist management menu.
        /// </summary>
        private async Task ProcessBlacklistManagementInput(SocketMessage message, PermissionMenuState state, DiscordBotClient client)
        {
            if (int.TryParse(message.Content, out int index) && index > 0 && index <= state.CommandList.Count)
            {
                var command = state.CommandList[index - 1];
                await ShowCommandBlacklist(message, command, client);
            }
            else
            {
                await message.Channel.SendMessageAsync("Invalid option. Please select a valid command number, or type 'back' to return to the main menu.");
            }
        }

        /// <summary>
        /// Processes input for the command blacklist menu.
        /// </summary>
        private async Task ProcessCommandBlacklistInput(SocketMessage message, PermissionMenuState state, DiscordBotClient client)
        {
            if (state.SelectedCommand == null)
            {
                await message.Channel.SendMessageAsync("Error: No command selected.");
                await ShowBlacklistMenu(message, client);
                return;
            }

            string[] parts = message.Content.Split(' ', 2);
            string command = parts[0].ToLower();

            if (command == "add" || command == "remove")
            {
                // Check if a user was mentioned
                if (message.MentionedUsers.Count == 0)
                {
                    await message.Channel.SendMessageAsync("Please mention a user to add or remove.");
                    return;
                }

                var user = message.MentionedUsers.First();

                if (command == "add")
                {
                    if (client.Permissions.AddBlacklistedUser(state.SelectedCommand.Name, user.Id))
                    {
                        await message.Channel.SendMessageAsync($"Added {user.Username} to the blacklist for {state.SelectedCommand.Name}.");
                    }
                    else
                    {
                        await message.Channel.SendMessageAsync($"{user.Username} is already blacklisted for {state.SelectedCommand.Name}.");
                    }
                }
                else // remove
                {
                    if (client.Permissions.RemoveBlacklistedUser(state.SelectedCommand.Name, user.Id))
                    {
                        await message.Channel.SendMessageAsync($"Removed {user.Username} from the blacklist for {state.SelectedCommand.Name}.");
                    }
                    else
                    {
                        await message.Channel.SendMessageAsync($"{user.Username} is not blacklisted for {state.SelectedCommand.Name}.");
                    }
                }

                // Refresh the blacklist menu
                await ShowCommandBlacklist(message, state.SelectedCommand, client);
            }
            else
            {
                await message.Channel.SendMessageAsync("Invalid command. Use 'add @user', 'remove @user', 'back', or 'exit'.");
            }
        }

        /// <summary>
        /// Processes input for the slash whitelist management menu.
        /// </summary>
        private async Task ProcessSlashWhitelistManagementInput(SocketMessage message, PermissionMenuState state, DiscordBotClient client)
        {
            if (int.TryParse(message.Content, out int index) && index > 0 && index <= state.SlashCommandList.Count)
            {
                var commandName = state.SlashCommandList[index - 1];
                await ShowSlashCommandWhitelist(message, commandName, client);
            }
            else
            {
                await message.Channel.SendMessageAsync("Invalid option. Please select a valid command number, or type 'back' to return to the main menu.");
            }
        }

        /// <summary>
        /// Processes input for the slash command whitelist menu.
        /// </summary>
        private async Task ProcessSlashCommandWhitelistInput(SocketMessage message, PermissionMenuState state, DiscordBotClient client)
        {
            if (string.IsNullOrEmpty(state.SelectedSlashCommand))
            {
                await message.Channel.SendMessageAsync("Error: No slash command selected.");
                await ShowSlashWhitelistMenu(message, client);
                return;
            }

            string[] parts = message.Content.Split(' ', 2);
            string command = parts[0].ToLower();

            if ((command == "add" || command == "remove") && parts.Length == 2)
            {
                if (!message.MentionedUsers.Any())
                {
                    await message.Channel.SendMessageAsync("Please mention a user to add or remove.");
                    return;
                }

                var user = message.MentionedUsers.First();

                if (command == "add")
                {
                    if (client.Permissions.AddWhitelistedUser(state.SelectedSlashCommand, user.Id))
                    {
                        await message.Channel.SendMessageAsync($"Added {user.Username} to the whitelist for /{state.SelectedSlashCommand}.");
                    }
                    else
                    {
                        await message.Channel.SendMessageAsync($"{user.Username} is already whitelisted for /{state.SelectedSlashCommand}.");
                    }
                }
                else // remove
                {
                    if (client.Permissions.RemoveWhitelistedUser(state.SelectedSlashCommand, user.Id))
                    {
                        await message.Channel.SendMessageAsync($"Removed {user.Username} from the whitelist for /{state.SelectedSlashCommand}.");
                    }
                    else
                    {
                        await message.Channel.SendMessageAsync($"{user.Username} is not whitelisted for /{state.SelectedSlashCommand}.");
                    }
                }

                // Refresh the whitelist menu
                await ShowSlashCommandWhitelist(message, state.SelectedSlashCommand, client);
            }
            else
            {
                await message.Channel.SendMessageAsync("Invalid command. Use 'add @user', 'remove @user', 'back', or 'exit'.");
            }
        }

        /// <summary>
        /// Processes input for the slash blacklist management menu.
        /// </summary>
        private async Task ProcessSlashBlacklistManagementInput(SocketMessage message, PermissionMenuState state, DiscordBotClient client)
        {
            if (int.TryParse(message.Content, out int index) && index > 0 && index <= state.SlashCommandList.Count)
            {
                var commandName = state.SlashCommandList[index - 1];
                await ShowSlashCommandBlacklist(message, commandName, client);
            }
            else
            {
                await message.Channel.SendMessageAsync("Invalid option. Please select a valid command number, or type 'back' to return to the main menu.");
            }
        }

        /// <summary>
        /// Processes input for the slash command blacklist menu.
        /// </summary>
        private async Task ProcessSlashCommandBlacklistInput(SocketMessage message, PermissionMenuState state, DiscordBotClient client)
        {
            if (string.IsNullOrEmpty(state.SelectedSlashCommand))
            {
                await message.Channel.SendMessageAsync("Error: No slash command selected.");
                await ShowSlashBlacklistMenu(message, client);
                return;
            }

            string[] parts = message.Content.Split(' ', 2);
            string command = parts[0].ToLower();

            if ((command == "add" || command == "remove") && parts.Length == 2)
            {
                if (!message.MentionedUsers.Any())
                {
                    await message.Channel.SendMessageAsync("Please mention a user to add or remove.");
                    return;
                }

                var user = message.MentionedUsers.First();

                if (command == "add")
                {
                    if (client.Permissions.AddBlacklistedUser(state.SelectedSlashCommand, user.Id))
                    {
                        await message.Channel.SendMessageAsync($"Added {user.Username} to the blacklist for /{state.SelectedSlashCommand}.");
                    }
                    else
                    {
                        await message.Channel.SendMessageAsync($"{user.Username} is already blacklisted for /{state.SelectedSlashCommand}.");
                    }
                }
                else // remove
                {
                    if (client.Permissions.RemoveBlacklistedUser(state.SelectedSlashCommand, user.Id))
                    {
                        await message.Channel.SendMessageAsync($"Removed {user.Username} from the blacklist for /{state.SelectedSlashCommand}.");
                    }
                    else
                    {
                        await message.Channel.SendMessageAsync($"{user.Username} is not blacklisted for /{state.SelectedSlashCommand}.");
                    }
                }

                // Refresh the blacklist menu
                await ShowSlashCommandBlacklist(message, state.SelectedSlashCommand, client);
            }
            else
            {
                await message.Channel.SendMessageAsync("Invalid command. Use 'add @user', 'remove @user', 'back', or 'exit'.");
            }
        }

        /// <summary>
        /// Shows the slash command whitelist for a specific command.
        /// </summary>
        private async Task ShowSlashCommandWhitelist(SocketMessage message, string commandName, DiscordBotClient client)
        {
            var whitelistedUsers = client.Permissions.CurrentPermissions.WhitelistedUsers.TryGetValue(commandName, out var users)
                ? users
                : new List<ulong>();

            var embedBuilder = new EmbedBuilder()
                .WithTitle($"📋 Whitelist for /{commandName}")
                .WithDescription("Manage whitelisted users for this slash command.")
                .WithColor(Color.Green);

            // Add current whitelisted users
            var userList = new StringBuilder();
            if (whitelistedUsers.Count > 0)
            {
                foreach (var userId in whitelistedUsers)
                {
                    userList.AppendLine($"• <@{userId}> (ID: {userId})");
                }
            }
            else
            {
                userList.AppendLine("No users whitelisted for this slash command.");
            }

            embedBuilder.AddField("Whitelisted Users", userList.ToString())
                .AddField("Commands",
                    "**add @user** - Add a user to the whitelist\n" +
                    "**remove @user** - Remove a user from the whitelist\n" +
                    "**back** - Return to slash whitelist menu\n" +
                    "**exit** - Close this menu")
                .WithFooter("This menu will time out after 2 minutes of inactivity");

            await message.Channel.SendMessageAsync(embed: embedBuilder.Build());

            // Update the menu state
            var state = _activeMenus[message.Author.Id];
            state.CurrentMenu = MenuType.SlashCommandWhitelist;
            state.SelectedSlashCommand = commandName;
            state.LastActivity = DateTime.UtcNow;
        }

        /// <summary>
        /// Shows the slash command blacklist for a specific command.
        /// </summary>
        private async Task ShowSlashCommandBlacklist(SocketMessage message, string commandName, DiscordBotClient client)
        {
            var blacklistedUsers = client.Permissions.CurrentPermissions.BlacklistedUsers.TryGetValue(commandName, out var users)
                ? users
                : new List<ulong>();

            var embedBuilder = new EmbedBuilder()
                .WithTitle($"⛔ Blacklist for /{commandName}")
                .WithDescription("Manage blacklisted users for this slash command.")
                .WithColor(Color.Red);

            // Add current blacklisted users
            var userList = new StringBuilder();
            if (blacklistedUsers.Count > 0)
            {
                foreach (var userId in blacklistedUsers)
                {
                    userList.AppendLine($"• <@{userId}> (ID: {userId})");
                }
            }
            else
            {
                userList.AppendLine("No users blacklisted for this slash command.");
            }

            embedBuilder.AddField("Blacklisted Users", userList.ToString())
                .AddField("Commands",
                    "**add @user** - Add a user to the blacklist\n" +
                    "**remove @user** - Remove a user from the blacklist\n" +
                    "**back** - Return to slash blacklist menu\n" +
                    "**exit** - Close this menu")
                .WithFooter("This menu will time out after 2 minutes of inactivity");

            await message.Channel.SendMessageAsync(embed: embedBuilder.Build());

            // Update the menu state
            var state = _activeMenus[message.Author.Id];
            state.CurrentMenu = MenuType.SlashCommandBlacklist;
            state.SelectedSlashCommand = commandName;
            state.LastActivity = DateTime.UtcNow;
        }

        /// <summary>
        /// Shows the cooldown management menu with pagination.
        /// </summary>
        private async Task ShowCooldownMenu(SocketMessage message, DiscordBotClient client)
        {
            var state = _activeMenus[message.Author.Id];
            var allCommands = client._commands.Values.OrderBy(c => c.Name).ToList();

            // Calculate pagination
            int totalPages = (int)Math.Ceiling((double)allCommands.Count / state.ItemsPerPage);
            int startIndex = state.CurrentPage * state.ItemsPerPage;
            int endIndex = Math.Min(startIndex + state.ItemsPerPage, allCommands.Count);

            var embedBuilder = new EmbedBuilder()
                .WithTitle("⏱️ Cooldown Management")
                .WithDescription($"Select a command to manage its cooldown time:\n**Page {state.CurrentPage + 1} of {totalPages}**")
                .WithColor(Color.Purple);

            // Add commands for current page
            for (int i = startIndex; i < endIndex; i++)
            {
                var command = allCommands[i];
                var cooldownSeconds = client.Permissions.GetCommandCooldown(command.Name);
                string cooldownText = cooldownSeconds > 0 ? $"{cooldownSeconds} seconds" : "No cooldown";
                int displayIndex = i + 1;
                embedBuilder.AddField($"{displayIndex}. {command.Name}", $"Current cooldown: {cooldownText}");
            }

            // Add navigation options
            var navigationText = new StringBuilder();
            if (state.CurrentPage > 0)
                navigationText.AppendLine("⬅️ Type 'prev' for previous page");
            if (state.CurrentPage < totalPages - 1)
                navigationText.AppendLine("➡️ Type 'next' for next page");

            if (navigationText.Length > 0)
                embedBuilder.AddField("📄 Navigation", navigationText.ToString().Trim());

            embedBuilder.AddField("🔙 Back", "Type 'back' to return to the main menu")
                .AddField("❌ Exit", "Type 'exit' to close this menu")
                .WithFooter($"This menu will time out after 2 minutes of inactivity • Page {state.CurrentPage + 1}/{totalPages}");

            await message.Channel.SendMessageAsync(embed: embedBuilder.Build());

            // Update the menu state
            state.CurrentMenu = MenuType.CooldownManagement;
            state.CommandList = allCommands;
            state.LastActivity = DateTime.UtcNow;
        }

        /// <summary>
        /// Shows the cooldown setting menu for a specific command.
        /// </summary>
        private async Task ShowCommandCooldown(SocketMessage message, Command command, DiscordBotClient client)
        {
            var currentCooldown = client.Permissions.GetCommandCooldown(command.Name);
            string cooldownText = currentCooldown > 0 ? $"{currentCooldown} seconds" : "No cooldown";

            var embed = new EmbedBuilder()
                .WithTitle($"⏱️ Cooldown for {command.Name}")
                .WithDescription($"Current cooldown: {cooldownText}\n\nEnter a new cooldown time in seconds, or 0 to disable cooldown.")
                .WithColor(Color.Purple)
                .AddField("Examples",
                    "**5** - 5 second cooldown\n" +
                    "**30** - 30 second cooldown\n" +
                    "**300** - 5 minute cooldown\n" +
                    "**0** - No cooldown")
                .AddField("🔙 Back", "Type 'back' to return to the cooldown list")
                .AddField("❌ Exit", "Type 'exit' to close this menu")
                .WithFooter("This menu will time out after 2 minutes of inactivity")
                .Build();

            await message.Channel.SendMessageAsync(embed: embed);

            // Update the menu state
            var state = _activeMenus[message.Author.Id];
            state.CurrentMenu = MenuType.CommandCooldown;
            state.SelectedCommand = command;
            state.LastActivity = DateTime.UtcNow;
        }

        /// <summary>
        /// Processes input for the cooldown management menu with pagination support.
        /// </summary>
        private async Task ProcessCooldownManagementInput(SocketMessage message, PermissionMenuState state, DiscordBotClient client)
        {
            string input = message.Content.ToLower();

            // Handle navigation
            if (input == "next")
            {
                int totalPages = (int)Math.Ceiling((double)state.CommandList.Count / state.ItemsPerPage);
                if (state.CurrentPage < totalPages - 1)
                {
                    state.CurrentPage++;
                    await ShowCooldownMenu(message, client);
                }
                else
                {
                    await message.Channel.SendMessageAsync("You're already on the last page.");
                }
                return;
            }

            if (input == "prev" || input == "previous")
            {
                if (state.CurrentPage > 0)
                {
                    state.CurrentPage--;
                    await ShowCooldownMenu(message, client);
                }
                else
                {
                    await message.Channel.SendMessageAsync("You're already on the first page.");
                }
                return;
            }

            // Handle command selection
            if (int.TryParse(message.Content, out int index) && index > 0 && index <= state.CommandList.Count)
            {
                var command = state.CommandList[index - 1];
                await ShowCommandCooldown(message, command, client);
            }
            else
            {
                await message.Channel.SendMessageAsync("Invalid option. Please select a valid command number, type 'next'/'prev' for navigation, or 'back' to return to the main menu.");
            }
        }

        /// <summary>
        /// Closes the menu for a user and cleans up resources.
        /// </summary>
        /// <param name="userId">The ID of the user whose menu to close.</param>
        /// <param name="client">The Discord bot client.</param>
        /// <param name="message">Optional message to send to the user.</param>
        private async Task CloseMenu(ulong userId, DiscordBotClient client, string? message = null)
        {
            // Remove the menu state
            _activeMenus.Remove(userId);

            // Get and remove the message handler
            if (_messageHandlers.TryGetValue(userId, out var handler))
            {
                // Unregister the handler
                client._client.MessageReceived -= handler;
                _messageHandlers.Remove(userId);
            }

            // We can't send a message here since we don't have the channel
            // The message will be sent by the calling method if needed
        }

        /// <summary>
        /// Processes input for the command cooldown menu.
        /// </summary>
        private async Task ProcessCommandCooldownInput(SocketMessage message, PermissionMenuState state, DiscordBotClient client)
        {
            if (state.SelectedCommand == null)
            {
                await message.Channel.SendMessageAsync("Error: No command selected.");
                await ShowCooldownMenu(message, client);
                return;
            }

            if (int.TryParse(message.Content, out int cooldownSeconds) && cooldownSeconds >= 0)
            {
                // Set the cooldown time
                client.Permissions.SetCommandCooldown(state.SelectedCommand.Name, cooldownSeconds);

                string cooldownText = cooldownSeconds > 0 ? $"{cooldownSeconds} seconds" : "disabled";
                await message.Channel.SendMessageAsync($"Cooldown for {state.SelectedCommand.Name} set to {cooldownText}.");

                // Return to the cooldown list
                await ShowCooldownMenu(message, client);
            }
            else
            {
                await message.Channel.SendMessageAsync("Invalid input. Please enter a valid number of seconds (0 or greater), or type 'back' to return to the cooldown list.");
            }
        }

        /// <summary>
        /// Shows the rate limit management menu.
        /// </summary>
        private async Task ShowRateLimitMenu(SocketMessage message, DiscordBotClient client)
        {
            // Get current rate limit settings
            int windowSeconds = client.Permissions.GetRateLimitWindow();
            int maxDifferentCommands = client.Permissions.GetMaxDifferentCommands();
            int maxSameCommand = client.Permissions.GetMaxSameCommand();

            var embed = new EmbedBuilder()
                .WithTitle("⚡ Rate Limit Management")
                .WithDescription("Configure rate limiting settings to prevent command spam. Rate limits help protect the bot from being overloaded by too many commands in a short period.")
                .WithColor(Color.Orange)
                .AddField("Current Settings",
                    $"**Rate Limit Window:** {windowSeconds} seconds\n" +
                    $"**Max Different Commands:** {maxDifferentCommands} commands\n" +
                    $"**Max Same Command:** {maxSameCommand} times")
                .AddField("Commands",
                    "**window [seconds]** - Set the rate limit window in seconds (1-300)\n" +
                    "**different [count]** - Set the maximum number of different commands\n" +
                    "**same [count]** - Set the maximum number of times the same command can be used\n" +
                    "**balanced** - Apply balanced preset (5s window, 4 different, 5 same)\n" +
                    "**strict** - Apply strict preset (10s window, 3 different, 3 same)\n" +
                    "**lenient** - Apply lenient preset (3s window, 6 different, 8 same)\n" +
                    "**back** - Return to main menu\n" +
                    "**exit** - Close this menu")
                .AddField("Example",
                    "With current settings, users will be rate limited if they:\n" +
                    $"- Use {maxDifferentCommands} different commands within {windowSeconds} seconds, OR\n" +
                    $"- Use the same command {maxSameCommand} times within {windowSeconds} seconds")
                .AddField("Rate Limit Behavior",
                    "When a user is rate limited:\n" +
                    "- They will see a message indicating they're going too fast\n" +
                    "- The message will automatically delete after 5 seconds\n" +
                    "- They cannot use any commands until the rate limit window expires\n" +
                    "- Bot owner and creator are exempt from rate limits")
                .AddField("Recommended Settings",
                    "**Balanced:** Window: 5s, Different: 4, Same: 5\n" +
                    "**Strict:** Window: 10s, Different: 3, Same: 3\n" +
                    "**Lenient:** Window: 3s, Different: 6, Same: 8")
                .WithFooter("This menu will time out after 2 minutes of inactivity")
                .Build();

            await message.Channel.SendMessageAsync(embed: embed);

            // Update the menu state
            var state = _activeMenus[message.Author.Id];
            state.CurrentMenu = MenuType.RateLimitManagement;
            state.LastActivity = DateTime.UtcNow;
        }

        /// <summary>
        /// Processes input for the rate limit management menu.
        /// </summary>
        private async Task ProcessRateLimitManagementInput(SocketMessage message, PermissionMenuState state, DiscordBotClient client)
        {
            string[] parts = message.Content.Split(' ', 2);
            string command = parts[0].ToLower();

            // Handle preset commands that don't require a value
            if (command == "balanced" || command == "strict" || command == "lenient")
            {
                await ApplyRateLimitPreset(command, message, client);
                return;
            }

            // For commands that require a value
            if (parts.Length < 2)
            {
                await message.Channel.SendMessageAsync("Please provide a value. Example: `window 5` or `different 4` or `same 5`.\n\nOr use a preset: `balanced`, `strict`, or `lenient`.");
                return;
            }

            if (!int.TryParse(parts[1], out int value) || value < 1)
            {
                await message.Channel.SendMessageAsync("Please provide a valid positive number.");
                return;
            }

            switch (command)
            {
                case "window":
                    // Cap window at 300 seconds (5 minutes)
                    if (value > 300)
                    {
                        value = 300;
                        await message.Channel.SendMessageAsync("Window value capped at 300 seconds (5 minutes).");
                    }
                    client.Permissions.SetRateLimitWindow(value);
                    await message.Channel.SendMessageAsync($"Rate limit window set to {value} seconds.");
                    break;
                case "different":
                    client.Permissions.SetMaxDifferentCommands(value);
                    await message.Channel.SendMessageAsync($"Maximum different commands set to {value}.");
                    break;
                case "same":
                    client.Permissions.SetMaxSameCommand(value);
                    await message.Channel.SendMessageAsync($"Maximum same command usage set to {value}.");
                    break;
                default:
                    await message.Channel.SendMessageAsync("Invalid command. Use `window`, `different`, `same`, `balanced`, `strict`, or `lenient`.");
                    return;
            }

            // Refresh the menu
            await ShowRateLimitMenu(message, client);
        }

        /// <summary>
        /// Applies a preset rate limit configuration.
        /// </summary>
        /// <param name="preset">The preset name: balanced, strict, or lenient.</param>
        /// <param name="message">The message that triggered the command.</param>
        /// <param name="client">The Discord bot client.</param>
        private async Task ApplyRateLimitPreset(string preset, SocketMessage message, DiscordBotClient client)
        {
            switch (preset.ToLower())
            {
                case "balanced":
                    client.Permissions.SetRateLimitWindow(5);
                    client.Permissions.SetMaxDifferentCommands(4);
                    client.Permissions.SetMaxSameCommand(5);
                    await message.Channel.SendMessageAsync("Applied balanced rate limit preset: Window: 5s, Different: 4, Same: 5");
                    break;
                case "strict":
                    client.Permissions.SetRateLimitWindow(10);
                    client.Permissions.SetMaxDifferentCommands(3);
                    client.Permissions.SetMaxSameCommand(3);
                    await message.Channel.SendMessageAsync("Applied strict rate limit preset: Window: 10s, Different: 3, Same: 3");
                    break;
                case "lenient":
                    client.Permissions.SetRateLimitWindow(3);
                    client.Permissions.SetMaxDifferentCommands(6);
                    client.Permissions.SetMaxSameCommand(8);
                    await message.Channel.SendMessageAsync("Applied lenient rate limit preset: Window: 3s, Different: 6, Same: 8");
                    break;
                default:
                    await message.Channel.SendMessageAsync("Unknown preset. Available presets: balanced, strict, lenient.");
                    return;
            }

            // Refresh the menu
            await ShowRateLimitMenu(message, client);
        }
    }

    /// <summary>
    /// Enum representing the different menu types in the permissions system.
    /// </summary>
    public enum MenuType
    {
        Main,
        CommandPermissions,
        PermissionLevel,
        StaffManagement,
        WhitelistManagement,
        CommandWhitelist,
        BlacklistManagement,
        CommandBlacklist,
        SlashWhitelistManagement,
        SlashCommandWhitelist,
        SlashBlacklistManagement,
        SlashCommandBlacklist,
        CooldownManagement,
        CommandCooldown,
        RateLimitManagement
    }

    /// <summary>
    /// Class to track the state of a permission menu for a user.
    /// </summary>
    public class PermissionMenuState
    {
        /// <summary>
        /// The current menu the user is viewing.
        /// </summary>
        public MenuType CurrentMenu { get; set; } = MenuType.Main;

        /// <summary>
        /// The list of commands for selection menus.
        /// </summary>
        public List<Command> CommandList { get; set; } = new List<Command>();

        /// <summary>
        /// The currently selected command.
        /// </summary>
        public Command? SelectedCommand { get; set; }

        /// <summary>
        /// The list of slash commands for selection menus.
        /// </summary>
        public List<string> SlashCommandList { get; set; } = new List<string>();

        /// <summary>
        /// The currently selected slash command.
        /// </summary>
        public string? SelectedSlashCommand { get; set; }

        /// <summary>
        /// The time of the last activity in this menu.
        /// </summary>
        public DateTime LastActivity { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Current page number for paginated menus (0-based).
        /// </summary>
        public int CurrentPage { get; set; } = 0;

        /// <summary>
        /// Number of items to display per page.
        /// </summary>
        public int ItemsPerPage { get; set; } = 10;
    }
}
