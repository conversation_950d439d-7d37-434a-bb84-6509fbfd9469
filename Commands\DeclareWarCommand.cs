using Discord;
using Discord.WebSocket;
using BotChan.Services;
using BotChan.Models;
using System.Text;

namespace BotChan.Commands
{
    /// <summary>
    /// DeclareWarCommand - Allows users to declare war against AI or other players
    /// Usage: %declarewar [target]
    /// Example: %declarewar AI
    /// </summary>
    public class DeclareWarCommand : Command
    {
        // Override the default category
        public override string Category => "Economy";

        // Override the default usage
        public override string Usage => "%declarewar [target]";

        // Override the default examples
        public override string Examples => "%declarewar AI";

        // Random number generator for battle outcomes
        private static readonly Random _random = new Random();

        // Dictionary to track pending war confirmations
        private static readonly Dictionary<ulong, (string Target, long TargetBotCount, DateTime Expiry, ulong? TargetUserId)> _pendingConfirmations = new Dictionary<ulong, (string, long, DateTime, ulong?)>();

        // Timeout for confirmations (30 seconds)
        private const int ConfirmationTimeoutSeconds = 30;

        // Static reference to the client for message handling
        private static DiscordBotClient _clientInstance;

        // Flag to track if the message handler has been registered
        private static bool _messageHandlerRegistered = false;

        public DeclareWarCommand() : base("declarewar", "Declare war against AI or other players")
        {
        }

        /// <summary>
        /// Handles all messages to check for confirmation responses
        /// </summary>
        private static async Task HandleMessageReceived(SocketMessage message)
        {
            // Ignore messages from bots
            if (message.Author.IsBot) return;

            // Check if this user has a pending confirmation
            if (_pendingConfirmations.TryGetValue(message.Author.Id, out var confirmationData))
            {
                // Check if the confirmation has expired
                if (DateTime.UtcNow > confirmationData.Expiry)
                {
                    // Remove the expired confirmation
                    _pendingConfirmations.Remove(message.Author.Id);
                    await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1142948885694787684> | {message.Author.Username}**, your war declaration has expired. Use `%declarewar AI` or `%declarewar [user ID]` to start again.");
                    return;
                }

                // Process the confirmation response
                string response = message.Content.Trim().ToUpper();

                // Check for Yes variations (Y, YES, YEAH, YEP, YE, YUP)
                if (response == "Y" || response == "YES" || response == "YEAH" || response == "YEP" || response == "YE" || response == "YUP")
                {
                    // User confirmed, remove from pending and start the battle
                    string confirmedTarget = confirmationData.Target;
                    long confirmedTargetBotCount = confirmationData.TargetBotCount;
                    ulong? confirmedTargetUserId = confirmationData.TargetUserId;
                    _pendingConfirmations.Remove(message.Author.Id);

                    // Get the user's data (in case it changed since confirmation was requested)
                    var confirmedUserData = _clientInstance.BotSystem.GetUserData(message.Author.Id, message.Author.Username);

                    // Start the battle in a background task to prevent blocking the bot
                    if (confirmedTarget == "AI")
                    {
                        // AI battle - run in background
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await new DeclareWarCommand().StartBattle(message, _clientInstance, confirmedUserData, confirmedTargetBotCount);
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error in AI battle: {ex.Message}");
                                try
                                {
                                    await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1142948885694787684> | {message.Author.Username}**, an error occurred during the battle. Please try again.");
                                }
                                catch { /* Ignore errors when sending error message */ }
                            }
                        });
                    }
                    else if (confirmedTargetUserId.HasValue)
                    {
                        // PvP battle - run in background
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                // Get the target user's data
                                var targetUserData = _clientInstance.BotSystem.GetUserData(confirmedTargetUserId.Value);

                                // Check if the target user still has enough bots
                                if (targetUserData.BotCount < 100)
                                {
                                    await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1142948885694787684> | {message.Author.Username}**, {confirmedTarget} no longer has enough bots to battle (minimum 100 required).");
                                    return;
                                }

                                // Start the PvP battle
                                await new DeclareWarCommand().StartPvpBattle(message, _clientInstance, confirmedUserData, targetUserData);
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error in PvP battle: {ex.Message}");
                                try
                                {
                                    await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1142948885694787684> | {message.Author.Username}**, an error occurred during the battle. Please try again.");
                                }
                                catch { /* Ignore errors when sending error message */ }
                            }
                        });
                    }
                    return;
                }
                // Check for No variations (N, NO, NAH, NOPE)
                else if (response == "N" || response == "NO" || response == "NAH" || response == "NOPE")
                {
                    // User declined, remove from pending
                    _pendingConfirmations.Remove(message.Author.Id);
                    await message.Channel.SendMessageAsync($"**<:BM_BotChanOhISee:1342598494883676334> | {message.Author.Username}**, you wisely decided to retreat from this battle.");
                    return;
                }
                else if (message.Content.StartsWith("%declarewar", StringComparison.OrdinalIgnoreCase))
                {
                    // User is trying to start a new war declaration, let it pass through
                    // We'll remove the old confirmation in the ExecuteAsync method
                    return;
                }
                else if (message.Content.StartsWith("%"))
                {

                    // User is trying to use another command, cancel the confirmation
                    _pendingConfirmations.Remove(message.Author.Id);
                    var sentMessage = await message.Channel.SendMessageAsync($"**<:BM_BotChanOhImBlind:1342600456404205588> | {message.Author.Username}**, You chose to abandon the enemy territory for something more important. Your war declaration was canceled.");

                    // Schedule the message to be deleted after 5 seconds
                    _ = Task.Run(async () => {
                        await Task.Delay(5000); // Wait 5 seconds
                        await sentMessage.DeleteAsync();
                    });

                    return;
                }
                else
                {
                    // Invalid response
                    await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1142948885694787684> | {message.Author.Username}**, please respond with Y/Yes or N/No to confirm or decline the battle.");
                    return;
                }
            }
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Store the client instance for the message handler
            _clientInstance = client;

            // Register the message handler if not already registered
            if (!_messageHandlerRegistered)
            {
                client._client.MessageReceived += HandleMessageReceived;
                _messageHandlerRegistered = true;
            }

            // Check if the user already has a pending confirmation
            bool hasPendingConfirmation = _pendingConfirmations.ContainsKey(message.Author.Id);

            // Clear any existing confirmation for this user
            // This allows users to restart the war declaration process
            _pendingConfirmations.Remove(message.Author.Id);

            // Normal command processing
            // Check if a target was specified
            if (args.Length < 2)
            {
                await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1142948885694787684> | {message.Author.Username}**, you need to specify a target! Try `%declarewar AI` or `%declarewar [user ID]`");
                return;
            }

            // Get the user's data
            var userData = client.BotSystem.GetUserData(message.Author.Id, message.Author.Username);

            // Check if the user has at least 100 bots
            if (userData.BotCount < 100)
            {
                await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1142948885694787684> | {message.Author.Username}**, you need at least 100 bots to declare war! You currently have {userData.BotCount} bots. Use `%recruit` to get more bots first.");
                return;
            }

            // Get the target
            string target = args[1];

            // Check if the target is AI
            if (target.Equals("AI", StringComparison.OrdinalIgnoreCase))
            {
                // Generate a random number of bots for the AI based on the user's bot count
                // Between 50% and 200% of the user's bot count, but with reasonable scaling for high bot counts
                long minAiBots = Math.Max(50, userData.BotCount / 2);
                long maxAiBots = userData.BotCount * 2;

                // For very high bot counts, cap the AI to prevent extremely long battles
                // but ensure the range is always valid
                const long MAX_AI_BOTS = 1000000; // Cap AI at 1M bots for reasonable battle times
                maxAiBots = Math.Min(maxAiBots, MAX_AI_BOTS);

                // Ensure minAiBots is never greater than maxAiBots
                if (minAiBots > maxAiBots)
                {
                    minAiBots = maxAiBots / 2;
                }

                long aiBotCount = _random.NextInt64(minAiBots, maxAiBots + 1);

                // Ask for confirmation before starting the battle
                await RequestBattleConfirmation(message, client, "AI", aiBotCount, null, hasPendingConfirmation);
                return;
            }

            // Try to parse the target as a user ID
            if (ulong.TryParse(target, out ulong targetUserId))
            {
                // Check if the user is trying to attack themselves
                if (targetUserId == message.Author.Id)
                {
                    await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1142948885694787684> | {message.Author.Username}**, you can't declare war on yourself!");
                    return;
                }

                // Get the target user's data
                var targetUserData = client.BotSystem.GetUserData(targetUserId);

                // Check if the target user exists and has enough bots
                if (targetUserData.BotCount < 100)
                {
                    await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1142948885694787684> | {message.Author.Username}**, that user doesn't have enough bots to battle (minimum 100 required).");
                    return;
                }

                // Check if the attacker's army is 95% or larger than the target's
                if (userData.BotCount >= (long)(targetUserData.BotCount * 1.95)) // 95% more bots (1.95 times as many)
                {
                    await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1142948885694787684> | {message.Author.Username}**, your army has grown so large it is pointless to attack this enemy territory. Wait until they have gathered enough resources and attack!");
                    return;
                }

                // Check if the user is on cooldown for attacking this target
                const int AttackCooldownHours = 12; // 12-hour cooldown
                if (!userData.CanAttackTarget(targetUserId, AttackCooldownHours, out TimeSpan remainingTime))
                {
                    // Format the remaining time in a user-friendly way
                    string timeText;
                    if (remainingTime.TotalHours >= 1)
                    {
                        timeText = $"{Math.Floor(remainingTime.TotalHours)} hour{(Math.Floor(remainingTime.TotalHours) != 1 ? "s" : "")}";
                    }
                    else
                    {
                        timeText = $"{Math.Floor(remainingTime.TotalMinutes)} minute{(Math.Floor(remainingTime.TotalMinutes) != 1 ? "s" : "")}";
                    }

                    // Calculate when the cooldown expires
                    DateTime expiresAt = DateTime.UtcNow.Add(remainingTime);
                    long expiresAtUnix = ((DateTimeOffset)expiresAt).ToUnixTimeSeconds();

                    await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1142948885694787684> | {message.Author.Username}**, your troops are exhausted from the previous battle with this territory! You need to wait **{timeText}** before attacking again. Try again <t:{expiresAtUnix}:R>.");
                    return;
                }

                // Try to get the target user from Discord
                var targetUser = client._client.GetUser(targetUserId);

                // Update user information if possible
                if (targetUser != null)
                {
                    client.BotSystem.GetUserData(targetUser);
                }

                // Use the stored username if we can't find the user
                string targetName = targetUser?.Username ?? targetUserData.GetDisplayName();

                // Ask for confirmation before starting the battle
                await RequestBattleConfirmation(message, client, targetName, targetUserData.BotCount, targetUserId, hasPendingConfirmation);
                return;
            }

            // If we get here, the target is invalid
            await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1142948885694787684> | {message.Author.Username}**, invalid target. Try `%declarewar AI` or `%declarewar [user ID]`");
        }

        /// <summary>
        /// Requests confirmation from the user before starting a battle
        /// </summary>
        /// <param name="message">The message that triggered the command</param>
        /// <param name="client">The Discord bot client</param>
        /// <param name="targetName">The name of the target (AI or username)</param>
        /// <param name="targetBotCount">The number of enemy bots</param>
        /// <param name="targetUserId">The user ID of the target (null for AI)</param>
        /// <param name="isSeekingDifferentTarget">Whether the user is seeking a different target</param>
        private async Task RequestBattleConfirmation(SocketMessage message, DiscordBotClient client, string targetName, long targetBotCount, ulong? targetUserId, bool isSeekingDifferentTarget = false)
        {
            // Store the target information and set an expiry time
            _pendingConfirmations[message.Author.Id] = (targetName, targetBotCount, DateTime.UtcNow.AddSeconds(ConfirmationTimeoutSeconds), targetUserId);

            // Send the confirmation message
            if (targetName == "AI")
            {
                // AI battle
                if (isSeekingDifferentTarget)
                {
                    // User is seeking a different target
                    await message.Channel.SendMessageAsync($"<:BM_BChanSmug:1181737377379651644> | **{message.Author.Username}**, You chose to seek for a different target and stumbled upon a territory with **{targetBotCount}** enemy bots {Constants.Emojis.BotDown}. Do you wish to proceed? (Y/Yes or N/No)");
                }
                else
                {
                    // First territory discovery
                    await message.Channel.SendMessageAsync($"<:BM_BChanSmug:1181737377379651644> | **{message.Author.Username}**, You stumble upon a territory with **{targetBotCount}** enemy bots {Constants.Emojis.BotDown}. Do you wish to proceed? (Y/Yes or N/No)");
                }
            }
            else
            {
                // PvP battle
                if (isSeekingDifferentTarget)
                {
                    // User is seeking a different target
                    await message.Channel.SendMessageAsync($"<:BM_BChanSmug:1181737377379651644> | **{message.Author.Username}**, You chose to seek for a different target and found **{targetName}**'s territory with **{targetBotCount}** bots {Constants.Emojis.BotDown}. Do you wish to proceed? (Y/Yes or N/No)");
                }
                else
                {
                    // First territory discovery
                    await message.Channel.SendMessageAsync($"<:BM_BChanSmug:1181737377379651644> | **{message.Author.Username}**, You found **{targetName}**'s territory with **{targetBotCount}** bots {Constants.Emojis.BotDown}. Do you wish to proceed? (Y/Yes or N/No)");
                }
            }
        }

        /// <summary>
        /// Starts a battle between the user and the AI
        /// </summary>
        private async Task StartBattle(SocketMessage message, DiscordBotClient client, UserData userData, long aiBotCount)
        {
            // Calculate win chance based on bot difference
            double winChance = CalculateWinChance(userData.BotCount, aiBotCount);

            // Check if this is a guaranteed loss due to 90% difference
            bool guaranteedLoss = false;
            if (aiBotCount > userData.BotCount)
            {
                double botRatio = (double)aiBotCount / userData.BotCount;
                if (botRatio >= 1.9) // 90% more bots (1.9 times as many)
                {
                    guaranteedLoss = true;
                }
            }

            // Determine the winner
            bool userWins = guaranteedLoss ? false : _random.NextDouble() < winChance;

            // Create the initial battle message
            var embed = CreateBattleEmbed(message.Author.Username, userData.BotCount, "AI", aiBotCount, 0, message.Author.GetAvatarUrl(), userWins);
            var battleMessage = await message.Channel.SendMessageAsync(embed: embed.Build());

            // Calculate bot losses based on early game, mid game and end game
            int loserBotLossPercentage;
            if(userData.BotCount < 10000)
            {
                // Early game
                loserBotLossPercentage = _random.Next(5, 41);
            }
            else if(userData.BotCount < 100000)
            {
                // Mid game
                loserBotLossPercentage = _random.Next(10, 31);
            }
            else
            {
                // End game
                loserBotLossPercentage = _random.Next(15, 26);
            }

            // Calculate the number of bots the loser will lose
            long userBotLoss = 0;
            long aiBotLoss = 0;
            long userBotGain = 0;
            long aiBotGain = 0;

            if (userWins)
            {
                // AI loses bots, user gains a portion of them
                aiBotLoss = (long)Math.Ceiling(aiBotCount * (loserBotLossPercentage / 100.0));
                // Make sure losses are at least 1 bot and don't exceed the total
                aiBotLoss = Math.Max(1, Math.Min(aiBotLoss, aiBotCount));
                // User gains the bots that AI lost
                userBotGain = aiBotLoss;
            }
            else
            {
                // User loses bots, AI gains a portion of them
                userBotLoss = (long)Math.Ceiling(userData.BotCount * (loserBotLossPercentage / 100.0));
                // Make sure losses are at least 1 bot and don't exceed the total
                userBotLoss = Math.Max(1, Math.Min(userBotLoss, userData.BotCount));
                // AI gains the bots that user lost
                aiBotGain = userBotLoss;
            }

            // Simulate the battle over 10 seconds with 5 updates
            int totalUpdates = 5;
            int delayBetweenUpdates = 2000; // 2 seconds between updates

            for (int i = 1; i <= totalUpdates; i++)
            {
                // Wait for the specified delay
                await Task.Delay(delayBetweenUpdates);

                // Calculate progress percentage
                int progressPercentage = (i * 100) / totalUpdates;

                // Update the battle message - pass the actual battle outcome to ensure visualization matches result
                embed = CreateBattleEmbed(message.Author.Username, userData.BotCount, "AI", aiBotCount, progressPercentage, message.Author.GetAvatarUrl(), userWins);
                await battleMessage.ModifyAsync(msg => msg.Embed = embed.Build());
            }

            // Wait a moment before showing the final result
            await Task.Delay(1000);

            // Apply the losses and gains
            if (userWins)
            {
                // User wins, gains bots from AI
                client.BotSystem.AddBots(message.Author.Id, userBotGain);

                // Record war statistics
                userData.RecordWarResult(true, userBotGain, 0, true); // true for AI war

                // Get updated user data
                userData = client.BotSystem.GetUserData(message.Author.Id);

                // Create the final battle result message
                var resultEmbed = CreateBattleResultEmbedNew(message.Author.Username, userData.BotCount, 0, userBotGain,
                                                         "AI", aiBotCount - aiBotLoss, aiBotLoss, 0, true, false, message.Author.GetAvatarUrl());
                await battleMessage.ModifyAsync(msg => msg.Embed = resultEmbed.Build());
            }
            else
            {
                // AI wins, user loses bots
                client.BotSystem.RemoveBots(message.Author.Id, userBotLoss);

                // Record war statistics
                userData.RecordWarResult(false, 0, userBotLoss, true); // true for AI war

                // Get updated user data
                userData = client.BotSystem.GetUserData(message.Author.Id);

                // Create the final battle result message
                var resultEmbed = CreateBattleResultEmbedNew(message.Author.Username, userData.BotCount, userBotLoss, 0,
                                                         "AI", aiBotCount + aiBotGain, 0, aiBotGain, false, guaranteedLoss, message.Author.GetAvatarUrl());
                await battleMessage.ModifyAsync(msg => msg.Embed = resultEmbed.Build());
            }
        }

        /// <summary>
        /// Calculates the win chance based on the difference in bot counts for AI battles
        /// Enhanced for better farming consistency at high bot counts
        /// </summary>
        private double CalculateWinChance(long userBotCount, long aiBotCount)
        {
            // Calculate the percentage difference
            double totalBots = userBotCount + aiBotCount;
            double userPercentage = (double)userBotCount / totalBots;
            double aiPercentage = (double)aiBotCount / totalBots;

            // Calculate the difference in percentages
            double percentageDifference = Math.Abs(userPercentage - aiPercentage);

            // Determine bot count tier for enhanced balance
            double farmingBonus = GetFarmingBonus(userBotCount);

            // Base win chance calculation
            double baseWinChance;
            if (percentageDifference < 0.25)
            {
                // Less than 25% difference: 50% chance to win
                baseWinChance = 0.5;
            }
            else if (userBotCount > aiBotCount)
            {
                // User has more bots
                if (percentageDifference >= 0.75)
                {
                    // 75% or more difference: 80% chance to win
                    baseWinChance = 0.8;
                }
                else if (percentageDifference >= 0.5)
                {
                    // 50% or more difference: 70% chance to win
                    baseWinChance = 0.7;
                }
                else // Between 25% and 50% difference
                {
                    // 25% or more difference: 60% chance to win
                    baseWinChance = 0.6;
                }
            }
            else
            {
                // AI has more bots
                if (percentageDifference >= 0.9)
                {
                    // 90% or more difference: 20% chance to win
                    baseWinChance = 0.2;
                }
                if (percentageDifference >= 0.75)
                {
                    // 75% or more difference: 30% chance to win
                    baseWinChance = 0.3;
                }
                else if (percentageDifference >= 0.5)
                {
                    // 50% or more difference: 40% chance to win
                    baseWinChance = 0.4;
                }
                else // Between 25% and 50% difference
                {
                    // 25% or more difference: 50% chance to win
                    baseWinChance = 0.5;
                }
            }

            // Apply farming bonus and ensure it doesn't exceed 0.90 (always keep some risk)
            double finalWinChance = Math.Min(0.90, baseWinChance + farmingBonus);
            return finalWinChance;
        }

        /// <summary>
        /// Calculates farming bonus based on user's bot count tier
        /// </summary>
        private double GetFarmingBonus(long userBotCount)
        {
            if (userBotCount >= 500000) // End-game (500k-1M): Maximum farming efficiency
            {
                return 0.5; // +5% win chance bonus
            }
            else if (userBotCount >= 100000) // Late end-game (100k-500k): High farming efficiency
            {
                return 0.10; // +10% win chance bonus
            }
            else if (userBotCount >= 10000) // Mid-game (10k-100k): Moderate farming efficiency
            {
                return 0.25; // +25% win chance bonus
            }
            else // Early game (under 10k): No bonus
            {
                return 0.0;
            }
        }

        /// <summary>
        /// Calculates the win chance based on the difference in bot counts for PvP battles
        /// </summary>
        private double CalculatePvpWinChance(long attackerBotCount, long targetBotCount)
        {
            // Calculate the percentage difference
            double totalBots = attackerBotCount + targetBotCount;
            double attackerPercentage = (double)attackerBotCount / totalBots;
            double targetPercentage = (double)targetBotCount / totalBots;

            // Calculate the difference in percentages
            double percentageDifference = Math.Abs(attackerPercentage - targetPercentage);

            // Determine win chance based on the specified rules
            if (percentageDifference < 0.25)
            {
                // Less than 25% difference: 55% chance to win
                return 0.55;
            }
            else if (attackerBotCount > targetBotCount)
            {
                // Attacker has more bots
                if (percentageDifference >= 0.90)
                {
                    // 90% or more difference: 100% chance to win
                    return 1.0;
                }
                if (percentageDifference >= 0.75)
                {
                    // 75% or more difference: 80% chance to win
                    return 0.8;
                }
                else if (percentageDifference >= 0.5)
                {
                    // 50% or more difference: 70% chance to win
                    return 0.7;
                }
                else // Between 25% and 50% difference
                {
                    // 25% or more difference: 60% chance to win
                    return 0.6;
                }
            }
            else
            {
                // Target has more bots
                if (percentageDifference >= 0.90)
                {
                    // 90% or more difference: 0% chance to win
                    return 0.0;
                }
                if (percentageDifference >= 0.75)
                {
                    // 75% or more difference: 30% chance to win
                    return 0.3;
                }
                else if (percentageDifference >= 0.5)
                {
                    // 50% or more difference: 40% chance to win
                    return 0.4;
                }
                else // Between 25% and 50% difference
                {
                    // 25% or more difference: 50% chance to win
                    return 0.5;
                }
            }
        }

        /// <summary>
        /// Creates an embed for the battle with the new visual layout
        /// </summary>
        private EmbedBuilder CreateBattleEmbed(string userName, long userBotCount, string aiName, long aiBotCount, int progressPercentage, string userAvatarUrl = null, bool? userWillWin = null)
        {
            // Calculate the current bot counts based on progress
            long currentUserBots = userBotCount;
            long currentAiBots = aiBotCount;

            // If battle is in progress, simulate bot losses over time
            if (progressPercentage > 0)
            {
                // Calculate bot losses based on progress
                double progress = progressPercentage / 100.0;

                // Determine who will win - use the actual battle outcome if provided
                bool willUserWin;

                if (userWillWin.HasValue)
                {
                    // Use the actual battle outcome that was determined in StartBattle
                    willUserWin = userWillWin.Value;
                }
                else
                {
                    // If no outcome was provided, use the win chance calculation
                    double winChance = CalculateWinChance(userBotCount, aiBotCount);
                    willUserWin = winChance >= 0.5;
                }

                // Simulate more dramatic bot losses with loser reaching exactly 0 at 100%
                if (willUserWin)
                {
                    // User will win
                    // AI loses all bots at the end
                    if (progressPercentage == 100)
                    {
                        // At 100% progress, AI has 0 bots left
                        currentAiBots = 0;
                    }
                    else
                    {
                        // Gradually decrease to near 0
                        long aiSurvivors = (long)(aiBotCount * (1 - (0.95 * progress)));
                        currentAiBots = Math.Max(aiSurvivors, 1); // At least 1 bot remains until final update
                    }

                    // User loses fewer bots
                    long userSurvivors = (long)(userBotCount * (1 - (0.4 * progress)));
                    currentUserBots = Math.Max(userSurvivors, userBotCount / 3); // At least 1/3 of original force remains
                }
                else
                {
                    // AI will win
                    // User loses all bots at the end
                    if (progressPercentage == 100)
                    {
                        // At 100% progress, user has 0 bots left
                        currentUserBots = 0;
                    }
                    else
                    {
                        // Gradually decrease to near 0
                        long userSurvivors = (long)(userBotCount * (1 - (0.95 * progress)));
                        currentUserBots = Math.Max(userSurvivors, 1); // At least 1 bot remains until final update
                    }

                    // AI loses fewer bots
                    long aiSurvivors = (long)(aiBotCount * (1 - (0.4 * progress)));
                    currentAiBots = Math.Max(aiSurvivors, aiBotCount / 3); // At least 1/3 of original force remains
                }
            }

            // Create the embed
            var embed = new EmbedBuilder();

            // Add author with avatar if available
            if (userAvatarUrl != null)
            {
                embed.WithAuthor(userName, userAvatarUrl);
            }
            else
            {
                embed.WithAuthor(userName);
            }

            embed.WithTitle($"{userName} goes into war with {aiName}")
                .WithDescription("The battle has begun!")
                .WithColor(Color.Red)
                .WithThumbnailUrl("https://cdn.discordapp.com/attachments/1363245551184908468/1363320873498447923/Bot_Army.png");

            // Create the battlefield visualization with single emojis
            string leftSide = "<a:BotWalkRight:1363225321188360422>";
            string rightSide = "<a:BotWalkLeft:1363225719701770460>";

            // Using single emojis for a cleaner look

            // Add the battlefield grid in a single field to reduce vertical spacing
            string battlefield = $"{leftSide}:vs:{rightSide}\n{leftSide}:crossed_swords:{rightSide}\n{leftSide}<:Blank:1410316480905609256>{rightSide}";
            embed.AddField("\u200b", battlefield, false);

            // Add the bot counts
            embed.AddField($"{userName}'s Army", $"{currentUserBots} <:BM_Bot:1363239519914164264>", true);
            embed.AddField("\u200b", "\u200b", true); // Empty field for spacing
            embed.AddField($"{aiName}'s Army", $"{currentAiBots} <:BM_Bot:1363239519914164264>", true);

            // Add battle progress as footer
            string battleStatus = progressPercentage switch
            {
                0 => "The armies are preparing for battle...",
                < 25 => "The armies are engaging in battle...",
                < 50 => "The battle is heating up! Bots are falling on both sides!",
                < 75 => "The battlefield is chaotic! Who will emerge victorious?",
                < 100 => "The battle is reaching its climax! The outcome will be decided soon!",
                _ => "Battle completed!"
            };

            embed.WithFooter($"Battle progress: {progressPercentage}% • {battleStatus}");

            return embed;
        }

        /// <summary>
        /// Creates an embed for the battle result
        /// </summary>
        private EmbedBuilder CreateBattleResultEmbed(string userName, long userRemainingBots, long userLostBots, long userGainedBots,
                                                   string aiName, long aiRemainingBots, long aiLostBots, long aiGainedBots, bool userWon,
                                                   bool guaranteedLoss = false)
        {
            Color embedColor = userWon ? Color.Green : Color.Red;

            var embed = new EmbedBuilder()
                .WithTitle("⚔️ Bot War Results ⚔️")
                .WithColor(embedColor)
                .WithThumbnailUrl("https://cdn.discordapp.com/attachments/1363245551184908468/1363320873498447923/Bot_Army.png")
                .WithFooter("Battle completed")
                .WithCurrentTimestamp();

            // Create the description with the battle results
            var description = new StringBuilder();

            // Announce the winner
            if (userWon)
            {
                description.AppendLine($"🏆 **{userName} is victorious!** 🏆");
            }
            else
            {
                if (guaranteedLoss)
                {
                    description.AppendLine($"💀 **{aiName} has overwhelmed {userName}!** 💀");
                    description.AppendLine($"*The enemy had 90% more bots, making defeat inevitable.*");
                }
                else
                {
                    description.AppendLine($"❌ **{aiName} has defeated {userName}!** ❌");
                }
            }

            description.AppendLine();

            // Show the battle results
            description.AppendLine("**Battle Results:**");

            // Show losses and gains
            if (userWon)
            {
                // User won - show user gains and AI losses
                description.AppendLine($"{userName}: **+{userGainedBots}** {Constants.Emojis.BotDown} **(Captured from enemy)**");
                description.AppendLine($"{aiName}: **-{aiLostBots}** {Constants.Emojis.BotDown} **(Lost in battle)**");
            }
            else
            {
                // AI won - show user losses and AI gains
                description.AppendLine($"{userName}: **-{userLostBots}** {Constants.Emojis.BotDown} **(Lost in battle)**");
                description.AppendLine($"{aiName}: **+{aiGainedBots}** {Constants.Emojis.BotDown} **(Captured from enemy)**");
            }

            description.AppendLine();

            // Show the remaining forces
            description.AppendLine("**Remaining Forces:**");
            description.AppendLine($"{userName}: **{userRemainingBots}** {Constants.Emojis.BotDown}");
            description.AppendLine($"{aiName}: **{aiRemainingBots}** {Constants.Emojis.BotDown}");

            embed.WithDescription(description.ToString());
            return embed;
        }
        /// <summary>
        /// Creates an embed for the battle result with the new visual layout
        /// </summary>
        private EmbedBuilder CreateBattleResultEmbedNew(string userName, long userRemainingBots, long userLostBots, long userGainedBots,
                                                   string aiName, long aiRemainingBots, long aiLostBots, long aiGainedBots, bool userWon,
                                                   bool guaranteedLoss = false, string userAvatarUrl = null)
    {
        Color embedColor = userWon ? Color.Green : Color.Red;

        // Create the embed
        var embed = new EmbedBuilder();

        // Add author with avatar if available
        if (userAvatarUrl != null)
        {
            embed.WithAuthor(userName, userAvatarUrl);
        }
        else
        {
            embed.WithAuthor(userName);
        }

        // Set title and description based on result
        embed.WithTitle($"{userName} goes into war with {aiName}")
            .WithColor(embedColor)
            .WithThumbnailUrl("https://cdn.discordapp.com/attachments/1363245551184908468/1363320873498447923/Bot_Army.png");

        // Create the description with the battle results
        var description = new StringBuilder();

        // Announce the winner
        if (userWon)
        {
            description.AppendLine($"🏆 **{userName} is victorious!** 🏆");
        }
        else
        {
            if (guaranteedLoss)
            {
                description.AppendLine($"💀 **{aiName} has overwhelmed {userName}!** 💀");
                description.AppendLine($"*The enemy had 90% more bots, making defeat inevitable.*");
            }
            else
            {
                description.AppendLine($"❌ **{aiName} has defeated {userName}!** ❌");
            }
        }

        embed.WithDescription(description.ToString());

        // Create the battlefield visualization for the final state with single emojis
        string leftSide = "<a:BotWalkRight:1363225321188360422>";
        string rightSide = "<a:BotWalkLeft:1363225719701770460>";

        // Using single emojis for a cleaner look

        // Add the battlefield grid in a single field to reduce vertical spacing
        string battlefield = $"{leftSide}:vs:{rightSide}\n{leftSide}:crossed_swords:{rightSide}\n{leftSide}<:Blank:1410316480905609256>{rightSide}";
        embed.AddField("\u200b", battlefield, false);

        // Add the final bot counts
        embed.AddField($"{userName}'s Army", $"{userRemainingBots} <:Bot:1363239519914164264>", true);
        embed.AddField("\u200b", "\u200b", true); // Empty field for spacing
        embed.AddField($"{aiName}'s Army", $"{aiRemainingBots} <:Bot:1363239519914164264>", true);

        // Add battle results in the footer
        string resultText;
        if (userWon)
        {
            resultText = $"{userName}: +{userGainedBots} | {aiName}: -{aiLostBots}";
        }
        else
        {
            resultText = $"{userName}: -{userLostBots} | {aiName}: +{aiGainedBots}";
        }

        embed.WithFooter($"Battle completed • {resultText}");

        return embed;
        }

        /// <summary>
        /// Starts a PvP battle immediately
        /// </summary>
        private async Task StartPvpBattle(SocketMessage message, DiscordBotClient client, UserData attackerData, UserData targetData)
        {
            // Get the target user from Discord
            var targetUser = client._client.GetUser(targetData.UserId);

            // Update user information if possible
            if (targetUser != null)
            {
                client.BotSystem.GetUserData(targetUser);
            }

            // Use the stored username
            string targetUsername = targetUser?.Username ?? targetData.GetDisplayName();

            // Create the initial battle message
            var embed = CreateBattleEmbed(message.Author.Username, attackerData.BotCount, targetUsername, targetData.BotCount, 0, attackerData.GetAvatarUrl(), null);

            // Send the battle message to the channel
            var battleMessage = await message.Channel.SendMessageAsync(embed: embed.Build());

            // Execute the battle immediately
            await ExecutePvpBattle(message, client, attackerData, targetData, battleMessage);
        }

        /// <summary>
        /// Executes a PvP battle
        /// </summary>
        private async Task ExecutePvpBattle(SocketMessage message, DiscordBotClient client, UserData attackerData, UserData targetData, IUserMessage battleMessage)
        {
            // Get both users from Discord
            var attackerUser = client._client.GetUser(attackerData.UserId);
            var targetUser = client._client.GetUser(targetData.UserId);

            // Update user information if possible
            if (attackerUser != null)
            {
                client.BotSystem.GetUserData(attackerUser);
            }

            if (targetUser != null)
            {
                client.BotSystem.GetUserData(targetUser);
            }

            // Use stored usernames if users can't be found
            string attackerUsername = attackerUser?.Username ?? attackerData.GetDisplayName();
            string targetUsername = targetUser?.Username ?? targetData.GetDisplayName();

            // Calculate win chance based on bot difference using the PvP-specific method
            double winChance = CalculatePvpWinChance(attackerData.BotCount, targetData.BotCount);

            // Check if this is a guaranteed loss or win based on the win chance
            bool guaranteedLoss = false;
            bool attackerLikely = false;

            if (winChance == 0.0)
            {
                // 100% chance to lose
                guaranteedLoss = true;
                attackerLikely = false;
            }
            else if (winChance == 1.0)
            {
                // 100% chance to win
                guaranteedLoss = true;
                attackerLikely = true;
            }
            else if (attackerData.BotCount > targetData.BotCount)
            {
                double botRatio = (double)attackerData.BotCount / targetData.BotCount;
                if (botRatio >= 1.9) // 90% more bots (1.9 times as many)
                {
                    guaranteedLoss = true;
                    attackerLikely = true;
                }
            }
            else if (targetData.BotCount > attackerData.BotCount)
            {
                double botRatio = (double)targetData.BotCount / attackerData.BotCount;
                if (botRatio >= 1.9) // 90% more bots (1.9 times as many)
                {
                    guaranteedLoss = true;
                    attackerLikely = false;
                }
            }

            // Determine the winner
            bool attackerWins = guaranteedLoss ? attackerLikely : _random.NextDouble() < winChance;

            // Create the initial battle message
            var embed = CreateBattleEmbed(attackerUsername, attackerData.BotCount, targetUsername, targetData.BotCount, 0, attackerData.GetAvatarUrl(), attackerWins);

            // Update the battle message
            await battleMessage.ModifyAsync(msg => msg.Embed = embed.Build());

            // Calculate bot losses (between 5% and 31% of army)
            int loserBotLossPercentage = _random.Next(5, 31);

            // Calculate the number of bots the loser will lose
            long attackerBotLoss = 0;
            long targetBotLoss = 0;
            long attackerBotGain = 0;
            long targetBotGain = 0;

            if (attackerWins)
            {
                // Target loses bots, attacker gains a portion of them
                targetBotLoss = (long)Math.Ceiling(targetData.BotCount * (loserBotLossPercentage / 100.0));
                // Make sure losses are at least 1 bot and don't exceed the total
                targetBotLoss = Math.Max(1, Math.Min(targetBotLoss, targetData.BotCount));
                // Attacker gains the bots that target lost
                attackerBotGain = targetBotLoss;
            }
            else
            {
                // Attacker loses bots, target gains a portion of them
                attackerBotLoss = (long)Math.Ceiling(attackerData.BotCount * (loserBotLossPercentage / 100.0));
                // Make sure losses are at least 1 bot and don't exceed the total
                attackerBotLoss = Math.Max(1, Math.Min(attackerBotLoss, attackerData.BotCount));
                // Target gains the bots that attacker lost
                targetBotGain = attackerBotLoss;
            }

            // Simulate the battle over 10 seconds with 5 updates
            int totalUpdates = 5;
            int delayBetweenUpdates = 2000; // 2 seconds between updates

            for (int i = 1; i <= totalUpdates; i++)
            {
                // Wait for the specified delay
                await Task.Delay(delayBetweenUpdates);

                // Calculate progress percentage
                int progressPercentage = (i * 100) / totalUpdates;

                // Update the battle message
                embed = CreateBattleEmbed(attackerUsername, attackerData.BotCount, targetUsername, targetData.BotCount, progressPercentage, attackerData.GetAvatarUrl(), attackerWins);

                // Update the battle message in the channel
                await battleMessage.ModifyAsync(msg => msg.Embed = embed.Build());
            }

            // Wait a moment before showing the final result
            await Task.Delay(1000);

            // Apply the losses and gains
            if (attackerWins)
            {
                // Attacker wins, gains bots from target
                client.BotSystem.AddBots(attackerData.UserId, attackerBotGain);
                client.BotSystem.RemoveBots(targetData.UserId, targetBotLoss);

                // Record war statistics
                attackerData.RecordWarResult(true, attackerBotGain, 0, false); // false for player war
                targetData.RecordWarResult(false, 0, targetBotLoss, false); // false for player war

                // Record the attack for cooldown tracking
                attackerData.RecordAttack(targetData.UserId);

                // Get updated user data
                attackerData = client.BotSystem.GetUserData(attackerData.UserId);
                targetData = client.BotSystem.GetUserData(targetData.UserId);

                // Create the final battle result message
                var resultEmbed = CreateBattleResultEmbedNew(attackerUsername, attackerData.BotCount, 0, attackerBotGain,
                                                         targetUsername, targetData.BotCount, targetBotLoss, 0, true, false, attackerData.GetAvatarUrl());

                // Update the battle message in the channel
                await battleMessage.ModifyAsync(msg => msg.Embed = resultEmbed.Build());
            }
            else
            {
                // Target wins, attacker loses bots
                client.BotSystem.RemoveBots(attackerData.UserId, attackerBotLoss);
                client.BotSystem.AddBots(targetData.UserId, targetBotGain);

                // Record war statistics
                attackerData.RecordWarResult(false, 0, attackerBotLoss, false); // false for player war
                targetData.RecordWarResult(true, targetBotGain, 0, false); // false for player war

                // Record the attack for cooldown tracking
                attackerData.RecordAttack(targetData.UserId);

                // Get updated user data
                attackerData = client.BotSystem.GetUserData(attackerData.UserId);
                targetData = client.BotSystem.GetUserData(targetData.UserId);

                // Create the final battle result message
                var resultEmbed = CreateBattleResultEmbedNew(attackerUsername, attackerData.BotCount, attackerBotLoss, 0,
                                                         targetUsername, targetData.BotCount, 0, targetBotGain, false, guaranteedLoss, attackerData.GetAvatarUrl());

                // Update the battle message in the channel
                await battleMessage.ModifyAsync(msg => msg.Embed = resultEmbed.Build());
            }
        }
    }
}
