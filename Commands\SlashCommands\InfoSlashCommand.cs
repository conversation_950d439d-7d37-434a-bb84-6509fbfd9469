using Discord;
using Discord.WebSocket;
using BotChan.Services;
using BotChan.Models;
using System.Text;

namespace BotChan.Commands.SlashCommands
{
    /// <summary>
    /// InfoSlashCommand - Slash command version of the info command
    /// Usage: /info [item_id]
    /// Shows information about shop items
    /// </summary>
    public class InfoSlashCommand
    {
        /// <summary>
        /// Executes the info slash command.
        /// </summary>
        /// <param name="command">The slash command that was executed</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task ExecuteAsync(SocketSlashCommand command, DiscordBotClient client)
        {
            try
            {
                // Check cooldown before allowing the command (same as %info)
                if (!client.Permissions.HasPermission(command.User.Id, "info", out string cooldownMessage))
                {
                    if (!string.IsNullOrEmpty(cooldownMessage))
                    {
                        await command.RespondAsync($"⏰ **Info command is on cooldown!** {cooldownMessage}", ephemeral: true);
                    }
                    else
                    {
                        await command.RespondAsync("❌ You don't have permission to use this command.", ephemeral: true);
                    }
                    return;
                }

                // Update last usage time for cooldown tracking
                client.Permissions.UpdateCommandUsage(command.User.Id, "info");

                // Get all consumables that are visible in the shop, ordered by cost for consistency
                var consumables = client.BotSystem.GetAllConsumables().Where(c => c.IsVisibleInShop).OrderBy(c => c.Cost).ToList();

                // Check if an item ID or page was provided
                var itemIdOption = command.Data.Options.FirstOrDefault(x => x.Name == "item_id");
                var pageOption = command.Data.Options.FirstOrDefault(x => x.Name == "page");

                if (itemIdOption == null && pageOption == null)
                {
                    await ShowItemList(command, client, consumables, 1);
                    return;
                }

                // If page option is provided, show that page
                if (pageOption != null)
                {
                    if (!int.TryParse(pageOption.Value.ToString(), out int pageNumber))
                    {
                        await command.RespondAsync($"<:BotChanReaction:1410313252826779648> | **{command.User.Username}**, please provide a valid page number.", ephemeral: true);
                        return;
                    }
                    await ShowItemList(command, client, consumables, pageNumber);
                    return;
                }

                // If item ID is provided, show item details
                if (itemIdOption != null)
                {
                    // Try to parse the item ID
                    if (!int.TryParse(itemIdOption.Value.ToString(), out int itemId))
                {
                    await command.RespondAsync($"<:BotChanReaction:1410313252826779648> | **{command.User.Username}**, please provide a valid item ID. Use `/info` to see all available items.", ephemeral: true);
                    return;
                }

                if (itemId < 1 || itemId > consumables.Count)
                {
                    await command.RespondAsync($"<:BotChanReaction:1410313252826779648> | **{command.User.Username}**, please provide a valid item ID between 1 and {consumables.Count}. Use `/info` to see all available items.", ephemeral: true);
                    return;
                }

                // Get the consumable (adjust for 0-based index)
                var consumable = consumables[itemId - 1];

                // Show the item details
                await ShowItemDetails(command, consumable, itemId, client);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing info slash command: {ex.Message}");
                
                if (!command.HasResponded)
                {
                    await command.RespondAsync("❌ An error occurred while processing the info command.", ephemeral: true);
                }
            }
        }

        /// <summary>
        /// Shows a paginated list of available items (exact same as %info).
        /// </summary>
        private static async Task ShowItemList(SocketSlashCommand command, DiscordBotClient client, List<Consumable> consumables, int page = 1)
        {
            if (consumables.Count == 0)
            {
                await command.RespondAsync($"<:BotChanReaction:1410313252826779648> | **{command.User.Username}**, there are no items available in the shop yet.", ephemeral: true);
                return;
            }

            // Calculate pagination
            const int itemsPerPage = 10; // Standardized pagination
            int totalPages = (int)Math.Ceiling((double)consumables.Count / itemsPerPage);
            page = Math.Max(1, Math.Min(page, totalPages));

            var pageConsumables = consumables
                .Skip((page - 1) * itemsPerPage)
                .Take(itemsPerPage)
                .ToList();

            // Build description with shop-style formatting
            var description = new StringBuilder();
            description.AppendLine("**Available Items:**");
            description.AppendLine("Use `/info item_id:[number]` to get detailed information about a specific item.");
            description.AppendLine($"Use `/info page:[1-{totalPages}]` to navigate pages.");
            description.AppendLine("════════════════════════════════");

            // Add items with shop-style formatting and dashes
            int displayIndex = (page - 1) * itemsPerPage + 1;
            foreach (var consumable in pageConsumables)
            {
                string costDisplay = FormatNumber(consumable.Cost);
                string itemLine = $"{displayIndex} {consumable.Emoji} {consumable.Name}";

                // Calculate dashes for alignment (same as shop)
                int targetWidth = 40; // Target width for alignment
                int currentLength = GetDisplayLength(itemLine);
                int dashCount = Math.Max(1, targetWidth - currentLength);
                string dashes = new string('-', dashCount);

                description.AppendLine($"{itemLine}{dashes} {costDisplay} {Constants.Emojis.BotDown}");
                displayIndex++;
            }

            // Create the embed
            var embed = new EmbedBuilder()
                .WithAuthor("Bot-Chan's Item Database", command.User.GetAvatarUrl() ?? command.User.GetDefaultAvatarUrl())
                .WithTitle("📋 Available Items")
                .WithDescription(description.ToString())
                .WithColor(Color.Blue)
                .WithFooter($"Page {page}/{totalPages} | Use /info item_id:[number] for detailed information")
                .WithTimestamp(DateTimeOffset.Now);

            await command.RespondAsync(embed: embed.Build());
        }

        /// <summary>
        /// Shows detailed information about a specific item (exact same as %info).
        /// </summary>
        private static async Task ShowItemDetails(SocketSlashCommand command, Consumable consumable, int displayId, DiscordBotClient client)
        {
            // Determine item type and effect description
            string itemType;
            string effectDescription;
            string usageInstructions;

            if (consumable.Category == 1) // Multiplier
            {
                itemType = "Multiplier";
                effectDescription = $"Increases recruitment rewards by **{consumable.Multiplier * 100:F0}%** for {consumable.DefaultUses} uses";
                usageInstructions = $"1. Purchase with `/shop buy {displayId}`\n" +
                                   $"2. Activate with `/use [inventory_id]`\n" +
                                   $"3. Use `/recruit` to earn boosted rewards\n" +
                                   $"4. Multiplier expires after {consumable.DefaultUses} uses";
            }
            else if (consumable.Category == 2) // Bot Pack
            {
                itemType = "Bot Pack";
                effectDescription = $"Gives **{consumable.BotReward:N0}** bots instantly and can be stored on your inventory!";
                usageInstructions = $"1. Purchase with `/shop buy {displayId}`\n" +
                                   $"2. Use with `/use [inventory_id]`\n" +
                                   $"3. Receive {consumable.BotReward:N0} bots immediately\n" +
                                   $"4. Single-use item";
            }
            else if (consumable.Category == 3) // Bomb
            {
                if (consumable.BotReward < 0) // Gift Bomb
                {
                    itemType = "Gift Bomb";
                    long botsToGive = Math.Abs(consumable.BotReward);
                    effectDescription = $"Gives **{botsToGive:N0}** bots to another player (or yourself)";
                    usageInstructions = $"1. Purchase with `/shop buy {displayId}`\n" +
                                       $"2. Use with `/bomb [target_user_id] [inventory_id]`\n" +
                                       $"3. Transfers **{botsToGive:N0}** bots from you to the target\n" +
                                       $"4. Perfect for alliances and helping other players!";
                }
                else // Regular Attack Bomb
                {
                    itemType = "Bomb";
                    effectDescription = $"Attacks enemy territories with **{consumable.BombSuccessRate}%** success rate";
                    usageInstructions = $"1. Purchase with `/shop buy {displayId}`\n" +
                                       $"2. Use with `/bomb [target_user_id] [inventory_id]`\n" +
                                       $"3. **Success:** Destroys {consumable.BombWinPercentage}% of enemy bots\n" +
                                       $"4. **Failure:** You lose {consumable.BombLosePercentage}% of your own bots";
                }
            }
            else
            {
                itemType = "Unknown";
                effectDescription = "Effect unknown";
                usageInstructions = "Usage instructions not available";
            }

            // Create the detailed embed
            var embed = new EmbedBuilder()
                .WithTitle($"{consumable.Name} {consumable.Emoji}")
                .WithDescription(effectDescription)
                .WithColor(Color.Blue)
                .WithThumbnailUrl("https://cdn.discordapp.com/attachments/1363245551184908468/1363389520245952672/botchan_icon.png?ex=6805daf0&is=68048970&hm=38ddf686a16b86789e8b2e80f7db77ca40b0daa4ef0624fae373af3d0aeae0f7&")
                .AddField("📊 Item Details", 
                    $"**Type:** {itemType}\n" +
                    $"**Cost:** {consumable.Cost} {Constants.Emojis.BotDown}\n" +
                    $"**Item ID:** {displayId}", true)
                .AddField("📋 How to Use", usageInstructions, false)
                .WithFooter($"Use `/shop buy {displayId}` to purchase this item")
                .WithTimestamp(DateTimeOffset.Now);

            await command.RespondAsync(embed: embed.Build());
        }

        /// <summary>
        /// Calculates the display length of a string, treating Discord custom emojis as single characters.
        /// </summary>
        private static int GetDisplayLength(string text)
        {
            if (string.IsNullOrEmpty(text))
                return 0;

            // Replace Discord custom emojis with single characters for length calculation
            // Discord custom emojis have the format <:name:id> or <a:name:id>
            var customEmojiPattern = @"<a?:[^:]+:\d+>";
            var withoutCustomEmojis = System.Text.RegularExpressions.Regex.Replace(text, customEmojiPattern, "E");

            return withoutCustomEmojis.Length;
        }

        /// <summary>
        /// Formats a number with appropriate suffixes (K, M, B, T) - matches shop formatting
        /// </summary>
        private static string FormatNumber(long number)
        {
            if (number >= 1_000_000_000_000) // Trillion
                return $"{number / 1_000_000_000_000}T";
            if (number >= 1_000_000_000) // Billion
                return $"{number / 1_000_000_000}B";
            if (number >= 1_000_000) // Million
                return $"{number / 1_000_000}M";
            if (number >= 1_000) // Thousand
                return $"{number / 1_000}K";
            return number.ToString();
        }
    }
}
