using Discord;
using Discord.WebSocket;
using BotChan.Services;
using BotChan.Models;

namespace BotChan.Commands.SlashCommands
{
    /// <summary>
    /// DismantleSlashCommand - Slash command version of the dismantle command
    /// Usage: /dismantle
    /// Remove your currently active recruitment multiplier
    /// </summary>
    public class DismantleSlashCommand
    {
        // Dictionary to track pending dismantle confirmations for slash commands
        private static readonly Dictionary<ulong, (string ConsumableId, string ConsumableName, string ConsumableEmoji, DateTime Expiry)> _pendingConfirmations = new Dictionary<ulong, (string, string, string, DateTime)>();

        // Timeout for confirmations (30 seconds)
        private const int ConfirmationTimeoutSeconds = 30;

        // Static reference to the client for button handling
        private static DiscordBotClient _clientInstance;

        // Flag to track if the button handler has been registered
        private static bool _buttonHandlerRegistered = false;

        /// <summary>
        /// Executes the dismantle slash command.
        /// </summary>
        /// <param name="command">The slash command that was executed</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task ExecuteAsync(SocketSlashCommand command, DiscordBotClient client)
        {
            try
            {
                // Check cooldown before allowing the command (same as %dismantle)
                if (!client.Permissions.HasPermission(command.User.Id, "dismantle", out string cooldownMessage))
                {
                    if (!string.IsNullOrEmpty(cooldownMessage))
                    {
                        await command.RespondAsync($"⏰ **Dismantle command is on cooldown!** {cooldownMessage}", ephemeral: true);
                    }
                    else
                    {
                        await command.RespondAsync("❌ You don't have permission to use this command.", ephemeral: true);
                    }
                    return;
                }

                // Update last usage time for cooldown tracking
                client.Permissions.UpdateCommandUsage(command.User.Id, "dismantle");

                // Register the button handler if not already done
                if (!_buttonHandlerRegistered)
                {
                    _clientInstance = client;
                    client._client.ButtonExecuted += OnButtonExecuted;
                    _buttonHandlerRegistered = true;
                }

                // Get the user's data
                var userData = client.BotSystem.GetUserData(command.User.Id, command.User.Username);

                // Check if the user has an active multiplier
                if (string.IsNullOrEmpty(userData.ActiveConsumableId) || userData.ActiveConsumableUsesRemaining <= 0)
                {
                    await command.RespondAsync($"**<:BotChanDespair:1410316642180796446> | {command.User.Username}**, you don't have a multiplier currently active. This command is used to dismantle multipliers for %recruit command! Please use a multiplier and try again!", ephemeral: true);
                    return;
                }

                // Get the active consumable details
                var activeConsumable = client.BotSystem.GetConsumable(userData.ActiveConsumableId);
                if (activeConsumable == null)
                {
                    await command.RespondAsync($"**<:BotChanDespair:1410316642180796446> | {command.User.Username}**, there was an error retrieving your active multiplier information. Please try again.", ephemeral: true);
                    return;
                }

                // Store the confirmation data
                _pendingConfirmations[command.User.Id] = (
                    userData.ActiveConsumableId,
                    activeConsumable.Name,
                    activeConsumable.Emoji,
                    DateTime.UtcNow.AddSeconds(ConfirmationTimeoutSeconds)
                );

                // Create buttons for confirmation
                var dismantleButton = new ButtonBuilder()
                    .WithLabel("Dismantle")
                    .WithStyle(ButtonStyle.Danger)
                    .WithCustomId($"dismantle_slash_confirm_{command.User.Id}")
                    .WithEmote(new Emoji("🗑️"));

                var keepButton = new ButtonBuilder()
                    .WithLabel("Keep")
                    .WithStyle(ButtonStyle.Success)
                    .WithCustomId($"dismantle_slash_cancel_{command.User.Id}")
                    .WithEmote(new Emoji("✅"));

                var components = new ComponentBuilder()
                    .WithButton(dismantleButton)
                    .WithButton(keepButton)
                    .Build();

                // Send confirmation message with buttons
                await command.RespondAsync(
                    text: $"**<:BotChanReaction:1410313252826779648> | {command.User.Username}**, would you like to dismantle the current multiplier item? You're currently using **{activeConsumable.Name}** {activeConsumable.Emoji}. The item will be lost permanently!",
                    components: components);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing dismantle slash command: {ex.Message}");
                
                if (!command.HasResponded)
                {
                    await command.RespondAsync("❌ An error occurred while processing the dismantle command.", ephemeral: true);
                }
            }
        }

        /// <summary>
        /// Handles button interactions for dismantle confirmations
        /// </summary>
        private static async Task OnButtonExecuted(SocketMessageComponent component)
        {
            try
            {
                var customId = component.Data.CustomId;
                var userId = component.User.Id;

                // Check if this is a dismantle slash button
                if (!customId.StartsWith("dismantle_slash_"))
                    return;

                // Extract the user ID from the custom ID
                var parts = customId.Split('_');
                if (parts.Length != 4 || !ulong.TryParse(parts[3], out var targetUserId))
                    return;

                // Only allow the original user to interact with their buttons
                if (userId != targetUserId)
                {
                    await component.RespondAsync("❌ You can only interact with your own dismantle confirmation.", ephemeral: true);
                    return;
                }

                // Check if the confirmation still exists and hasn't expired
                if (!_pendingConfirmations.TryGetValue(userId, out var confirmation) || DateTime.UtcNow > confirmation.Expiry)
                {
                    _pendingConfirmations.Remove(userId);
                    await component.RespondAsync("❌ This dismantle confirmation has expired. Please use the command again.", ephemeral: true);
                    return;
                }

            var action = parts[2]; // "confirm" or "cancel"

            if (action == "confirm")
            {
                // Remove the confirmation
                _pendingConfirmations.Remove(userId);

                // Get the user's data
                var userData = _clientInstance.BotSystem.GetUserData(userId);

                // Verify the user still has the same active consumable
                if (userData.ActiveConsumableId != confirmation.ConsumableId)
                {
                    await component.RespondAsync("❌ Your active multiplier has changed since the confirmation was requested. Please try again.", ephemeral: true);
                    return;
                }

                // Remove the active consumable
                userData.ActiveConsumableId = string.Empty;
                userData.ActiveConsumableUsesRemaining = 0;
                userData.ActiveConsumableMultiplier = 1.0;

                // Save the user data
                _clientInstance.BotSystem.SaveUserData();

                // Update the message to show completion with error handling
                try
                {
                    await component.UpdateAsync(msg =>
                    {
                        msg.Content = $"**<a:BotChanHappy:1410317358852997233> | {component.User.Username}**, you have successfully dismantled **{confirmation.ConsumableName}** {confirmation.ConsumableEmoji}! Your multiplier has been removed.";
                        msg.Components = new ComponentBuilder().Build(); // Remove buttons
                    });
                }
                catch (Exception updateEx)
                {
                    Console.WriteLine($"Failed to update dismantle message: {updateEx.Message}");
                    // Fallback: Try to respond instead if update fails
                    if (!component.HasResponded)
                    {
                        await component.RespondAsync($"**<a:BotChanHappy:1410317358852997233> | {component.User.Username}**, you have successfully dismantled **{confirmation.ConsumableName}** {confirmation.ConsumableEmoji}! Your multiplier has been removed.", ephemeral: true);
                    }
                }
            }
            else if (action == "cancel")
            {
                // Remove the confirmation
                _pendingConfirmations.Remove(userId);

                // Update the message to show cancellation with error handling
                try
                {
                    await component.UpdateAsync(msg =>
                    {
                        msg.Content = $"**<a:BotChanHappy:1410317358852997233> | {component.User.Username}**, dismantle cancelled. Your **{confirmation.ConsumableName}** {confirmation.ConsumableEmoji} multiplier remains active.";
                        msg.Components = new ComponentBuilder().Build(); // Remove buttons
                    });
                }
                catch (Exception updateEx)
                {
                    Console.WriteLine($"Failed to update dismantle cancellation message: {updateEx.Message}");
                    // Fallback: Try to respond instead if update fails
                    if (!component.HasResponded)
                    {
                        await component.RespondAsync($"**<a:BotChanHappy:1410317358852997233> | {component.User.Username}**, dismantle cancelled. Your **{confirmation.ConsumableName}** {confirmation.ConsumableEmoji} multiplier remains active.", ephemeral: true);
                    }
                }
            }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in dismantle button handler: {ex.Message}");

                // Try to respond to the interaction if it hasn't been responded to yet
                try
                {
                    if (!component.HasResponded)
                    {
                        await component.RespondAsync("❌ An error occurred while processing your dismantle request.", ephemeral: true);
                    }
                }
                catch
                {
                    // If we can't respond, just log it
                    Console.WriteLine("Failed to send error response for dismantle button interaction");
                }
            }
        }
    }
}
