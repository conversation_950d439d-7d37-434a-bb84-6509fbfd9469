using Discord;
using Discord.WebSocket;
using BotChan.Services;
using BotChan.Models;

namespace BotChan.Commands.SlashCommands
{
    /// <summary>
    /// UseSlashCommand - Slash command version of the use command
    /// Usage: /use item_id
    /// Use a consumable from the user's inventory
    /// </summary>
    public class UseSlashCommand
    {
        /// <summary>
        /// Executes the use slash command.
        /// </summary>
        /// <param name="command">The slash command that was executed</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task ExecuteAsync(SocketSlashCommand command, DiscordBotClient client)
        {
            try
            {
                // Check cooldown before allowing the command (same as %use)
                if (!client.Permissions.HasPermission(command.User.Id, "use", out string cooldownMessage))
                {
                    if (!string.IsNullOrEmpty(cooldownMessage))
                    {
                        await command.RespondAsync($"⏰ **Use command is on cooldown!** {cooldownMessage}", ephemeral: true);
                    }
                    else
                    {
                        await command.RespondAsync("❌ You don't have permission to use this command.", ephemeral: true);
                    }
                    return;
                }

                // Update last usage time for cooldown tracking
                client.Permissions.UpdateCommandUsage(command.User.Id, "use");

                // Get the user's data
                var userData = client.BotSystem.GetUserData(command.User.Id);

                // Check if the user has any items in their inventory
                if (userData.Inventory.Count == 0)
                {
                    await command.RespondAsync($"<:BChanSmug:1410316817939042334> | {command.User.Username}, You do not have any items to use to begin with. Go to my shop to get some!", ephemeral: true);
                    return;
                }

                // Get command options
                var itemIdOption = command.Data.Options.FirstOrDefault(x => x.Name == "item_id");

                if (itemIdOption == null)
                {
                    await command.RespondAsync("Please specify an inventory ID to use. Example: `/use item_id:1`", ephemeral: true);
                    return;
                }

                // Try to parse the inventory ID
                if (!int.TryParse(itemIdOption.Value.ToString(), out int inventoryId))
                {
                    await command.RespondAsync("Invalid inventory ID. Please provide a numeric ID. Use `/inventory` to see your consumables.", ephemeral: true);
                    return;
                }

                // Check if the user already has an active consumable (only for multiplier items)
                if (!string.IsNullOrEmpty(userData.ActiveConsumableId))
                {
                    var activeConsumable = client.BotSystem.GetConsumable(userData.ActiveConsumableId);
                    if (activeConsumable != null)
                    {
                        // Get the consumable the user is trying to use
                        var itemToUse = userData.GetConsumableByInventoryId(inventoryId);
                        if (itemToUse != null)
                        {
                            var consumableToUse = client.BotSystem.GetConsumable(itemToUse.ConsumableId);

                            // If it's a bot reward item (not a consumable), allow it to be used
                            if (consumableToUse != null && consumableToUse.BotReward > 0)
                            {
                                // This is a bot reward item, so we'll allow it to be used even with an active consumable
                            }
                            else
                            {
                                // This is a consumable item, so we can't use it while another is active
                                await command.RespondAsync($"<:BotChanReaction:1410313252826779648> | Oops {command.User.Username}, it seems like you already have a consumable active. You cannot activate another consumable until this one is fully used!", ephemeral: true);
                                return;
                            }
                        }
                    }
                    else
                    {
                        // If the active consumable no longer exists, clear it
                        userData.ActiveConsumableId = string.Empty;
                        client.BotSystem.SaveUserData();
                    }
                }

                // Check if the user has the consumable with the given inventory ID
                var userConsumable = userData.GetConsumableByInventoryId(inventoryId);
                if (userConsumable == null)
                {
                    await command.RespondAsync($"You don't have a consumable with inventory ID `{inventoryId}`. Use `/inventory` to see your consumables.", ephemeral: true);
                    return;
                }

                // Get the consumable details
                var consumable = client.BotSystem.GetConsumable(userConsumable.ConsumableId);
                if (consumable == null)
                {
                    await command.RespondAsync($"Error: Consumable not found in the system.", ephemeral: true);
                    return;
                }

                // Check if this is a bomb item (Category 3)
                if (consumable.Category == 3)
                {
                    await command.RespondAsync($"**<:BotChanDespair:1410316642180796446> | {command.User.Username}**, bomb items cannot be used with the `/use` command. Use `/bomb target:[target_user_id] item_id:[inventory_id]` instead to attack other players.", ephemeral: true);
                    return;
                }

                // Check if this is a bot reward item or a consumable
                if (consumable.Category == 2) // Bot Pack
                {
                    // This is a bot reward item - give the bots immediately (exact same as %use)
                    long botsAwarded = consumable.BotReward;

                    // Add the bots to the user's account
                    client.BotSystem.AddBots(command.User.Id, botsAwarded);

                    // Create the success message for bot reward item (exact same as %use)
                    string responseMessage = $"<a:BotChanHappy:1410317358852997233> ✨ | {command.User.Username}, you used **{consumable.Name}** {consumable.Emoji} and received **{botsAwarded}** <:BotDown:1410320403469176913>!\n\n" +
                        $"Your new bot count: **{userData.BotCount}** <:BotDown:1410320403469176913>";

                    // Send the response
                    await command.RespondAsync(responseMessage);

                    // Remove the consumable from inventory immediately
                    userData.Inventory.Remove(userConsumable);

                    // Save the user data
                    client.BotSystem.SaveUserData();
                }
                else
                {
                    // This is a consumable item (exact same as %use)
                    // Create the success message for consumable
                    string responseMessage = $"<a:BotChanHappy:1410317358852997233> ✨ | {command.User.Username}, you activated the following item!\n" +
                        $"**{consumable.Name}** {consumable.Emoji} (x{consumable.Multiplier} multiplier for {userConsumable.UsesRemaining} uses)\n\n" +
                        $"**Note**\n" +
                        $"This consumable will remain active until all uses are exhausted. You cannot activate another consumable until this one is fully used.";

                    // Send the response
                    await command.RespondAsync(responseMessage);

                    // Set this consumable as the active one and store its properties
                    userData.ActiveConsumableId = userConsumable.ConsumableId;
                    userData.ActiveConsumableUsesRemaining = userConsumable.UsesRemaining;
                    userData.ActiveConsumableMultiplier = consumable.Multiplier;

                    // Remove the consumable from inventory immediately
                    userData.Inventory.Remove(userConsumable);

                    // Save the user data
                    client.BotSystem.SaveUserData();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing use slash command: {ex.Message}");
                
                if (!command.HasResponded)
                {
                    await command.RespondAsync("❌ An error occurred while processing the use command.", ephemeral: true);
                }
            }
        }
    }
}
