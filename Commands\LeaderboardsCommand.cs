using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;
using System.Text;

namespace BotChan.Commands
{
    /// <summary>
    /// LeaderboardsCommand - A command that shows the global leaderboard of users ranked by bot count
    /// Usage: %leaderboards
    /// Example: %leaderboards
    /// </summary>
    public class LeaderboardsCommand : AliasCommand
    {
        // Number of users to display per page
        private const int UsersPerPage = 10;

        // Override the default category
        public override string Category => "Economy";

        // Override the default usage
        public override string Usage => "%leaderboards\n%leaderboards page [number]";

        // Override the default examples
        public override string Examples => "%leaderboards\n%leaderboards page 2";

        public LeaderboardsCommand() : base("leaderboards", "View the global bot leaderboards")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Check if the user wants to navigate to a specific page
            if (args.Length > 1 && args[1].ToLower() == "page")
            {
                if (args.Length < 3 || !int.TryParse(args[2], out int pageNumber))
                {
                    await message.Channel.SendMessageAsync("Please specify a valid page number. Example: `%leaderboards page 2`");
                    return;
                }

                // Show the specified page
                await ShowLeaderboards(message, pageNumber, client);
                return;
            }

            // Otherwise, show the first page
            await ShowLeaderboards(message, 1, client);
        }

        private async Task ShowLeaderboards(SocketMessage message, int page, DiscordBotClient client)
        {
            // Get all user data
            var allUsers = client.BotSystem.GetAllUserData()
                .OrderByDescending(u => u.BotCount)
                .ToList();

            // Calculate total pages
            int totalPages = (int)Math.Ceiling(allUsers.Count / (double)UsersPerPage);
            if (totalPages == 0) totalPages = 1; // At least one page even if empty

            // Ensure page is within valid range
            page = Math.Max(1, Math.Min(page, totalPages));

            // Get users for the current page
            var pageUsers = allUsers
                .Skip((page - 1) * UsersPerPage)
                .Take(UsersPerPage)
                .ToList();

            // Find the current user's rank
            int userRank = allUsers.FindIndex(u => u.UserId == message.Author.Id) + 1;
            long userBotCount = allUsers.FirstOrDefault(u => u.UserId == message.Author.Id)?.BotCount ?? 0;

            // Build the leaderboards message content
            var sb = new StringBuilder();

            // Add navigation instructions if there are multiple pages
            if (totalPages > 1)
            {
                sb.AppendLine($"%leaderboards page [1-{totalPages}] to navigate pages");
            }

            sb.AppendLine("════════════════════════════════");

            // Add the users
            for (int i = 0; i < pageUsers.Count; i++)
            {
                var userData = pageUsers[i];
                int rank = (page - 1) * UsersPerPage + i + 1; // Calculate the rank
                string formattedBotCount = FormatNumber(userData.BotCount);

                // Get the username from stored data
                string username = userData.GetDisplayName();

                // Try to update user info if possible
                var user = client._client.GetUser(userData.UserId);
                if (user != null)
                {
                    // Update the user information in the database
                    client.BotSystem.GetUserData(user);
                    username = user.Username;
                }

                // Format: Rank Username----------------------- BotCount :BotDown:
                // Calculate dashes needed for alignment
                int dashCount = Math.Max(1, 40 - username.Length - formattedBotCount.Length);
                string dashes = new string('-', dashCount);

                sb.AppendLine($"{rank} {username}{dashes} {formattedBotCount} {Constants.Emojis.BotDown}");
            }

            // Add a blank line at the end for spacing
            sb.AppendLine();

            // Create an embed for the leaderboards
            var embed = new EmbedBuilder()
                .WithAuthor("Global Leaderboards", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                .WithDescription(sb.ToString())
                .WithColor(Color.Gold)
                .WithFooter($"Page {page}/{totalPages} | You are #{userRank} with {userBotCount} bots");

            // Send the embed message
            var leaderboardsMessage = await message.Channel.SendMessageAsync(embed: embed.Build());
        }

        /// <summary>
        /// Formats a number with K, M, B suffixes for thousands, millions, billions
        /// </summary>
        private string FormatNumber(long number)
        {
            if (number >= 1_000_000_000)
                return $"{number / 1_000_000_000}B";
            if (number >= 1_000_000)
                return $"{number / 1_000_000}M";
            if (number >= 1_000)
                return $"{number / 1_000}K";
            return number.ToString();
        }
    }
}
