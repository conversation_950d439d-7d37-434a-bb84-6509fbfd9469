namespace BotChan.Models
{
    /// <summary>
    /// Contains constant values used throughout the application.
    /// Centralizing these values makes it easier to update them in the future.
    /// </summary>
    public static class Constants
    {
        /// <summary>
        /// URLs for images and other resources
        /// </summary>
        public static class Urls
        {
            /// <summary>
            /// URL for the BotDown emoji used in command embeds
            /// </summary>
            public const string BotDownEmoji = "BotDown";

            /// <summary>
            /// Alternative URL for bot icon if the emoji doesn't work
            /// </summary>
            public const string BotIcon = "https://cdn.discordapp.com/attachments/1363245551184908468/1363245612967006299/Bot_Down.png?ex=680554ea&is=6804036a&hm=c5e1b572c6a0d4a8fd4f8e4dc4be4540cdf9a5448c96b89ef209518f29e282bb&";

            // Add more URLs as needed
        }

        /// <summary>
        /// Color values used in embeds
        /// </summary>
        public static class Colors
        {
            // Add color constants if needed
        }

        /// <summary>
        /// Command-related constants
        /// </summary>
        public static class Commands
        {
            // Add command-related constants if needed
        }

        /// <summary>
        /// Discord emoji IDs and formatted strings
        /// </summary>
        public static class Emojis
        {
            /// <summary>
            /// BotDown emoji formatted for Discord messages (currency symbol)
            /// </summary>
            public const string BotDown = "<:BotDown:1410320403469176913>";


        }
    }
}
