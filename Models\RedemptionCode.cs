using System;
using System.Collections.Generic;

namespace BotChan.Models
{
    /// <summary>
    /// Represents a redemption code that can be used to obtain items.
    /// </summary>
    public class RedemptionCode
    {
        /// <summary>
        /// The unique identifier for this code.
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// The code that users will enter to redeem the reward.
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// The ID of the consumable item to give when this code is redeemed.
        /// </summary>
        public string ConsumableId { get; set; } = string.Empty;

        /// <summary>
        /// The quantity of the consumable to give.
        /// </summary>
        public int Quantity { get; set; } = 1;

        /// <summary>
        /// The list of user IDs that have already redeemed this code.
        /// </summary>
        public List<ulong> RedeemedBy { get; set; } = new List<ulong>();

        /// <summary>
        /// The date and time when this code expires. If null, the code never expires.
        /// </summary>
        public DateTime? ExpiryDate { get; set; } = null;

        /// <summary>
        /// Checks if this code has expired.
        /// </summary>
        /// <returns>True if the code has expired, false otherwise.</returns>
        public bool HasExpired()
        {
            return ExpiryDate.HasValue && DateTime.UtcNow > ExpiryDate.Value;
        }

        /// <summary>
        /// Checks if a user has already redeemed this code.
        /// </summary>
        /// <param name="userId">The ID of the user to check.</param>
        /// <returns>True if the user has already redeemed this code, false otherwise.</returns>
        public bool HasBeenRedeemedBy(ulong userId)
        {
            return RedeemedBy.Contains(userId);
        }

        /// <summary>
        /// Marks this code as redeemed by a user.
        /// </summary>
        /// <param name="userId">The ID of the user who redeemed the code.</param>
        public void MarkAsRedeemed(ulong userId)
        {
            if (!RedeemedBy.Contains(userId))
            {
                RedeemedBy.Add(userId);
            }
        }
    }
}
