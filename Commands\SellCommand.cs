using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;
using System.Text;

namespace BotChan.Commands
{
    /// <summary>
    /// SellCommand - A command that allows users to sell items from their inventory
    /// Usage: %sell [inventory_id]
    /// Example: %sell 1
    /// </summary>
    public class SellCommand : Command
    {
        // Override the default category
        public override string Category => "Economy";

        // Override the default usage
        public override string Usage => "%sell [inventory_id]";

        // Override the default examples
        public override string Examples => "%sell 1\n%sell 5";

        // Dictionary to track pending sell confirmations
        private static readonly Dictionary<ulong, (int InventoryId, string ItemName, string ItemEmoji, long SellPrice, DateTime Expiry)> _pendingConfirmations = new Dictionary<ulong, (int, string, string, long, DateTime)>();

        // Timeout for confirmations (30 seconds)
        private const int ConfirmationTimeoutSeconds = 30;

        // Static reference to the client for button handling
        private static DiscordBotClient? _clientInstance;

        // Flag to track if the button handler has been registered
        private static bool _buttonHandlerRegistered = false;

        public SellCommand() : base("sell", "Sell items from your inventory for 50% of their original price")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Register the button handler if not already done
            if (!_buttonHandlerRegistered)
            {
                _clientInstance = client;
                client._client.ButtonExecuted += OnButtonExecuted;
                _buttonHandlerRegistered = true;
            }

            // Check if an inventory ID was provided
            if (args.Length < 2)
            {
                await message.Channel.SendMessageAsync("Please specify an inventory ID to sell. Use `%inventory` to see your items and their IDs.\n\nExample: `%sell 1`");
                return;
            }

            // Try to parse the ID as an integer
            if (!int.TryParse(args[1], out int inventoryId))
            {
                await message.Channel.SendMessageAsync("Invalid inventory ID. Please provide a numeric ID. Example: `%sell 1`");
                return;
            }

            await ShowSellConfirmation(message, inventoryId, client);
        }

        private async Task ShowSellConfirmation(SocketMessage message, int inventoryId, DiscordBotClient client)
        {
            // Get the user's data
            var userData = client.BotSystem.GetUserData(message.Author.Id, message.Author.Username);

            // Find the item in the user's inventory
            var inventoryItem = userData.Inventory.FirstOrDefault(item => item.InventoryId == inventoryId);
            if (inventoryItem == null)
            {
                await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1142948885694787684> | {message.Author.Username}**, you don't have an item with inventory ID {inventoryId}. Use `%inventory` to see your items.");
                return;
            }

            // Get the consumable details
            var consumable = client.BotSystem.GetConsumable(inventoryItem.ConsumableId);
            if (consumable == null)
            {
                await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1142948885694787684> | {message.Author.Username}**, there was an error retrieving the item information. Please try again.");
                return;
            }

            // Calculate sell price (50% of original cost)
            long sellPrice = consumable.Cost / 2;

            // Check if the item is currently active
            if (userData.ActiveConsumableId == consumable.Id && userData.ActiveConsumableUsesRemaining > 0)
            {
                await message.Channel.SendMessageAsync($"**<:BM_BotChanDespair:1142948885694787684> | {message.Author.Username}**, you cannot sell an item that is currently active. Please use `%dismantle` to remove the active multiplier first, or wait for it to expire.");
                return;
            }

            // Store the confirmation data
            _pendingConfirmations[message.Author.Id] = (
                inventoryId,
                consumable.Name,
                consumable.Emoji,
                sellPrice,
                DateTime.UtcNow.AddSeconds(ConfirmationTimeoutSeconds)
            );

            // Create buttons for confirmation
            var sellButton = new ButtonBuilder()
                .WithLabel("Sell")
                .WithStyle(ButtonStyle.Danger)
                .WithCustomId($"sell_confirm_{message.Author.Id}")
                .WithEmote(new Emoji("💰"));

            var keepButton = new ButtonBuilder()
                .WithLabel("Keep")
                .WithStyle(ButtonStyle.Success)
                .WithCustomId($"sell_cancel_{message.Author.Id}")
                .WithEmote(new Emoji("✅"));

            var components = new ComponentBuilder()
                .WithButton(sellButton)
                .WithButton(keepButton)
                .Build();

            // Send confirmation message with buttons
            await message.Channel.SendMessageAsync(
                text: $"**<:BM_BotChanReaction:1143303450369736754> | {message.Author.Username}**, are you sure you want to sell **{consumable.Name}** {consumable.Emoji} for **{sellPrice:N0}** {Constants.Emojis.BotDown}? This action cannot be undone!",
                components: components);
        }

        /// <summary>
        /// Handles button interactions for sell confirmations
        /// </summary>
        private static async Task OnButtonExecuted(SocketMessageComponent component)
        {
            var customId = component.Data.CustomId;
            var userId = component.User.Id;

            // Check if this is a sell button
            if (!customId.StartsWith("sell_"))
                return;

            // Extract the user ID from the custom ID
            var parts = customId.Split('_');
            if (parts.Length != 3 || !ulong.TryParse(parts[2], out var targetUserId))
                return;

            // Only allow the original user to interact with their buttons
            if (userId != targetUserId)
            {
                await component.RespondAsync("❌ You can only interact with your own sell confirmation.", ephemeral: true);
                return;
            }

            // Check if there's a pending confirmation for this user
            if (!_pendingConfirmations.TryGetValue(userId, out var confirmationData))
            {
                await component.RespondAsync("❌ This sell confirmation has expired or is no longer valid.", ephemeral: true);
                return;
            }

            // Check if the confirmation has expired
            if (DateTime.UtcNow > confirmationData.Expiry)
            {
                _pendingConfirmations.Remove(userId);
                await component.RespondAsync("❌ This sell confirmation has expired. Please try again.", ephemeral: true);
                return;
            }

            var action = parts[1]; // "confirm" or "cancel"

            if (action == "confirm")
            {
                // Check if client instance is available
                if (_clientInstance == null)
                {
                    await component.RespondAsync("❌ An error occurred. Please try again.", ephemeral: true);
                    return;
                }

                // Perform the sell
                var userData = _clientInstance.BotSystem.GetUserData(userId, component.User.Username);

                // Find and remove the item from inventory
                var inventoryItem = userData.Inventory.FirstOrDefault(item => item.InventoryId == confirmationData.InventoryId);
                if (inventoryItem != null)
                {
                    userData.Inventory.Remove(inventoryItem);

                    // Add bots to the user's account
                    userData.BotCount += confirmationData.SellPrice;

                    // Save the user data
                    _clientInstance.BotSystem.SaveUserData();

                    // Create success embed
                    var description = new StringBuilder();
                    description.AppendLine($"You've sold **{confirmationData.ItemName}** {confirmationData.ItemEmoji} for **{confirmationData.SellPrice:N0}** {Constants.Emojis.BotDown}!");
                    description.AppendLine();
                    description.AppendLine($"**New Bot Count:** {userData.BotCount:N0} {Constants.Emojis.BotDown}");

                    var embed = new EmbedBuilder()
                        .WithTitle("💰 Item Sold!")
                        .WithDescription(description.ToString())
                        .WithColor(Color.Green)
                        .WithThumbnailUrl(component.User.GetAvatarUrl() ?? component.User.GetDefaultAvatarUrl())
                        .WithTimestamp(DateTimeOffset.Now);

                    await component.RespondAsync(embed: embed.Build());
                }
                else
                {
                    await component.RespondAsync($"**<:BM_BotChanDespair:1142948885694787684> | {component.User.Username}**, the item no longer exists in your inventory.", ephemeral: true);
                }
            }
            else if (action == "cancel")
            {
                await component.RespondAsync($"**<:BM_BotChanReaction:1143303450369736754> | {component.User.Username}**, you've decided to keep **{confirmationData.ItemName}** {confirmationData.ItemEmoji}. The item remains in your inventory.");
            }

            // Remove the confirmation data
            _pendingConfirmations.Remove(userId);
        }
    }
}
