using System.Text.Json.Serialization;

namespace BotChan.Models
{
    /// <summary>
    /// Represents a single command cooldown entry.
    /// </summary>
    public class CooldownEntry
    {
        /// <summary>
        /// The user ID and command name combined as a key.
        /// </summary>
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// The timestamp when the command was last used.
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// Stores cooldown data that can be serialized to JSON.
    /// </summary>
    public class CooldownData
    {
        /// <summary>
        /// List of cooldown entries.
        /// </summary>
        public List<CooldownEntry> Entries { get; set; } = new List<CooldownEntry>();
    }
}
