using Discord;
using Discord.WebSocket;
using BotChan.Services;

namespace BotChan.Commands.SlashCommands
{
    /// <summary>
    /// DeclareWarSlashCommand - Slash command version that redirects to Discord server
    /// Usage: /declarewar
    /// Redirects users to the Discord server where the full command should be used
    /// </summary>
    public class DeclareWarSlashCommand
    {
        /// <summary>
        /// Executes the declare war slash command.
        /// </summary>
        /// <param name="command">The slash command that was executed</param>
        /// <param name="client">Reference to the bot client</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public static async Task ExecuteAsync(SocketSlashCommand command, DiscordBotClient client)
        {
            try
            {
                // Create the redirect embed
                var embed = new EmbedBuilder()
                    .WithTitle("⚔️ Declare War - Discord Server Required")
                    .WithDescription("The declare war feature is only available in our Discord server!")
                    .AddField("🏰 Join Our Server", "You need to be in our Discord server to use this command: https://discord.gg/DHbChVj")
                    .AddField("🎮 Why Discord Server Only?", 
                        "• Full battle system with real-time updates\n" +
                        "• Community interaction and spectating\n" +
                        "• Advanced war mechanics and features\n" +
                        "• Better moderation and fair play")
                    .AddField("📋 How to Use", 
                        "1. Join our Discord server using the link above\n" +
                        "2. Use `%declarewar [target]` in the server\n" +
                        "3. Enjoy the full war experience!")
                    .WithColor(Color.Orange)
                    .WithFooter("Join our server for the complete Bot Mania! Empires Rise! experience!")
                    .WithTimestamp(DateTimeOffset.Now);

                // Respond with the embed
                await command.RespondAsync(embed: embed.Build(), ephemeral: true);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing declare war slash command: {ex.Message}");

                if (!command.HasResponded)
                {
                    await command.RespondAsync("❌ An error occurred. Please join our Discord server to use the declare war feature: https://discord.gg/DHbChVj", ephemeral: true);
                }
            }
        }
    }
}
