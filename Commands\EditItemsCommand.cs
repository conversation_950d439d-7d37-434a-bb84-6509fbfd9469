using Discord;
using Discord.WebSocket;
using BotChan.Models;
using BotChan.Services;
using System.Text;
using System.Text.RegularExpressions;

namespace BotChan.Commands
{
    /// <summary>
    /// EditItemsCommand - A command that allows staff to manage items
    /// Usage: %edititems
    /// Example: %edititems
    /// </summary>
    public class EditItemsCommand : Command
    {
        // Number of items to display per page (reduced to prevent Discord text limit)
        private const int ItemsPerPage = 5;

        // Override the default category
        public override string Category => "Admin";

        // Override the default usage
        public override string Usage =>
            "%edititems - Show all items\n" +
            "%edititems page [number] - Show page\n" +
            "%edititems remove [id] - Remove item\n\n" +
            "**Parameter Names:** [mult]=multiplier, [desc]=description, [bots]=botreward, [succ%]=success rate, [win%]=enemy loss %, [lose%]=your loss %, [vis]=visible\n\n" +
            "**ADD/EDIT SYNTAX:**\n" +
            "**Multipliers (Cat 1):** `[id] 1 [name] [desc] [mult] 0 0 0 0 [cost] [uses] [emoji] [vis]`\n" +
            "**Bot Packs (Cat 2):** `[id] 2 [name] [desc] 1 [bots] 0 0 0 [cost] [uses] [emoji] [vis]`\n" +
            "**Bombs (Cat 3):** `[id] 3 [name] [desc] 1 0 [succ%] [win%] [lose%] [cost] [uses] [emoji] [vis]`\n\n" +
            "Categories: 1=Multipliers, 2=Bot Packs, 3=Bombs";

        // Override the default examples
        public override string Examples =>
            "%edititems\n" +
            "%edititems page 2\n" +
            "%edititems add boost 1 \"Boost\" \"2x multiplier\" 2 0 0 0 0 50 5 ⭐ true\n" +
            "%edititems add pack 2 \"Pack\" \"1000 bots\" 1 1000 0 0 0 150 1 🤖 true\n" +
            "%edititems add bomb 3 \"Bomb\" \"Attack item\" 1 0 80 15 5 300 1 💣 true\n" +
            "%edititems edit boost 1 \"Super Boost\" \"5x multiplier\" 5 0 0 0 0 200 3 💥 true\n" +
            "%edititems remove old_item";

        public EditItemsCommand() : base("edititems", "Manage items (staff only)")
        {
        }

        public override async Task ExecuteAsync(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Check if the user is a staff member
            if (!client.Permissions.IsStaffUser(message.Author.Id))
            {
                await message.Channel.SendMessageAsync("You don't have permission to use this command.");
                return;
            }

            // If no arguments, show the list of items
            if (args.Length == 1)
            {
                await ShowItems(message, 1, client);
                return;
            }

            // Handle subcommands
            string subCommand = args[1].ToLower();

            switch (subCommand)
            {
                case "add":
                    await AddItem(message, args, client);
                    break;

                case "edit":
                    await EditItem(message, args, client);
                    break;

                case "remove":
                    await RemoveItem(message, args, client);
                    break;

                case "page":
                    // Check if a page number was provided
                    if (args.Length < 3 || !int.TryParse(args[2], out int pageNumber))
                    {
                        await message.Channel.SendMessageAsync("Please specify a valid page number. Example: `%edititems page 2`");
                        return;
                    }

                    // Show the specified page
                    await ShowItems(message, pageNumber, client);
                    break;

                default:
                    await message.Channel.SendMessageAsync("Unknown subcommand. Available subcommands: `add`, `edit`, `remove`, `page`");
                    break;
            }
        }

        private async Task ShowItems(SocketMessage message, int page, DiscordBotClient client)
        {
            // Get all items
            var allItems = client.BotSystem.Config.AvailableConsumables;

            // Calculate total pages
            int totalPages = (int)Math.Ceiling(allItems.Count / (double)ItemsPerPage);
            if (totalPages == 0) totalPages = 1; // At least one page even if empty

            // Ensure page is within valid range
            page = Math.Max(1, Math.Min(page, totalPages));

            // Get items for the current page
            var pageItems = allItems
                .Skip((page - 1) * ItemsPerPage)
                .Take(ItemsPerPage)
                .ToList();

            // Build the items list
            var sb = new StringBuilder();

            // Add the header
            sb.AppendLine("%edititems add [id] [category] [name] [description] [multiplier] [botreward] [succ%] [win%] [lose%] [cost] [uses] [emoji] [visible]");
            sb.AppendLine("%edititems edit [id] [category] [name] [description] [multiplier] [botreward] [succ%] [win%] [lose%] [cost] [uses] [emoji] [visible]");
            sb.AppendLine("%edititems remove [id]");

            // Add navigation instructions if there are multiple pages
            if (totalPages > 1)
            {
                sb.AppendLine($"%edititems page [1-{totalPages}] to navigate pages");
            }

            sb.AppendLine("Here are all the available items:");
            sb.AppendLine("════════════════════════════════");

            // Add the items
            for (int i = 0; i < pageItems.Count; i++)
            {
                var item = pageItems[i];
                int displayId = (page - 1) * ItemsPerPage + i + 1; // Calculate the display ID
                string formattedPrice = FormatNumber(item.Cost);
                string itemType = item.Category == 2 ? "Bot Pack" : item.Category == 3 ? "Bomb" : "Multiplier";
                string visibility = item.IsVisibleInShop ? "Visible" : "Hidden";

                // Format: ID emoji name----------------------- price :BotDown: (Type) [Visibility]
                // Calculate dashes needed for alignment
                int dashCount = Math.Max(1, 30 - item.Name.Length - formattedPrice.Length);
                string dashes = new string('-', dashCount);

                sb.AppendLine($"{displayId} {item.Emoji} {item.Name}{dashes} {formattedPrice} {Constants.Emojis.BotDown} ({itemType}) [{visibility}]");
                sb.AppendLine($"   ID: {item.Id}");
            }

            // Add a blank line at the end for spacing
            sb.AppendLine();

            // Create an embed for the items list
            var embed = new EmbedBuilder()
                .WithAuthor("Bot-Chan's Secret Research Facility", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                .WithDescription(sb.ToString())
                .WithColor(Color.Purple)
                .WithFooter($"Page {page}/{totalPages} |");

            // Send the embed message
            var itemsMessage = await message.Channel.SendMessageAsync(embed: embed.Build());
        }

        private async Task AddItem(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Get the full command text
            string fullCommand = message.Content;

            // Parse the command using regex to handle quoted strings properly
            var matches = Regex.Matches(fullCommand, @"[\""].+?[\""]|[^ ]+");
            var parsedArgs = new List<string>();

            foreach (Match match in matches)
            {
                string arg = match.Value;
                // Remove quotes from quoted arguments
                if (arg.StartsWith("\"") && arg.EndsWith("\""))
                {
                    arg = arg.Substring(1, arg.Length - 2);
                }
                parsedArgs.Add(arg);
            }

            // Check if we have enough arguments (extended syntax with bomb parameters)
            if (parsedArgs.Count < 14)
            {
                await message.Channel.SendMessageAsync("**Not enough arguments.** Use `%help edititems` for syntax.\n\n" +
                    "**Quick reference:**\n" +
                    "• **Multipliers:** `add [id] 1 [name] [desc] [mult] 0 0 0 0 [cost] [uses] [emoji] [vis]`\n" +
                    "• **Bot Packs:** `add [id] 2 [name] [desc] 1 [bots] 0 0 0 [cost] [uses] [emoji] [vis]`\n" +
                    "• **Bombs:** `add [id] 3 [name] [desc] 1 0 [succ%] [win%] [lose%] [cost] [uses] [emoji] [vis]`");
                return;
            }

            // Parse the arguments
            string id = parsedArgs[2];

            // Parse category
            if (!int.TryParse(parsedArgs[3], out int category) || category < 1 || category > 3)
            {
                await message.Channel.SendMessageAsync($"Invalid category value: '{parsedArgs[3]}'. Must be 1 (Multipliers), 2 (Bot Packs), or 3 (Bombs).");
                return;
            }

            string name = parsedArgs[4];
            string description = parsedArgs[5];

            // Parse the numeric values
            if (!double.TryParse(parsedArgs[6], out double multiplier))
            {
                await message.Channel.SendMessageAsync($"Invalid multiplier value: '{parsedArgs[6]}'. Must be a number.");
                return;
            }

            if (!int.TryParse(parsedArgs[7], out int botReward))
            {
                await message.Channel.SendMessageAsync($"Invalid bot reward value: '{parsedArgs[7]}'. Must be a number.");
                return;
            }

            // Parse bomb-specific parameters
            if (!int.TryParse(parsedArgs[8], out int bombSuccessRate))
            {
                await message.Channel.SendMessageAsync($"Invalid bomb success rate: '{parsedArgs[8]}'. Must be a number (0-100).");
                return;
            }

            if (!int.TryParse(parsedArgs[9], out int bombWinPercentage))
            {
                await message.Channel.SendMessageAsync($"Invalid bomb win percentage: '{parsedArgs[9]}'. Must be a number (0-100).");
                return;
            }

            if (!int.TryParse(parsedArgs[10], out int bombLosePercentage))
            {
                await message.Channel.SendMessageAsync($"Invalid bomb lose percentage: '{parsedArgs[10]}'. Must be a number (0-100).");
                return;
            }

            if (!int.TryParse(parsedArgs[11], out int cost))
            {
                await message.Channel.SendMessageAsync($"Invalid cost value: '{parsedArgs[11]}'. Must be a number.");
                return;
            }

            if (!int.TryParse(parsedArgs[12], out int uses))
            {
                await message.Channel.SendMessageAsync($"Invalid uses value: '{parsedArgs[12]}'. Must be a number.");
                return;
            }

            // Get the emoji
            string emoji = parsedArgs.Count > 13 ? parsedArgs[13] : "🔮";

            // Get visibility (default to true if not specified)
            bool isVisible = true;
            if (parsedArgs.Count > 14)
            {
                if (!bool.TryParse(parsedArgs[14], out isVisible))
                {
                    // Try to parse "yes"/"no" or "true"/"false"
                    string visibilityStr = parsedArgs[14].ToLower();
                    isVisible = visibilityStr == "yes" || visibilityStr == "true" || visibilityStr == "1";
                }
            }

            // Validate category-specific restrictions and bomb parameters
            string validationError = ValidateCategoryRestrictions(category, multiplier, botReward, bombSuccessRate, bombWinPercentage, bombLosePercentage);
            if (!string.IsNullOrEmpty(validationError))
            {
                await message.Channel.SendMessageAsync(validationError);
                return;
            }

            // Create the item
            var item = new Consumable
            {
                Id = id,
                Name = name,
                Description = description,
                Multiplier = multiplier,
                BotReward = botReward,
                Cost = cost,
                DefaultUses = uses,
                Emoji = emoji,
                IsVisibleInShop = isVisible,
                Category = category,
                // Use parsed bomb values
                BombSuccessRate = bombSuccessRate,
                BombWinPercentage = bombWinPercentage,
                BombLosePercentage = bombLosePercentage,
                // Legacy fields for backward compatibility
                BombMinDamage = 0,
                BombMaxDamage = 0,
                BombFailurePenalty = 0
            };

            // Add the item
            bool success = client.BotSystem.AddConsumable(item);

            if (success)
            {
                // Create an embed for the response
                string categoryName = category == 1 ? "Multiplier" : category == 2 ? "Bot Pack" : "Bomb";
                string itemDetails = $"• ID: `{id}`\n" +
                                   $"• Category: **{categoryName}**\n" +
                                   $"• Description: {description}\n";

                if (category == 1) // Multiplier
                {
                    itemDetails += $"• Multiplier: **{multiplier}x**\n";
                }
                else if (category == 2) // Bot Pack
                {
                    itemDetails += $"• Bot Reward: **{botReward}** bots\n";
                }
                else if (category == 3) // Bomb
                {
                    itemDetails += $"• Success Rate: **{bombSuccessRate}%**\n" +
                                  $"• Enemy Loss: **{bombWinPercentage}%** (on success)\n" +
                                  $"• Your Loss: **{bombLosePercentage}%** (on failure)\n";
                }

                itemDetails += $"• Uses: **{uses}**\n" +
                              $"• Price: **{cost}** {Constants.Emojis.BotDown}\n" +
                              $"• Visible in Shop: **{(isVisible ? "Yes" : "No")}**";

                var embed = new EmbedBuilder()
                    .WithAuthor("Bot-Chan's Secret Research Facility", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                    .WithTitle("Item Added")
                    .WithDescription($"**{name}** {emoji} has been added!\n\n{itemDetails}")
                    .WithColor(Color.Green)
                    .WithFooter("Use %shop to see all available items");

                await message.Channel.SendMessageAsync(embed: embed.Build());
            }
            else
            {
                await message.Channel.SendMessageAsync($"Error: An item with ID `{id}` already exists.");
            }
        }

        private async Task EditItem(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Get the full command text
            string fullCommand = message.Content;

            // Parse the command using regex to handle quoted strings properly
            var matches = Regex.Matches(fullCommand, @"[\""].+?[\""]|[^ ]+");
            var parsedArgs = new List<string>();

            foreach (Match match in matches)
            {
                string arg = match.Value;
                // Remove quotes from quoted arguments
                if (arg.StartsWith("\"") && arg.EndsWith("\""))
                {
                    arg = arg.Substring(1, arg.Length - 2);
                }
                parsedArgs.Add(arg);
            }

            // Check if we have enough arguments (extended syntax with bomb parameters)
            if (parsedArgs.Count < 14)
            {
                await message.Channel.SendMessageAsync("**Not enough arguments.** Use `%help edititems` for syntax.\n\n" +
                    "**Quick reference:**\n" +
                    "• **Multipliers:** `edit [id] 1 [name] [desc] [mult] 0 0 0 0 [cost] [uses] [emoji] [vis]`\n" +
                    "• **Bot Packs:** `edit [id] 2 [name] [desc] 1 [bots] 0 0 0 [cost] [uses] [emoji] [vis]`\n" +
                    "• **Bombs:** `edit [id] 3 [name] [desc] 1 0 [succ%] [win%] [lose%] [cost] [uses] [emoji] [vis]`");
                return;
            }

            // Parse the arguments
            string id = parsedArgs[2];

            // Parse category
            if (!int.TryParse(parsedArgs[3], out int category) || category < 1 || category > 3)
            {
                await message.Channel.SendMessageAsync($"Invalid category value: '{parsedArgs[3]}'. Must be 1 (Multipliers), 2 (Bot Packs), or 3 (Bombs).");
                return;
            }

            string name = parsedArgs[4];
            string description = parsedArgs[5];

            // Parse the numeric values
            if (!double.TryParse(parsedArgs[6], out double multiplier))
            {
                await message.Channel.SendMessageAsync($"Invalid multiplier value: '{parsedArgs[6]}'. Must be a number.");
                return;
            }

            if (!int.TryParse(parsedArgs[7], out int botReward))
            {
                await message.Channel.SendMessageAsync($"Invalid bot reward value: '{parsedArgs[7]}'. Must be a number.");
                return;
            }

            // Parse bomb-specific parameters
            if (!int.TryParse(parsedArgs[8], out int bombSuccessRate))
            {
                await message.Channel.SendMessageAsync($"Invalid bomb success rate: '{parsedArgs[8]}'. Must be a number (0-100).");
                return;
            }

            if (!int.TryParse(parsedArgs[9], out int bombWinPercentage))
            {
                await message.Channel.SendMessageAsync($"Invalid bomb win percentage: '{parsedArgs[9]}'. Must be a number (0-100).");
                return;
            }

            if (!int.TryParse(parsedArgs[10], out int bombLosePercentage))
            {
                await message.Channel.SendMessageAsync($"Invalid bomb lose percentage: '{parsedArgs[10]}'. Must be a number (0-100).");
                return;
            }

            if (!int.TryParse(parsedArgs[11], out int cost))
            {
                await message.Channel.SendMessageAsync($"Invalid cost value: '{parsedArgs[11]}'. Must be a number.");
                return;
            }

            if (!int.TryParse(parsedArgs[12], out int uses))
            {
                await message.Channel.SendMessageAsync($"Invalid uses value: '{parsedArgs[12]}'. Must be a number.");
                return;
            }

            // Get the emoji
            string emoji = parsedArgs.Count > 13 ? parsedArgs[13] : "🔮";

            // Get visibility (default to true if not specified)
            bool isVisible = true;
            if (parsedArgs.Count > 14)
            {
                if (!bool.TryParse(parsedArgs[14], out isVisible))
                {
                    // Try to parse "yes"/"no" or "true"/"false"
                    string visibilityStr = parsedArgs[14].ToLower();
                    isVisible = visibilityStr == "yes" || visibilityStr == "true" || visibilityStr == "1";
                }
            }

            // Validate category-specific restrictions and bomb parameters
            string validationError = ValidateCategoryRestrictions(category, multiplier, botReward, bombSuccessRate, bombWinPercentage, bombLosePercentage);
            if (!string.IsNullOrEmpty(validationError))
            {
                await message.Channel.SendMessageAsync(validationError);
                return;
            }

            // Get the existing item to preserve any fields we're not updating
            var existingItem = client.BotSystem.GetConsumable(id);
            if (existingItem == null)
            {
                await message.Channel.SendMessageAsync($"Error: No item with ID `{id}` found.");
                return;
            }

            // Create the updated item
            var item = new Consumable
            {
                Id = id,
                Name = name,
                Description = description,
                Multiplier = multiplier,
                BotReward = botReward,
                Cost = cost,
                DefaultUses = uses,
                Emoji = emoji,
                IsVisibleInShop = isVisible,
                Category = category,
                // Use parsed bomb values
                BombSuccessRate = bombSuccessRate,
                BombWinPercentage = bombWinPercentage,
                BombLosePercentage = bombLosePercentage,
                // Legacy fields for backward compatibility
                BombMinDamage = 0,
                BombMaxDamage = 0,
                BombFailurePenalty = 0
            };

            // Update the item
            bool success = client.BotSystem.UpdateConsumable(item);

            if (success)
            {
                // Create an embed for the response
                string categoryName = category == 1 ? "Multiplier" : category == 2 ? "Bot Pack" : "Bomb";
                string itemDetails = $"• ID: `{id}`\n" +
                                   $"• Category: **{categoryName}**\n" +
                                   $"• Description: {description}\n";

                if (category == 1) // Multiplier
                {
                    itemDetails += $"• Multiplier: **{multiplier}x**\n";
                }
                else if (category == 2) // Bot Pack
                {
                    itemDetails += $"• Bot Reward: **{botReward}** bots\n";
                }
                else if (category == 3) // Bomb
                {
                    itemDetails += $"• Success Rate: **{bombSuccessRate}%**\n" +
                                  $"• Enemy Loss: **{bombWinPercentage}%** (on success)\n" +
                                  $"• Your Loss: **{bombLosePercentage}%** (on failure)\n";
                }

                itemDetails += $"• Uses: **{uses}**\n" +
                              $"• Price: **{cost}** {Constants.Emojis.BotDown}\n" +
                              $"• Visible in Shop: **{(isVisible ? "Yes" : "No")}**";

                var embed = new EmbedBuilder()
                    .WithAuthor("Bot-Chan's Secret Research Facility", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                    .WithTitle("Item Updated")
                    .WithDescription($"**{name}** {emoji} has been updated!\n\n{itemDetails}")
                    .WithColor(Color.Blue)
                    .WithFooter("Use %shop to see all available items");

                await message.Channel.SendMessageAsync(embed: embed.Build());
            }
            else
            {
                await message.Channel.SendMessageAsync($"Error: Failed to update item with ID `{id}`.");
            }
        }

        private async Task RemoveItem(SocketMessage message, string[] args, DiscordBotClient client)
        {
            // Check if we have enough arguments
            if (args.Length < 3)
            {
                await message.Channel.SendMessageAsync("Not enough arguments. Usage: `%edititems remove [id]`");
                return;
            }

            // Parse the arguments
            string id = args[2];

            // Get the item before removing it
            var item = client.BotSystem.GetConsumable(id);
            if (item == null)
            {
                await message.Channel.SendMessageAsync($"Error: No item with ID `{id}` found.");
                return;
            }

            // Remove the item
            bool success = client.BotSystem.RemoveConsumable(id);

            if (success)
            {
                // Create an embed for the response
                var embed = new EmbedBuilder()
                    .WithAuthor("Bot-Chan's Secret Research Facility", message.Author.GetAvatarUrl() ?? message.Author.GetDefaultAvatarUrl())
                    .WithTitle("Item Removed")
                    .WithDescription($"**{item.Name}** {item.Emoji} has been removed.")
                    .WithColor(Color.Red)
                    .WithFooter("Use %shop to see all available items");

                await message.Channel.SendMessageAsync(embed: embed.Build());
            }
            else
            {
                await message.Channel.SendMessageAsync($"Error: Failed to remove item with ID `{id}`.");
            }
        }

        /// <summary>
        /// Formats a number with K, M, B suffixes for thousands, millions, billions
        /// </summary>
        private string FormatNumber(int number)
        {
            if (number >= 1_000_000_000)
                return $"{number / 1_000_000_000}B";
            if (number >= 1_000_000)
                return $"{number / 1_000_000}M";
            if (number >= 1_000)
                return $"{number / 1_000}K";
            return number.ToString();
        }

        /// <summary>
        /// Validates category-specific restrictions for items
        /// </summary>
        private string ValidateCategoryRestrictions(int category, double multiplier, int botReward, int bombSuccessRate = 0, int bombWinPercentage = 0, int bombLosePercentage = 0)
        {
            switch (category)
            {
                case 1: // Bot recruitment multipliers
                    if (botReward > 0)
                    {
                        return "**Error:** Bot recruitment multipliers (category 1) cannot have bot rewards. Set bot reward to 0.";
                    }
                    if (multiplier <= 1.0)
                    {
                        return "**Error:** Bot recruitment multipliers (category 1) must have a multiplier greater than 1.0.";
                    }
                    if (bombSuccessRate != 0 || bombWinPercentage != 0 || bombLosePercentage != 0)
                    {
                        return "**Error:** Bot recruitment multipliers (category 1) must have all bomb properties set to 0.";
                    }
                    break;

                case 2: // Bot packs (instant bot rewards)
                    if (botReward <= 0)
                    {
                        return "**Error:** Bot packs (category 2) must have a bot reward greater than 0.";
                    }
                    if (multiplier != 1.0)
                    {
                        return "**Error:** Bot packs (category 2) should have a multiplier of 1.0 (multiplier field is not used).";
                    }
                    if (bombSuccessRate != 0 || bombWinPercentage != 0 || bombLosePercentage != 0)
                    {
                        return "**Error:** Bot packs (category 2) must have all bomb properties set to 0.";
                    }
                    break;

                case 3: // Bombs (attack items)
                    if (botReward > 0)
                    {
                        return "**Error:** Bombs (category 3) cannot have bot rewards. Set bot reward to 0.";
                    }
                    if (multiplier != 1.0)
                    {
                        return "**Error:** Bombs (category 3) should have a multiplier of 1.0 (multiplier field is not used).";
                    }
                    if (bombSuccessRate < 0 || bombSuccessRate > 100)
                    {
                        return "**Error:** Bomb success rate must be between 0-100%.";
                    }
                    if (bombWinPercentage < 0 || bombWinPercentage > 100)
                    {
                        return "**Error:** Bomb win percentage (enemy loss) must be between 0-100%.";
                    }
                    if (bombLosePercentage < 0 || bombLosePercentage > 100)
                    {
                        return "**Error:** Bomb lose percentage (your loss) must be between 0-100%.";
                    }
                    break;

                default:
                    return "**Error:** Invalid category. Must be 1 (Multipliers), 2 (Bot Packs), or 3 (Bombs).";
            }

            return string.Empty; // No validation errors
        }
    }
}
